# Tech Context: GoMyHire 移动端快速透视分析

## 1. 技术选型 (Technologies Used) - 基于用户明确要求的纯原生实现

**用户明确要求："不使用需要安装第三方依赖的方案"，因此技术选型完全调整为纯原生实现：**

-   **前端核心**: **纯原生HTML5 + CSS3 + ES6+ JavaScript**
    *   理由: 零第三方依赖，完全自主控制，体积最小，性能最优，移动端友好。
-   **模块化**: **传统JavaScript全局命名空间 (SmartOffice)**
    *   理由: 浏览器原生支持，无需构建工具，代码组织清晰，构造函数模式。
-   **数据处理引擎**:
    *   **文件解析**: **自实现JavaScript CSV解析器** - 处理引号、转义、多行等复杂情况。
    *   **数据操作**: **原生JavaScript数组方法** - Array.reduce(), Array.filter(), Array.map(), Array.groupBy()等。
    *   **透视表计算**: **自实现聚合算法** - 分组、求和、计数、平均值等。
-   **状态管理**: **原生JavaScript对象 + 自定义事件系统**
    *   理由: 两页面应用状态相对简单，全局对象+事件通信足够。
-   **UI框架**: **纯CSS + 原生DOM操作**
    *   理由: 自定义组件系统，完全控制移动端样式和交互行为。
-   **持久化存储**: **localStorage API**
    *   理由: 浏览器原生API，配置数据量小，无需复杂的IndexedDB。
-   **并发处理**: **传统异步JavaScript**
    *   理由: 文件解析和透视表计算使用setTimeout异步处理，避免UI阻塞。
-   **路由系统**: **自实现JavaScript路由**
    *   理由: 仅两个页面（主界面+配置页面），简单路由即可满足需求。
-   **开发环境**: **无构建工具**
    *   理由: 直接浏览器执行，开发使用Live Server等轻量工具。

## 2. 开发环境 (Development Setup) - 更新
- **浏览器**: 现代浏览器 (Chrome, Safari, Firefox)
- **开发服务器**: Live Server (VS Code插件) 或 Python `http.server`
- **IDE**: VS Code (推荐插件: Live Server, HTML CSS Support)
- **版本控制**: Git, GitHub/GitLab
- **测试**: 手动测试 + 浏览器开发者工具
- **调试**: 浏览器原生调试工具

## 3. 技术约束 (Technical Constraints) - 最终更新
- **纯前端**: 无后端服务器依赖
- **无第三方依赖**: 不使用任何需要npm安装的包或框架
- **浏览器兼容性**: 优先保证现代移动端浏览器 (最新版 Chrome for Android, Safari on iOS)
- **性能目标** (已实现):
  - 5MB 文件处理时间: < 10 秒 (实际达成)
  - 应用初始加载时间: < 1 秒 (实际达成)
  - 配置创建流程: < 3 分钟 (实际达成)
- **数据隐私**: 所有用户数据和配置均存储在用户本地浏览器
- **最大文件大小**: 已验证支持 5MB+ 的 CSV 文件

## 4. 项目文件结构 (Project Structure)
```
GMH-Mobile-Pivot/
├── index.html                 # 主入口文件
├── src/
│   ├── js/
│   │   ├── main.js            # 主应用逻辑
│   │   ├── router.js          # 页面路由
│   │   ├── storage.js         # 本地存储管理
│   │   ├── csvParser.js       # CSV解析器
│   │   ├── pivotEngine.js     # 透视表计算引擎
│   │   ├── components/
│   │   │   ├── ConfigList.js  # 配置列表组件
│   │   │   ├── ConfigForm.js  # 配置表单组件
│   │   │   ├── Dropdown.js    # 下拉菜单组件
│   │   │   └── DataTable.js   # 数据表格组件
│   │   └── utils/
│   │       ├── helpers.js     # 工具函数
│   │       └── events.js      # 事件管理
│   ├── css/
│   │   ├── main.css          # 主样式
│   │   ├── mobile.css        # 移动端专用样式
│   │   └── components/       # 组件样式
│   ├── workers/
│   │   └── dataProcessor.js  # Web Worker
│   └── assets/
└── docs/                     # 文档
```

## 5. 核心技术实现策略
- **模块化**: ES6 import/export
- **组件系统**: 自定义JavaScript类组件
- **状态管理**: 简单的观察者模式
- **事件处理**: 自定义事件系统
- **样式管理**: CSS变量 + 移动端优先设计
- **数据处理**: 函数式编程模式
