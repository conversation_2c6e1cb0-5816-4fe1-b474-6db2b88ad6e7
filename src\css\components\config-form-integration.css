/**
 * @file 配置表单集成功能样式
 * @description 文件上传、数据预览、字段选择器集成的iOS风格样式
 * <AUTHOR> Team
 */

/* 数据源区域样式 */
.file-upload-container {
    margin: 0;
    padding: 0;
}

/* 数据预览区域样式 */
.data-preview-section {
    margin-top: 20px;
    border-radius: 10px;
    background: var(--ios-card-background, #FFFFFF);
    overflow: hidden;
}

.data-preview-section .ios-form-header.collapsible {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    cursor: pointer;
    user-select: none;
    -webkit-user-select: none;
    border-bottom: 1px solid var(--ios-separator, #C6C6C8);
}

.preview-toggle-button {
    background: none;
    border: none;
    color: var(--ios-primary, #007AFF);
    font-size: 16px;
    font-weight: 400;
    padding: 4px 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.preview-toggle-button:hover {
    background-color: rgba(0, 122, 255, 0.1);
}

.preview-toggle-button:active {
    background-color: rgba(0, 122, 255, 0.2);
    transform: scale(0.98);
}

/* 数据预览内容样式 */
.data-preview-content {
    padding: 0;
}

.data-info {
    padding: 16px 20px;
    background: var(--ios-background, #F2F2F7);
    border-bottom: 1px solid var(--ios-separator, #C6C6C8);
}

.data-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.data-info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.data-info-label {
    font-size: 13px;
    color: var(--ios-text-secondary, #8E8E93);
    font-weight: 400;
}

.data-info-value {
    font-size: 16px;
    color: var(--ios-text-primary, #000000);
    font-weight: 500;
}

/* 数据表格样式 */
.data-table-container {
    padding: 16px 20px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.data-table-wrapper {
    min-width: 100%;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--ios-separator, #C6C6C8);
}

.data-preview-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    background: white;
}

.data-preview-table th {
    background: var(--ios-background, #F2F2F7);
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid var(--ios-separator, #C6C6C8);
    font-weight: 600;
    color: var(--ios-text-primary, #000000);
    position: sticky;
    top: 0;
    z-index: 1;
}

.data-preview-table th .field-name {
    display: block;
    margin-bottom: 4px;
    font-size: 14px;
}

.data-preview-table th .field-type-tag {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.field-type-number .field-type-tag {
    background: #E3F2FD;
    color: #1976D2;
}

.field-type-date .field-type-tag {
    background: #F3E5F5;
    color: #7B1FA2;
}

.field-type-category .field-type-tag {
    background: #E8F5E8;
    color: #388E3C;
}

.field-type-text .field-type-tag {
    background: #FFF3E0;
    color: #F57C00;
}

.data-preview-table td {
    padding: 10px 8px;
    border-bottom: 1px solid var(--ios-separator, #C6C6C8);
    color: var(--ios-text-primary, #000000);
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.data-preview-table tr:last-child td {
    border-bottom: none;
}

.data-preview-table tr:nth-child(even) {
    background: rgba(0, 0, 0, 0.02);
}

.data-table-more {
    padding: 12px 16px;
    text-align: center;
    color: var(--ios-text-secondary, #8E8E93);
    font-size: 13px;
    background: var(--ios-background, #F2F2F7);
    border-top: 1px solid var(--ios-separator, #C6C6C8);
}

/* 字段选择器全局容器样式 */
.field-selector-global-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    pointer-events: none;
}

.field-selector-global-container > * {
    pointer-events: auto;
}

/* 字段选择器背景遮罩样式 */
.so-field-selector-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.so-field-selector-backdrop.so-backdrop-visible {
    opacity: 1;
}

/* 字段选择器模态框样式 */
.so-field-selector-modal {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-radius: 16px 16px 0 0;
    max-height: 80vh;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    z-index: 1001;
    display: flex;
    flex-direction: column;
}

.so-field-selector-modal.so-modal-visible {
    transform: translateY(0);
}

/* 字段选择器头部样式 */
.so-field-selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--ios-separator, #C6C6C8);
    background: white;
    border-radius: 16px 16px 0 0;
}

.so-header-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--ios-text-primary, #000000);
    margin: 0;
}

.so-header-button {
    background: none;
    border: none;
    font-size: 16px;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.so-header-button.cancel {
    color: var(--ios-text-secondary, #8E8E93);
}

.so-header-button.confirm {
    color: var(--ios-primary, #007AFF);
    font-weight: 600;
}

.so-header-button:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.so-header-button:active {
    background-color: rgba(0, 0, 0, 0.1);
    transform: scale(0.98);
}

/* 字段选择器内容样式 */
.so-field-selector-content {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    /* 确保滚动区域有明确的高度限制 */
    max-height: calc(80vh - 120px); /* 减去头部和底部的高度 */
    min-height: 200px;
}

.so-field-list {
    padding: 0;
    /* 确保列表内容可以正常滚动 */
    min-height: min-content;
}

.so-field-item {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--ios-separator, #C6C6C8);
    cursor: pointer;
    transition: background-color 0.2s ease;
    user-select: none;
    -webkit-user-select: none;
}

.so-field-item:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.so-field-item:active {
    background-color: rgba(0, 0, 0, 0.05);
}

.so-field-item.selected {
    background-color: rgba(0, 122, 255, 0.1);
}

.so-field-content {
    flex: 1;
    margin-right: 12px;
}

.so-field-main {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.so-field-name {
    font-size: 16px;
    font-weight: 500;
    color: var(--ios-text-primary, #000000);
}

.so-field-type {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.so-field-type-number {
    background: #E3F2FD;
    color: #1976D2;
}

.so-field-type-date {
    background: #F3E5F5;
    color: #7B1FA2;
}

.so-field-type-category {
    background: #E8F5E8;
    color: #388E3C;
}

.so-field-type-text {
    background: #FFF3E0;
    color: #F57C00;
}

.so-field-description {
    font-size: 13px;
    color: var(--ios-text-secondary, #8E8E93);
    margin-top: 2px;
}

/* 字段选择器复选框和单选框样式 */
.so-field-selector {
    display: flex;
    align-items: center;
}

.so-field-checkbox,
.so-field-radio {
    width: 24px;
    height: 24px;
    border: 2px solid var(--ios-separator, #C6C6C8);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.so-field-checkbox {
    border-radius: 6px;
}

.so-field-radio {
    border-radius: 50%;
}

.so-field-checkbox.checked,
.so-field-radio.checked {
    background: var(--ios-primary, #007AFF);
    border-color: var(--ios-primary, #007AFF);
}

.so-field-checkbox .checkbox-icon {
    width: 14px;
    height: 14px;
    fill: white;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.so-field-checkbox.checked .checkbox-icon {
    opacity: 1;
}

.so-radio-inner {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: white;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.so-field-radio.checked .so-radio-inner {
    opacity: 1;
}

/* 字段选择器底部信息样式 */
.so-field-selector-footer {
    padding: 16px 20px;
    border-top: 1px solid var(--ios-separator, #C6C6C8);
    background: var(--ios-background, #F2F2F7);
}

.so-selection-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
    color: var(--ios-text-secondary, #8E8E93);
}

.so-max-info {
    font-size: 12px;
}

/* 空状态样式 */
.so-field-empty {
    padding: 40px 20px;
    text-align: center;
    color: var(--ios-text-secondary, #8E8E93);
    font-size: 16px;
}

/* 防止背景滚动 */
body.so-modal-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .data-info-grid {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .data-preview-table th,
    .data-preview-table td {
        padding: 8px 6px;
        font-size: 13px;
    }
    
    .data-preview-table th .field-name {
        font-size: 13px;
    }
    
    .data-preview-table th .field-type-tag {
        font-size: 10px;
    }
    
    .so-field-selector-modal {
        max-height: 85vh;
    }
}

/* 时间区间按钮样式 */
.so-time-range-btn {
    background: var(--ios-blue);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 4px 8px;
    font-size: 12px;
    margin-left: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.so-time-range-btn:hover {
    background: var(--ios-blue-dark);
    transform: scale(1.05);
}

.so-time-range-btn:active {
    transform: scale(0.95);
}

/* 时间区间模态框样式 */
.time-range-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.time-range-modal.show {
    opacity: 1;
    visibility: visible;
}

.time-range-modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.time-range-modal.show .time-range-modal-content {
    transform: scale(1);
}

.time-range-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--ios-separator);
    background: var(--ios-background-secondary);
}

.time-range-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--ios-text-primary);
}

.time-range-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--ios-text-secondary);
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;
    transition: background-color 0.2s ease;
}

.time-range-modal-close:hover {
    background: var(--ios-background-tertiary);
}

.time-range-modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.time-range-modal-footer {
    display: flex;
    gap: 12px;
    padding: 16px 20px;
    border-top: 1px solid var(--ios-separator);
    background: var(--ios-background-secondary);
}

.time-range-modal-footer .ios-button {
    flex: 1;
}

/* 时间区间预设样式 */
.time-range-presets {
    margin-bottom: 24px;
}

.time-range-presets h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--ios-text-primary);
}

.time-range-presets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 8px;
}

.time-range-preset-btn {
    background: var(--ios-background-secondary);
    border: 1px solid var(--ios-separator);
    border-radius: 8px;
    padding: 12px 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    color: var(--ios-text-primary);
}

.time-range-preset-btn:hover {
    background: var(--ios-blue);
    color: white;
    border-color: var(--ios-blue);
}

.time-range-preset-btn small {
    display: block;
    font-size: 12px;
    opacity: 0.7;
    margin-top: 4px;
}

/* 自定义时间区间样式 */
.time-range-custom {
    margin-bottom: 24px;
}

.time-range-custom h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--ios-text-primary);
}

.time-range-form {
    background: var(--ios-background-secondary);
    border-radius: 8px;
    padding: 16px;
}

.time-range-form-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.time-range-form-row:last-child {
    margin-bottom: 0;
}

.time-range-form-row label {
    width: 80px;
    font-size: 14px;
    color: var(--ios-text-primary);
    margin-right: 12px;
}

.time-range-form-row input {
    flex: 1;
}

/* 时间区间列表样式 */
.time-ranges-list h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--ios-text-primary);
}

.time-ranges-items {
    background: var(--ios-background-secondary);
    border-radius: 8px;
    overflow: hidden;
}

.time-ranges-empty {
    padding: 20px;
    text-align: center;
    color: var(--ios-text-secondary);
    font-size: 14px;
}

.time-range-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid var(--ios-separator);
}

.time-range-item:last-child {
    border-bottom: none;
}

.time-range-info {
    flex: 1;
}

.time-range-name {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--ios-text-primary);
    margin-bottom: 2px;
}

.time-range-time {
    display: block;
    font-size: 12px;
    color: var(--ios-text-secondary);
}

.time-range-remove {
    background: var(--ios-red);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.2s ease;
}

.time-range-remove:hover {
    background: var(--ios-red-dark);
    transform: scale(1.1);
}

.time-range-remove:active {
    transform: scale(0.9);
}
