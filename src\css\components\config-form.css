/**
 * @file 配置表单组件样式
 * @description iOS风格的透视表配置表单界面
 * <AUTHOR> Team
 */

/* 配置表单容器 */
.config-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    padding: 0;
}

/* 表单区域 */
.form-section {
    margin-bottom: var(--spacing-lg);
}

.form-section:last-child {
    margin-bottom: 0;
}

/* 表单标题区域 */
.form-title-section {
    margin-bottom: var(--spacing-xl);
}

.form-title-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

.form-title {
    font-size: var(--font-size-title2);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    flex: 1;
    text-align: center;
}

/* 返回主页按钮 */
.back-to-home-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    background: none;
    border: none;
    border-radius: var(--radius-medium);
    cursor: pointer;
    transition: all var(--animation-fast) ease;
    -webkit-tap-highlight-color: transparent;
    color: var(--ios-blue);
    position: relative;
    z-index: 1;
}

.back-to-home-button:hover {
    background-color: var(--background-tertiary);
}

.back-to-home-button:active {
    transform: scale(0.95);
    background-color: var(--text-quaternary);
}

.back-icon {
    width: 24px;
    height: 24px;
    fill: currentColor;
    transition: transform var(--animation-fast) ease;
}

.back-to-home-button:active .back-icon {
    transform: scale(0.9);
}

/* 标题占位元素 */
.title-spacer {
    width: 44px;
    height: 44px;
}

/* iOS风格表单组 */
.ios-form-header {
    padding: var(--spacing-sm) var(--spacing-md);
    margin-bottom: var(--spacing-xs);
}

.ios-form-title {
    font-size: var(--font-size-footnote);
    color: var(--text-secondary);
    text-transform: uppercase;
    font-weight: 400;
    letter-spacing: 0.5px;
    margin: 0;
}

.ios-form-group {
    background-color: var(--background-secondary);
    border-radius: var(--radius-medium);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

/* iOS风格表单行 */
.ios-form-row {
    display: flex;
    align-items: flex-start;
    padding: var(--spacing-md);
    border-bottom: 0.5px solid var(--separator-opaque);
    min-height: 44px;
}

.ios-form-row:last-child {
    border-bottom: none;
}

.ios-form-label {
    font-size: var(--font-size-body);
    color: var(--text-primary);
    margin-right: var(--spacing-md);
    min-width: 80px;
    flex-shrink: 0;
    padding-top: 2px;
    font-weight: 400;
}

/* iOS风格输入框 */
.ios-form-input {
    flex: 1;
    border: none;
    background: none;
    font-size: var(--font-size-body);
    color: var(--text-primary);
    padding: 0;
    outline: none;
    -webkit-appearance: none;
    font-family: inherit;
    resize: none;
}

.ios-form-input::placeholder {
    color: var(--text-tertiary);
}

.ios-form-input:focus {
    outline: none;
}

/* 文本域特殊样式 */
textarea.ios-form-input {
    min-height: 60px;
    line-height: 1.4;
    padding-top: 2px;
}

/* 选择框特殊样式 */
select.ios-form-input {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='%23999' d='M4.427 9.573L8 13.146l3.573-3.573a.5.5 0 0.708.708l-4 4a.5.5 0 01-.708 0l-4-4a.5.5 0 11.708-.708z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
    padding-right: 32px;
    cursor: pointer;
}

/* 字段选择器行 */
.field-selector-row {
    flex-direction: column;
    align-items: stretch;
    padding: var(--spacing-md);
}

.field-selector-row .ios-form-label {
    margin-bottom: var(--spacing-sm);
    margin-right: 0;
    min-width: auto;
}

.field-selector-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

/* 字段选择按钮 */
.field-selector-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--background-tertiary);
    border: 1px solid var(--separator-opaque);
    border-radius: var(--radius-small);
    font-size: var(--font-size-body);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--animation-fast) ease;
    -webkit-tap-highlight-color: transparent;
    min-height: 44px;
}

.field-selector-button:hover {
    background-color: var(--text-quaternary);
}

.field-selector-button:active {
    transform: scale(0.98);
    background-color: var(--text-tertiary);
}

.field-selector-button::after {
    content: '>';
    font-size: var(--font-size-subhead);
    color: var(--text-tertiary);
    transform: rotate(90deg);
    transition: transform var(--animation-fast) ease;
}

/* 已选字段显示 */
.selected-fields {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    min-height: 20px;
}

.selected-fields:empty::after {
    content: '暂未选择字段';
    color: var(--text-tertiary);
    font-size: var(--font-size-footnote);
    font-style: italic;
}

/* 字段标签 */
.field-tag {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 4px 8px;
    background-color: var(--background-tertiary);
    border-radius: var(--radius-small);
    font-size: var(--font-size-caption);
    color: var(--text-primary);
    font-weight: 500;
    border: 1px solid var(--separator-opaque);
}

.field-tag.row {
    background-color: rgba(52, 199, 89, 0.1);
    color: var(--ios-green);
    border-color: rgba(52, 199, 89, 0.3);
}

.field-tag.column {
    background-color: rgba(0, 122, 255, 0.1);
    color: var(--ios-blue);
    border-color: rgba(0, 122, 255, 0.3);
}

.field-tag.value {
    background-color: rgba(255, 149, 0, 0.1);
    color: var(--ios-orange);
    border-color: rgba(255, 149, 0, 0.3);
}

.field-tag.filter {
    background-color: rgba(175, 82, 222, 0.1);
    color: var(--ios-purple);
    border-color: rgba(175, 82, 222, 0.3);
}

.field-tag-remove {
    background: none;
    border: none;
    color: currentColor;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    padding: 0;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color var(--animation-fast) ease;
    -webkit-tap-highlight-color: transparent;
}

.field-tag-remove:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.field-tag-remove:active {
    transform: scale(0.9);
}

/* 表单操作按钮区域 */
.form-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    padding: var(--spacing-lg) 0;
    margin-top: var(--spacing-lg);
}

.form-actions .ios-button {
    width: 100%;
    justify-content: center;
}

/* 表单验证错误样式 */
.ios-form-input.error {
    border: 1px solid var(--ios-red);
    border-radius: var(--radius-small);
    padding: var(--spacing-xs);
    margin: -var(--spacing-xs);
}

.field-error {
    font-size: var(--font-size-caption);
    color: var(--ios-red);
    margin-top: var(--spacing-xs);
    padding-left: 80px;
}

.field-selector-row .field-error {
    padding-left: 0;
}

/* 响应式调整 */
@media (max-width: 375px) {
    .ios-form-row {
        padding: var(--spacing-sm);
    }

    .ios-form-label {
        min-width: 70px;
        font-size: var(--font-size-subhead);
    }

    .field-error {
        padding-left: 70px;
    }

    .form-actions {
        padding: var(--spacing-md) 0;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .ios-form-group {
        background-color: #1C1C1E;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    .field-selector-button {
        background-color: #2C2C2E;
        border-color: #38383A;
    }

    .field-tag {
        background-color: #2C2C2E;
        border-color: #38383A;
    }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
    .field-selector-button,
    .field-tag-remove,
    .ios-form-input {
        transition: none;
    }

    .field-selector-button:active,
    .field-tag-remove:active {
        transform: none;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .ios-form-group {
        border: 1px solid var(--separator-opaque);
    }

    .field-selector-button {
        border: 2px solid var(--separator-opaque);
    }

    .field-tag {
        border: 2px solid currentColor;
    }
}
