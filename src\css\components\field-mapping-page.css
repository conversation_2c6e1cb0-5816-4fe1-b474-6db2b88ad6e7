/**
 * @file 字段映射管理页面样式
 * @description iOS Human Interface Guidelines风格的字段映射管理页面样式
 * <AUTHOR> Team
 * @version 1.0.0
 */

/* 页面容器 */
.field-mapping-page {
    padding: 20px;
    background-color: #f2f2f7;
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 页面头部 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding: 0 4px;
}

.header-content {
    flex: 1;
}

.page-title {
    font-size: 28px;
    font-weight: 700;
    color: #1c1c1e;
    margin: 0 0 8px 0;
    line-height: 1.2;
}

.page-description {
    font-size: 16px;
    color: #8e8e93;
    margin: 0;
    line-height: 1.4;
}

/* 创建按钮 */
.btn-create-mapping {
    background: #007aff;
    color: white;
    border: none;
    border-radius: 12px;
    padding: 12px 20px;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    min-height: 44px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
}

.btn-create-mapping:hover {
    background: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

.btn-create-mapping:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
}

.btn-icon {
    width: 20px;
    height: 20px;
    fill: currentColor;
}

/* 搜索和筛选栏 */
.search-filter-bar {
    background: white;
    border-radius: 16px;
    padding: 16px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    display: flex;
    gap: 16px;
    align-items: center;
}

.search-container {
    flex: 1;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 12px;
    width: 20px;
    height: 20px;
    fill: #8e8e93;
    z-index: 1;
}

.search-input {
    width: 100%;
    padding: 12px 12px 12px 44px;
    border: 2px solid #e5e5ea;
    border-radius: 12px;
    font-size: 16px;
    background: #f2f2f7;
    transition: all 0.2s ease;
    min-height: 44px;
    box-sizing: border-box;
}

.search-input:focus {
    outline: none;
    border-color: #007aff;
    background: white;
    box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
}

.search-clear {
    position: absolute;
    right: 8px;
    width: 28px;
    height: 28px;
    border: none;
    background: #8e8e93;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.search-clear:hover {
    background: #636366;
}

.search-clear svg {
    width: 16px;
    height: 16px;
    fill: white;
}

/* 排序下拉菜单 */
.sort-dropdown {
    position: relative;
}

.sort-btn {
    background: #f2f2f7;
    border: 2px solid #e5e5ea;
    border-radius: 12px;
    padding: 12px 16px;
    font-size: 16px;
    color: #1c1c1e;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 44px;
}

.sort-btn:hover {
    background: #e5e5ea;
}

.sort-icon {
    width: 20px;
    height: 20px;
    fill: currentColor;
}

.sort-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    padding: 8px;
    min-width: 180px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all 0.2s ease;
}

.sort-menu-visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.sort-option {
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    color: #1c1c1e;
    transition: background-color 0.2s ease;
}

.sort-option:hover {
    background: #f2f2f7;
}

/* 映射模板容器 */
.mapping-templates-container {
    position: relative;
}

.mapping-templates-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

/* 映射模板卡片 */
.mapping-template-card {
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.mapping-template-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.mapping-template-card.default-template {
    border-color: #ff9500;
}

.mapping-template-card.default-template::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff9500, #ffb340);
}

/* 卡片头部 */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.card-title-section {
    flex: 1;
    margin-right: 12px;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: #1c1c1e;
    margin: 0 0 8px 0;
    line-height: 1.3;
}

.card-meta {
    display: flex;
    gap: 8px;
    align-items: center;
}

.category-badge {
    background: #e5e5ea;
    color: #636366;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
}

.category-sales { background: #e3f2fd; color: #1976d2; }
.category-finance { background: #f3e5f5; color: #7b1fa2; }
.category-hr { background: #e8f5e8; color: #388e3c; }
.category-inventory { background: #fff3e0; color: #f57c00; }
.category-custom { background: #e5e5ea; color: #636366; }

.default-badge {
    background: #ff9500;
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
}

/* 卡片操作按钮 */
.card-actions {
    display: flex;
    gap: 4px;
}

.card-action-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: #f2f2f7;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.card-action-btn:hover {
    background: #e5e5ea;
}

.card-action-btn svg {
    width: 18px;
    height: 18px;
    fill: #636366;
}

.card-action-btn.delete-template:hover {
    background: #ff3b30;
}

.card-action-btn.delete-template:hover svg {
    fill: white;
}

/* 卡片内容 */
.card-content {
    margin-bottom: 16px;
}

.card-description {
    font-size: 14px;
    color: #8e8e93;
    line-height: 1.4;
    margin: 0 0 16px 0;
}

.card-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin-bottom: 16px;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: #8e8e93;
    margin-bottom: 4px;
}

.stat-value {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #1c1c1e;
}

/* 映射预览 */
.mapping-preview {
    background: #f2f2f7;
    border-radius: 8px;
    padding: 12px;
}

.mapping-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 12px;
}

.mapping-item:last-child {
    margin-bottom: 0;
}

.source-field {
    color: #636366;
    flex-shrink: 0;
}

.mapping-arrow {
    width: 12px;
    height: 12px;
    fill: #8e8e93;
    flex-shrink: 0;
}

.target-field {
    color: #1c1c1e;
    font-weight: 500;
    flex-shrink: 0;
}

.data-type-badge {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
    flex-shrink: 0;
}

.type-string { background: #e3f2fd; color: #1976d2; }
.type-number { background: #e8f5e8; color: #388e3c; }
.type-date { background: #f3e5f5; color: #7b1fa2; }
.type-time { background: #fff3e0; color: #f57c00; }

/* 卡片底部 */
.card-footer {
    display: flex;
    gap: 12px;
}

.btn-secondary {
    background: #f2f2f7;
    color: #007aff;
    border: 2px solid #e5e5ea;
    border-radius: 12px;
    padding: 10px 16px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    min-height: 44px;
}

.btn-secondary:hover {
    background: #e5e5ea;
}

.btn-primary {
    background: #007aff;
    color: white;
    border: none;
    border-radius: 12px;
    padding: 10px 16px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    min-height: 44px;
}

.btn-primary:hover {
    background: #0056b3;
}

/* 分页控件 */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 24px;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 16px;
    background: white;
    padding: 12px 20px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.pagination-btn {
    width: 44px;
    height: 44px;
    border: none;
    background: #f2f2f7;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
    background: #e5e5ea;
}

.pagination-btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
}

.pagination-btn svg {
    width: 20px;
    height: 20px;
    fill: #636366;
}

.pagination-info {
    font-size: 16px;
    font-weight: 500;
    color: #1c1c1e;
    min-width: 60px;
    text-align: center;
}

/* 空状态 */
.empty-state,
.no-results-state {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.empty-icon,
.no-results-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 24px;
    fill: #c7c7cc;
}

.empty-title,
.no-results-title {
    font-size: 24px;
    font-weight: 600;
    color: #1c1c1e;
    margin: 0 0 12px 0;
}

.empty-description,
.no-results-description {
    font-size: 16px;
    color: #8e8e93;
    margin: 0 0 24px 0;
    line-height: 1.4;
}

.btn-create-first,
.btn-clear-search {
    background: #007aff;
    color: white;
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 44px;
}

.btn-create-first:hover,
.btn-clear-search:hover {
    background: #0056b3;
}

.btn-clear-search {
    background: #8e8e93;
}

.btn-clear-search:hover {
    background: #636366;
}

/* 加载状态 */
.loading-state {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    margin: 0 auto 24px;
}

.spinner-ring {
    width: 40px;
    height: 40px;
    border: 4px solid #e5e5ea;
    border-top: 4px solid #007aff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 16px;
    color: #8e8e93;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .field-mapping-page {
        padding: 16px;
    }
    
    .page-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .mapping-templates-list {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .search-filter-bar {
        flex-direction: column;
        gap: 12px;
    }
    
    .card-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .card-footer {
        flex-direction: column;
    }
}
