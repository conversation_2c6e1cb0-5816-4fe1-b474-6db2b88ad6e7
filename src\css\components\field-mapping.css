/**
 * @file 字段映射主界面组件样式 - 重构版本
 * @description 字段映射界面的iOS风格样式，支持拖拽操作和响应式设计
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2025-01-03 重构为通用字段映射管理模块
 */

/* 字段映射容器 */
.field-mapping-container {
    display: flex;
    flex-direction: column;
    gap: var(--ios-spacing-lg);
    padding: var(--ios-spacing-md);
    background: var(--ios-background-primary);
    min-height: 100vh;
}

/* 区域标题样式 */
.section-header {
    margin-bottom: var(--ios-spacing-md);
}

.section-title {
    display: flex;
    align-items: center;
    gap: var(--ios-spacing-sm);
    font-size: var(--ios-font-size-lg);
    font-weight: var(--ios-font-weight-semibold);
    color: var(--ios-text-primary);
    margin: 0 0 var(--ios-spacing-xs) 0;
}

.section-icon {
    width: 20px;
    height: 20px;
    fill: var(--ios-blue);
}

.section-description {
    font-size: var(--ios-font-size-sm);
    color: var(--ios-text-secondary);
    margin: 0;
}

/* 文件上传区域 */
.file-upload-section {
    background: var(--ios-background-secondary);
    border-radius: var(--ios-border-radius-lg);
    padding: var(--ios-spacing-lg);
    border: 1px solid var(--ios-separator);
}

/* 字段映射区域 */
.field-mapping-section {
    background: var(--ios-background-secondary);
    border-radius: var(--ios-border-radius-lg);
    padding: var(--ios-spacing-lg);
    border: 1px solid var(--ios-separator);
    flex: 1;
}

/* 映射工具栏 */
.mapping-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--ios-spacing-lg);
    padding: var(--ios-spacing-md);
    background: var(--ios-background-tertiary);
    border-radius: var(--ios-border-radius-md);
    border: 1px solid var(--ios-separator);
}

.toolbar-left,
.toolbar-right {
    display: flex;
    gap: var(--ios-spacing-sm);
}

.toolbar-left .btn-secondary,
.toolbar-right .btn-secondary,
.toolbar-right .btn-primary {
    min-height: 44px;
    padding: var(--ios-spacing-sm) var(--ios-spacing-md);
    font-size: var(--ios-font-size-sm);
}

/* 字段映射主界面 */
.mapping-interface {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: var(--ios-spacing-lg);
    min-height: 400px;
    margin-bottom: var(--ios-spacing-lg);
}

/* 字段面板 */
.source-fields-panel,
.target-fields-panel {
    background: var(--ios-background-primary);
    border-radius: var(--ios-border-radius-md);
    border: 1px solid var(--ios-separator);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--ios-spacing-md);
    background: var(--ios-background-tertiary);
    border-bottom: 1px solid var(--ios-separator);
}

.panel-title {
    font-size: var(--ios-font-size-md);
    font-weight: var(--ios-font-weight-semibold);
    color: var(--ios-text-primary);
    margin: 0;
}

.field-count {
    font-size: var(--ios-font-size-sm);
    color: var(--ios-text-secondary);
}

.field-filter .filter-select {
    min-height: 32px;
    padding: var(--ios-spacing-xs) var(--ios-spacing-sm);
    border: 1px solid var(--ios-separator);
    border-radius: var(--ios-border-radius-sm);
    background: var(--ios-background-primary);
    color: var(--ios-text-primary);
    font-size: var(--ios-font-size-sm);
}

/* 字段列表 */
.fields-list {
    flex: 1;
    overflow-y: auto;
    padding: var(--ios-spacing-sm);
    max-height: 500px;
}

/* 字段项 */
.field-item {
    background: var(--ios-background-secondary);
    border: 1px solid var(--ios-separator);
    border-radius: var(--ios-border-radius-md);
    padding: var(--ios-spacing-md);
    margin-bottom: var(--ios-spacing-sm);
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
}

.field-item:hover {
    background: var(--ios-background-tertiary);
    border-color: var(--ios-blue-light);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.field-item.field-mapped {
    border-color: var(--ios-green);
    background: var(--ios-green-light);
}

.field-item.dragging {
    opacity: 0.6;
    transform: rotate(2deg);
}

.field-item.drag-over {
    border-color: var(--ios-blue);
    background: var(--ios-blue-light);
    box-shadow: 0 0 0 2px var(--ios-blue-light);
}

/* 字段头部 */
.field-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--ios-spacing-sm);
}

.field-info {
    flex: 1;
}

.field-name {
    font-size: var(--ios-font-size-md);
    font-weight: var(--ios-font-weight-medium);
    color: var(--ios-text-primary);
    margin: 0 0 var(--ios-spacing-xs) 0;
    word-break: break-word;
}

.field-tags {
    display: flex;
    gap: var(--ios-spacing-xs);
    flex-wrap: wrap;
}

/* 字段类型和类别标签 */
.field-type-tag,
.field-category-tag {
    display: inline-block;
    padding: 2px 6px;
    border-radius: var(--ios-border-radius-sm);
    font-size: 11px;
    font-weight: var(--ios-font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.field-type-tag {
    background: var(--ios-blue-light);
    color: var(--ios-blue-dark);
}

.field-type-tag.field-type-number {
    background: var(--ios-orange-light);
    color: var(--ios-orange-dark);
}

.field-type-tag.field-type-date,
.field-type-tag.field-type-time {
    background: var(--ios-purple-light);
    color: var(--ios-purple-dark);
}

.field-category-tag {
    background: var(--ios-gray-light);
    color: var(--ios-gray-dark);
}

.field-category-tag.field-category-financial {
    background: var(--ios-green-light);
    color: var(--ios-green-dark);
}

.field-category-tag.field-category-temporal {
    background: var(--ios-purple-light);
    color: var(--ios-purple-dark);
}

/* 字段操作 */
.field-actions {
    display: flex;
    align-items: center;
    gap: var(--ios-spacing-xs);
}

.mapped-indicator {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    background: var(--ios-green);
    color: white;
    border-radius: 50%;
    font-size: 12px;
    font-weight: bold;
}

.field-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: transparent;
    border: 1px solid var(--ios-separator);
    border-radius: var(--ios-border-radius-sm);
    cursor: pointer;
    transition: all 0.2s ease;
}

.field-action-btn:hover {
    background: var(--ios-background-tertiary);
    border-color: var(--ios-blue);
}

.field-action-btn svg {
    width: 16px;
    height: 16px;
    fill: var(--ios-text-secondary);
}

.field-action-btn:hover svg {
    fill: var(--ios-blue);
}

/* 字段详情 */
.field-details {
    font-size: var(--ios-font-size-sm);
    color: var(--ios-text-secondary);
}

.field-stats {
    display: flex;
    gap: var(--ios-spacing-md);
    margin-bottom: var(--ios-spacing-xs);
}

.stat-item {
    display: flex;
    gap: var(--ios-spacing-xs);
}

.stat-label {
    font-weight: var(--ios-font-weight-medium);
}

.field-samples,
.field-description,
.field-keywords {
    display: flex;
    gap: var(--ios-spacing-xs);
    margin-bottom: var(--ios-spacing-xs);
}

.field-samples:last-child,
.field-description:last-child,
.field-keywords:last-child {
    margin-bottom: 0;
}

.samples-label,
.description-label,
.keywords-label {
    font-weight: var(--ios-font-weight-medium);
    min-width: 60px;
}

.samples-value,
.description-value,
.keywords-value {
    word-break: break-word;
}

/* 映射连接区域 */
.mapping-connections {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 120px;
    position: relative;
}

.connection-lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.mapping-hint {
    text-align: center;
    padding: var(--ios-spacing-lg);
    color: var(--ios-text-secondary);
}

.hint-icon {
    width: 32px;
    height: 32px;
    fill: var(--ios-text-tertiary);
    margin-bottom: var(--ios-spacing-sm);
}

.mapping-hint p {
    font-size: var(--ios-font-size-sm);
    margin: 0;
    line-height: 1.4;
}

/* 操作按钮区域 */
.action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: var(--ios-spacing-md);
    padding: var(--ios-spacing-lg);
    background: var(--ios-background-secondary);
    border-radius: var(--ios-border-radius-lg);
    border: 1px solid var(--ios-separator);
}

.action-buttons .btn-secondary,
.action-buttons .btn-primary {
    min-height: 44px;
    min-width: 120px;
    padding: var(--ios-spacing-md) var(--ios-spacing-lg);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .mapping-interface {
        grid-template-columns: 1fr;
        gap: var(--ios-spacing-md);
    }
    
    .mapping-connections {
        order: 3;
        min-height: 60px;
    }
    
    .target-fields-panel {
        order: 2;
    }
    
    .mapping-toolbar {
        flex-direction: column;
        gap: var(--ios-spacing-md);
    }
    
    .toolbar-left,
    .toolbar-right {
        width: 100%;
        justify-content: center;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .action-buttons .btn-secondary,
    .action-buttons .btn-primary {
        width: 100%;
    }
}
