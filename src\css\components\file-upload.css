/**
 * @file 文件上传组件样式
 * @description iOS风格的文件上传组件界面
 * <AUTHOR> Team
 */

/* 文件上传组件容器 */
.so-file-upload {
    background-color: var(--background-secondary);
    border-radius: var(--radius-medium);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

/* 文件拖拽上传区域 */
.so-upload-dropzone {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    border: 2px dashed var(--separator-opaque);
    border-radius: var(--radius-medium);
    background-color: var(--background-tertiary);
    transition: all var(--animation-fast) ease;
    text-align: center;
    min-height: 200px;
    cursor: pointer;
}

.so-upload-dropzone:hover {
    border-color: var(--ios-blue);
    background-color: rgba(0, 122, 255, 0.05);
}

.so-upload-dropzone.so-dragover {
    border-color: var(--ios-blue);
    background-color: rgba(0, 122, 255, 0.1);
    transform: scale(1.02);
}

/* 上传图标 */
.so-upload-icon {
    width: 48px;
    height: 48px;
    margin-bottom: var(--spacing-md);
    opacity: 0.6;
}

.so-upload-icon svg {
    width: 100%;
    height: 100%;
    fill: var(--ios-blue);
}

/* 上传标题和描述 */
.so-upload-title {
    font-size: var(--font-size-title3);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
}

.so-upload-description {
    font-size: var(--font-size-footnote);
    color: var(--text-secondary);
    line-height: 1.4;
    margin: 0 0 var(--spacing-lg) 0;
    max-width: 280px;
}

/* 上传按钮 */
.so-upload-button {
    background-color: var(--ios-blue);
    color: white;
    border: none;
    border-radius: var(--radius-medium);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-body);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--animation-fast) ease;
    -webkit-tap-highlight-color: transparent;
    min-height: 44px;
}

.so-upload-button:hover {
    background-color: #0056D6;
}

.so-upload-button:active {
    transform: scale(0.98);
    background-color: #004BB8;
}

/* 隐藏的文件输入 */
.so-file-input {
    display: none !important;
    position: absolute;
    left: -9999px;
    opacity: 0;
    pointer-events: none;
}

/* 上传进度区域 */
.so-upload-progress {
    padding: var(--spacing-lg);
    background-color: var(--background-secondary);
    border-radius: var(--radius-medium);
}

.so-progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.so-progress-filename {
    font-size: var(--font-size-subhead);
    color: var(--text-primary);
    font-weight: 500;
    flex: 1;
    margin-right: var(--spacing-sm);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.so-progress-percentage {
    font-size: var(--font-size-subhead);
    color: var(--ios-blue);
    font-weight: 600;
    flex-shrink: 0;
}

/* 进度条 */
.so-progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--background-tertiary);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.so-progress-fill {
    height: 100%;
    background-color: var(--ios-blue);
    transition: width var(--animation-fast) ease;
    border-radius: 4px;
}

.so-progress-status {
    font-size: var(--font-size-footnote);
    color: var(--text-secondary);
    text-align: center;
}

/* 上传结果区域 */
.so-upload-result {
    padding: var(--spacing-lg);
    background-color: var(--background-secondary);
    border-radius: var(--radius-medium);
    text-align: center;
}

.so-result-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto var(--spacing-md);
}

.so-result-icon svg {
    width: 100%;
    height: 100%;
}

.so-result-icon.so-success svg {
    fill: var(--ios-green);
}

.so-result-icon.so-error svg {
    fill: var(--ios-red);
}

/* 结果消息 */
.so-result-message h3 {
    font-size: var(--font-size-title3);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.so-result-message p {
    font-size: var(--font-size-subhead);
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-sm) 0;
    line-height: 1.4;
}

.so-result-message .file-info {
    font-size: var(--font-size-footnote);
    color: var(--text-tertiary);
    margin: var(--spacing-xs) 0 0 0;
}

/* 结果操作按钮 */
.so-result-action {
    background-color: var(--ios-blue);
    color: white;
    border: none;
    border-radius: var(--radius-medium);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-body);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--animation-fast) ease;
    -webkit-tap-highlight-color: transparent;
    min-height: 44px;
    margin-top: var(--spacing-md);
}

.so-result-action:hover {
    background-color: #0056D6;
}

.so-result-action:active {
    transform: scale(0.98);
    background-color: #004BB8;
}

/* 响应式调整 */
@media (max-width: 375px) {
    .so-upload-dropzone {
        padding: var(--spacing-lg);
        min-height: 160px;
    }
    
    .so-upload-icon {
        width: 40px;
        height: 40px;
        margin-bottom: var(--spacing-sm);
    }
    
    .so-upload-title {
        font-size: var(--font-size-headline);
    }
    
    .so-upload-description {
        font-size: var(--font-size-caption);
    }
    
    .so-upload-button,
    .so-result-action {
        padding: var(--spacing-xs) var(--spacing-md);
        font-size: var(--font-size-subhead);
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .so-upload-dropzone {
        background-color: #1C1C1E;
        border-color: #38383A;
    }
    
    .so-upload-dropzone:hover {
        background-color: rgba(0, 122, 255, 0.1);
    }
    
    .so-upload-progress,
    .so-upload-result {
        background-color: #1C1C1E;
    }
    
    .so-progress-bar {
        background-color: #2C2C2E;
    }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
    .so-upload-dropzone,
    .so-upload-button,
    .so-result-action,
    .so-progress-fill {
        transition: none;
    }
    
    .so-upload-dropzone.so-dragover {
        transform: none;
    }
    
    .so-upload-button:active,
    .so-result-action:active {
        transform: none;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .so-upload-dropzone {
        border-width: 3px;
        border-color: var(--text-primary);
    }
    
    .so-upload-button,
    .so-result-action {
        border: 2px solid var(--ios-blue);
    }
}

SmartOffice.log('info', '文件上传组件样式已加载');
