/**
 * 筛选器组件样式
 * iOS风格的数据筛选和排序界面
 */

/* 筛选器容器 */
.filter-container {
    background: #ffffff;
    border-radius: 12px;
    margin: 16px 0;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: #f2f2f7;
    border-bottom: 1px solid #e5e5ea;
}

.filter-title {
    font-size: 18px;
    font-weight: 600;
    color: #1c1c1e;
    margin: 0;
}

.filter-toggle {
    background: #007aff;
    color: #ffffff;
    border: none;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-toggle:hover {
    background: #0056cc;
}

.filter-toggle:active {
    transform: scale(0.95);
}

/* 快速筛选栏 */
.filter-quick-bar {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: #f2f2f7;
    border-bottom: 1px solid #e5e5ea;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.filter-search {
    flex: 1;
    min-width: 200px;
    background: #ffffff;
    border: 1px solid #d1d1d6;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 14px;
    color: #1c1c1e;
}

.filter-search:focus {
    outline: none;
    border-color: #007aff;
}

.filter-search::placeholder {
    color: #8e8e93;
}

.filter-quick-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.filter-quick-button {
    background: #ffffff;
    border: 1px solid #d1d1d6;
    border-radius: 6px;
    padding: 6px 10px;
    font-size: 12px;
    color: #007aff;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.filter-quick-button:hover {
    background: #f2f2f7;
}

.filter-quick-button.active {
    background: #007aff;
    color: #ffffff;
    border-color: #007aff;
}

/* 筛选器面板 */
.filter-panel {
    padding: 16px;
    display: none;
}

.filter-panel.active {
    display: block;
}

.filter-section {
    margin-bottom: 20px;
}

.filter-section:last-child {
    margin-bottom: 0;
}

.filter-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #1c1c1e;
    margin: 0 0 12px 0;
}

/* 筛选条件 */
.filter-conditions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.filter-condition {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: #f2f2f7;
    border-radius: 8px;
}

.filter-field-select,
.filter-operator-select {
    background: #ffffff;
    border: 1px solid #d1d1d6;
    border-radius: 6px;
    padding: 6px 8px;
    font-size: 14px;
    color: #1c1c1e;
    min-width: 100px;
}

.filter-field-select:focus,
.filter-operator-select:focus {
    outline: none;
    border-color: #007aff;
}

.filter-value-input {
    flex: 1;
    background: #ffffff;
    border: 1px solid #d1d1d6;
    border-radius: 6px;
    padding: 6px 8px;
    font-size: 14px;
    color: #1c1c1e;
}

.filter-value-input:focus {
    outline: none;
    border-color: #007aff;
}

.filter-remove-button {
    background: #ff3b30;
    color: #ffffff;
    border: none;
    border-radius: 4px;
    padding: 6px 8px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.filter-remove-button:hover {
    background: #d70015;
}

/* 添加筛选条件 */
.filter-add-condition {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: #f2f2f7;
    border: 2px dashed #d1d1d6;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #8e8e93;
}

.filter-add-condition:hover {
    border-color: #007aff;
    color: #007aff;
}

.filter-add-icon {
    font-size: 16px;
}

.filter-add-text {
    font-size: 14px;
}

/* 排序控件 */
.filter-sort-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.filter-sort-field {
    flex: 1;
    background: #ffffff;
    border: 1px solid #d1d1d6;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    color: #1c1c1e;
}

.filter-sort-field:focus {
    outline: none;
    border-color: #007aff;
}

.filter-sort-direction {
    display: flex;
    background: #f2f2f7;
    border-radius: 6px;
    overflow: hidden;
}

.filter-sort-option {
    background: transparent;
    border: none;
    padding: 8px 12px;
    font-size: 14px;
    color: #8e8e93;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-sort-option.active {
    background: #007aff;
    color: #ffffff;
}

/* 筛选结果统计 */
.filter-results {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f2f2f7;
    border-top: 1px solid #e5e5ea;
    font-size: 14px;
    color: #8e8e93;
}

.filter-results-count {
    font-weight: 600;
    color: #1c1c1e;
}

.filter-results-actions {
    display: flex;
    gap: 8px;
}

.filter-results-button {
    background: transparent;
    border: none;
    color: #007aff;
    font-size: 14px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.filter-results-button:hover {
    background: #e5e5ea;
}

/* 活跃筛选器标签 */
.filter-active-tags {
    display: flex;
    gap: 8px;
    padding: 12px 16px;
    background: #ffffff;
    border-bottom: 1px solid #e5e5ea;
    flex-wrap: wrap;
}

.filter-tag {
    display: flex;
    align-items: center;
    gap: 6px;
    background: #007aff;
    color: #ffffff;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.filter-tag-remove {
    background: transparent;
    border: none;
    color: #ffffff;
    font-size: 14px;
    cursor: pointer;
    padding: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.filter-tag-remove:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* 筛选器操作栏 */
.filter-actions {
    display: flex;
    gap: 8px;
    padding: 16px;
    border-top: 1px solid #e5e5ea;
    background: #f2f2f7;
}

.filter-action-button {
    flex: 1;
    background: #007aff;
    color: #ffffff;
    border: none;
    border-radius: 8px;
    padding: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-action-button:hover {
    background: #0056cc;
}

.filter-action-button:active {
    transform: scale(0.95);
}

.filter-action-button.secondary {
    background: #ffffff;
    color: #007aff;
    border: 1px solid #d1d1d6;
}

.filter-action-button.secondary:hover {
    background: #f2f2f7;
}

.filter-action-button.danger {
    background: #ff3b30;
}

.filter-action-button.danger:hover {
    background: #d70015;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .filter-container {
        margin: 12px 0;
    }
    
    .filter-header,
    .filter-panel,
    .filter-actions {
        padding: 12px;
    }
    
    .filter-quick-bar {
        padding: 8px 12px;
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .filter-search {
        min-width: auto;
    }
    
    .filter-quick-actions {
        justify-content: center;
    }
    
    .filter-condition {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .filter-sort-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .filter-results {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
        text-align: center;
    }
    
    .filter-active-tags {
        padding: 8px 12px;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .filter-container {
        background: #1c1c1e;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    }
    
    .filter-header,
    .filter-quick-bar,
    .filter-results,
    .filter-actions {
        background: #2c2c2e;
        border-color: #3a3a3c;
    }
    
    .filter-title {
        color: #ffffff;
    }
    
    .filter-search,
    .filter-field-select,
    .filter-operator-select,
    .filter-value-input,
    .filter-sort-field {
        background: #1c1c1e;
        border-color: #3a3a3c;
        color: #ffffff;
    }
    
    .filter-search::placeholder {
        color: #8e8e93;
    }
    
    .filter-quick-button {
        background: #1c1c1e;
        border-color: #3a3a3c;
        color: #0a84ff;
    }
    
    .filter-quick-button:hover {
        background: #2c2c2e;
    }
    
    .filter-quick-button.active {
        background: #0a84ff;
        border-color: #0a84ff;
    }
    
    .filter-condition {
        background: #2c2c2e;
    }
    
    .filter-add-condition {
        background: #2c2c2e;
        border-color: #3a3a3c;
    }
    
    .filter-add-condition:hover {
        border-color: #0a84ff;
        color: #0a84ff;
    }
    
    .filter-sort-direction {
        background: #2c2c2e;
    }
    
    .filter-sort-option.active {
        background: #0a84ff;
    }
    
    .filter-results-count {
        color: #ffffff;
    }
    
    .filter-results-button {
        color: #0a84ff;
    }
    
    .filter-results-button:hover {
        background: #3a3a3c;
    }
    
    .filter-active-tags {
        background: #1c1c1e;
        border-color: #3a3a3c;
    }
    
    .filter-action-button.secondary {
        background: #1c1c1e;
        color: #0a84ff;
        border-color: #3a3a3c;
    }
    
    .filter-action-button.secondary:hover {
        background: #2c2c2e;
    }
}
