/**
 * @file 映射配置对话框样式
 * @description iOS Human Interface Guidelines风格的映射配置对话框样式
 * <AUTHOR> Team
 * @version 1.0.0
 */

/* 对话框遮罩层 */
.mapping-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
    padding: 20px;
    box-sizing: border-box;
}

.dialog-visible {
    opacity: 1;
}

/* 对话框主体 */
.mapping-dialog {
    background: white;
    border-radius: 20px;
    width: 100%;
    max-width: 1200px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transform: scale(0.9) translateY(20px);
    transition: transform 0.3s ease;
    font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON>o, sans-serif;
}

.dialog-visible .mapping-dialog {
    transform: scale(1) translateY(0);
}

/* 对话框头部 */
.dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 0 24px;
    border-bottom: 1px solid #e5e5ea;
    margin-bottom: 24px;
}

.dialog-title {
    font-size: 24px;
    font-weight: 700;
    color: #1c1c1e;
    margin: 0;
}

.dialog-close {
    width: 44px;
    height: 44px;
    border: none;
    background: #f2f2f7;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dialog-close:hover {
    background: #e5e5ea;
}

.dialog-close svg {
    width: 20px;
    height: 20px;
    fill: #636366;
}

/* 对话框内容 */
.dialog-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 24px;
}

/* 表单样式 */
.template-info-section {
    background: #f2f2f7;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 24px;
}

.form-group {
    margin-bottom: 16px;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 16px;
    align-items: end;
}

.form-label {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #1c1c1e;
    margin-bottom: 8px;
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e5ea;
    border-radius: 12px;
    font-size: 16px;
    background: white;
    transition: all 0.2s ease;
    box-sizing: border-box;
    min-height: 44px;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: #007aff;
    box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

.form-checkbox {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    accent-color: #007aff;
}

/* 智能建议区域 */
.smart-suggestions-section {
    background: white;
    border: 2px solid #e5e5ea;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 24px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #1c1c1e;
    margin: 0;
}

.btn-apply-suggestions {
    background: #34c759;
    color: white;
    border: none;
    border-radius: 12px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 36px;
}

.btn-apply-suggestions:hover {
    background: #28a745;
}

/* 建议列表 */
.suggestions-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.suggestion-item {
    background: #f2f2f7;
    border-radius: 12px;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.suggestion-content {
    flex: 1;
}

.suggestion-mapping {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.suggestion-mapping .source-field {
    font-weight: 500;
    color: #636366;
}

.suggestion-mapping .mapping-arrow {
    width: 16px;
    height: 16px;
    fill: #8e8e93;
}

.suggestion-mapping .target-field {
    font-weight: 600;
    color: #1c1c1e;
}

.suggestion-meta {
    display: flex;
    align-items: center;
    gap: 12px;
}

.confidence-badge {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
}

.confidence-high { background: #d4edda; color: #155724; }
.confidence-medium { background: #fff3cd; color: #856404; }
.confidence-low { background: #f8d7da; color: #721c24; }

.reason-text {
    font-size: 12px;
    color: #8e8e93;
}

.btn-apply-suggestion {
    background: #007aff;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 32px;
}

.btn-apply-suggestion:hover {
    background: #0056b3;
}

/* 映射配置区域 */
.mapping-config-section {
    background: white;
    border: 2px solid #e5e5ea;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 24px;
}

.mapping-actions {
    display: flex;
    gap: 12px;
}

.btn-load-sample,
.btn-clear-mappings {
    background: #f2f2f7;
    color: #636366;
    border: 2px solid #e5e5ea;
    border-radius: 8px;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 32px;
}

.btn-load-sample:hover,
.btn-clear-mappings:hover {
    background: #e5e5ea;
}

.btn-clear-mappings {
    color: #ff3b30;
}

.btn-clear-mappings:hover {
    background: #ff3b30;
    color: white;
}

/* 映射工作区 */
.mapping-workspace {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: 20px;
    margin-top: 20px;
}

/* 字段面板 */
.source-fields-panel,
.target-fields-panel {
    background: #f2f2f7;
    border-radius: 12px;
    padding: 16px;
    height: 400px;
    display: flex;
    flex-direction: column;
}

.panel-header {
    margin-bottom: 16px;
}

.panel-title {
    font-size: 16px;
    font-weight: 600;
    color: #1c1c1e;
    margin: 0 0 12px 0;
}

/* 文件上传区域 */
.field-upload-area {
    border: 2px dashed #c7c7cc;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.field-upload-area:hover,
.field-upload-area.drag-over {
    border-color: #007aff;
    background: rgba(0, 122, 255, 0.05);
}

.upload-icon {
    width: 32px;
    height: 32px;
    fill: #c7c7cc;
    margin-bottom: 8px;
}

.upload-text {
    font-size: 14px;
    color: #8e8e93;
    margin: 0;
}

.upload-link {
    color: #007aff;
    text-decoration: underline;
}

/* 字段搜索 */
.field-search {
    margin-bottom: 12px;
}

.field-search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e5e5ea;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    box-sizing: border-box;
}

/* 字段列表 */
.fields-list {
    flex: 1;
    overflow-y: auto;
    margin: -4px;
    padding: 4px;
}

.field-group {
    margin-bottom: 16px;
}

.field-group:last-child {
    margin-bottom: 0;
}

.field-group-header {
    font-size: 12px;
    font-weight: 600;
    color: #8e8e93;
    text-transform: uppercase;
    margin-bottom: 8px;
    padding: 0 4px;
}

.field-group-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

/* 字段项 */
.source-field-item,
.standard-field-item {
    background: white;
    border: 1px solid #e5e5ea;
    border-radius: 8px;
    padding: 12px;
    cursor: grab;
    transition: all 0.2s ease;
    user-select: none;
}

.source-field-item:hover,
.standard-field-item:hover {
    background: #f8f9fa;
    border-color: #007aff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.source-field-item:active,
.standard-field-item:active {
    cursor: grabbing;
}

.field-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.field-name {
    font-weight: 500;
    color: #1c1c1e;
    font-size: 14px;
}

.field-description {
    font-size: 12px;
    color: #8e8e93;
}

.empty-fields {
    text-align: center;
    padding: 40px 20px;
    color: #8e8e93;
}

.empty-hint {
    font-size: 12px;
    margin-top: 4px;
}

/* 映射区域 */
.mapping-area {
    background: white;
    border: 2px dashed #e5e5ea;
    border-radius: 12px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    min-height: 400px;
}

.mapping-list {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 16px;
}

.empty-mappings {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #8e8e93;
    text-align: center;
}

.empty-mappings p {
    margin: 4px 0;
}

/* 映射项 */
.mapping-item {
    background: #f2f2f7;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    border: 2px solid transparent;
    transition: all 0.2s ease;
}

.mapping-item:hover {
    border-color: #007aff;
}

.mapping-content {
    display: grid;
    grid-template-columns: 1fr auto 1fr auto auto auto;
    gap: 12px;
    align-items: center;
}

.field-label {
    font-size: 12px;
    font-weight: 600;
    color: #8e8e93;
    margin-bottom: 4px;
    display: block;
}

.mapping-input,
.mapping-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e5e5ea;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    box-sizing: border-box;
    min-height: 36px;
}

.mapping-input:focus,
.mapping-select:focus {
    outline: none;
    border-color: #007aff;
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1);
}

.mapping-arrow {
    width: 20px;
    height: 20px;
    fill: #8e8e93;
    margin: 0 8px;
}

.confidence-section {
    text-align: center;
}

.confidence-label {
    font-size: 10px;
    color: #8e8e93;
    display: block;
    margin-bottom: 2px;
}

.confidence-value {
    font-size: 12px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
}

.mapping-actions {
    display: flex;
    gap: 4px;
}

.mapping-action-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: #e5e5ea;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mapping-action-btn:hover {
    background: #d1d1d6;
}

.mapping-action-btn.active {
    background: #007aff;
}

.mapping-action-btn.active svg {
    fill: white;
}

.mapping-action-btn svg {
    width: 16px;
    height: 16px;
    fill: #636366;
}

.delete-mapping:hover {
    background: #ff3b30;
}

.delete-mapping:hover svg {
    fill: white;
}

/* 添加映射按钮 */
.btn-add-mapping {
    background: #007aff;
    color: white;
    border: none;
    border-radius: 12px;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 44px;
}

.btn-add-mapping:hover {
    background: #0056b3;
}

/* 映射预览区域 */
.mapping-preview-section {
    background: white;
    border: 2px solid #e5e5ea;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 24px;
}

.btn-toggle-preview {
    background: #f2f2f7;
    color: #636366;
    border: 2px solid #e5e5ea;
    border-radius: 8px;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 32px;
}

.btn-toggle-preview:hover {
    background: #e5e5ea;
}

/* 预览表格 */
.preview-table {
    border: 1px solid #e5e5ea;
    border-radius: 8px;
    overflow: hidden;
}

.preview-header,
.preview-row {
    display: grid;
    grid-template-columns: 1fr 1fr auto auto;
    gap: 1px;
    background: #e5e5ea;
}

.preview-header {
    background: #f2f2f7;
}

.preview-col {
    background: white;
    padding: 12px;
    font-size: 14px;
}

.preview-header .preview-col {
    background: #f2f2f7;
    font-weight: 600;
    color: #1c1c1e;
}

.type-badge {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
}

/* 对话框底部 */
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 24px;
    border-top: 1px solid #e5e5ea;
    background: #f2f2f7;
    border-radius: 0 0 20px 20px;
}

.dialog-footer .btn-secondary {
    background: white;
    color: #636366;
    border: 2px solid #e5e5ea;
    border-radius: 12px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 44px;
}

.dialog-footer .btn-secondary:hover {
    background: #f2f2f7;
}

.dialog-footer .btn-primary {
    background: #007aff;
    color: white;
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 44px;
}

.dialog-footer .btn-primary:hover {
    background: #0056b3;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .mapping-workspace {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .source-fields-panel,
    .target-fields-panel {
        height: 200px;
    }
    
    .mapping-area {
        min-height: 300px;
    }
}

@media (max-width: 768px) {
    .mapping-dialog {
        margin: 10px;
        max-height: 95vh;
    }
    
    .dialog-header,
    .dialog-content,
    .dialog-footer {
        padding-left: 16px;
        padding-right: 16px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .mapping-content {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .mapping-arrow {
        transform: rotate(90deg);
        margin: 8px 0;
    }
    
    .preview-header,
    .preview-row {
        grid-template-columns: 1fr;
    }
}

/* 防止页面滚动 */
body.dialog-open {
    overflow: hidden;
}
