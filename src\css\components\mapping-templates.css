/**
 * @file 字段映射模板管理组件样式 - 重构版本
 * @description 字段映射模板管理界面的iOS风格样式，支持模板卡片和对话框
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2025-01-03 重构为通用字段映射管理模块
 */

/* 模板管理容器 */
.mapping-templates-container {
    display: flex;
    flex-direction: column;
    gap: var(--ios-spacing-lg);
    padding: var(--ios-spacing-md);
    background: var(--ios-background-primary);
    min-height: 100vh;
}

/* 模板管理头部 */
.templates-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: var(--ios-spacing-lg);
    background: var(--ios-background-secondary);
    border-radius: var(--ios-border-radius-lg);
    border: 1px solid var(--ios-separator);
}

.header-content {
    flex: 1;
}

.header-title {
    display: flex;
    align-items: center;
    gap: var(--ios-spacing-sm);
    font-size: var(--ios-font-size-xl);
    font-weight: var(--ios-font-weight-semibold);
    color: var(--ios-text-primary);
    margin: 0 0 var(--ios-spacing-xs) 0;
}

.header-icon {
    width: 24px;
    height: 24px;
    fill: var(--ios-blue);
}

.header-description {
    font-size: var(--ios-font-size-sm);
    color: var(--ios-text-secondary);
    margin: 0;
}

.header-actions {
    display: flex;
    gap: var(--ios-spacing-sm);
}

.header-actions .btn-secondary,
.header-actions .btn-primary {
    min-height: 44px;
    padding: var(--ios-spacing-sm) var(--ios-spacing-md);
    font-size: var(--ios-font-size-sm);
}

/* 模板筛选区域 */
.templates-filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--ios-spacing-md);
    background: var(--ios-background-secondary);
    border-radius: var(--ios-border-radius-md);
    border: 1px solid var(--ios-separator);
    gap: var(--ios-spacing-lg);
}

.filter-left {
    display: flex;
    gap: var(--ios-spacing-lg);
    align-items: center;
}

.filter-right {
    display: flex;
    align-items: center;
}

.category-filter .filter-select {
    min-height: 36px;
    padding: var(--ios-spacing-xs) var(--ios-spacing-sm);
    border: 1px solid var(--ios-separator);
    border-radius: var(--ios-border-radius-sm);
    background: var(--ios-background-primary);
    color: var(--ios-text-primary);
    font-size: var(--ios-font-size-sm);
    min-width: 120px;
}

.template-type-filter {
    display: flex;
    gap: var(--ios-spacing-md);
}

.filter-checkbox {
    display: flex;
    align-items: center;
    gap: var(--ios-spacing-xs);
    cursor: pointer;
    font-size: var(--ios-font-size-sm);
    color: var(--ios-text-primary);
}

.filter-checkbox input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--ios-blue);
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    width: 240px;
    height: 36px;
    padding: var(--ios-spacing-xs) var(--ios-spacing-sm) var(--ios-spacing-xs) 36px;
    border: 1px solid var(--ios-separator);
    border-radius: var(--ios-border-radius-sm);
    background: var(--ios-background-primary);
    color: var(--ios-text-primary);
    font-size: var(--ios-font-size-sm);
}

.search-input:focus {
    outline: none;
    border-color: var(--ios-blue);
    box-shadow: 0 0 0 2px var(--ios-blue-light);
}

.search-icon {
    position: absolute;
    left: 10px;
    width: 16px;
    height: 16px;
    fill: var(--ios-text-tertiary);
    pointer-events: none;
}

/* 模板列表 */
.templates-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--ios-spacing-lg);
    padding: var(--ios-spacing-sm);
}

/* 模板卡片 */
.template-card {
    background: var(--ios-background-secondary);
    border: 1px solid var(--ios-separator);
    border-radius: var(--ios-border-radius-lg);
    overflow: hidden;
    transition: all 0.2s ease;
    cursor: pointer;
}

.template-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    border-color: var(--ios-blue-light);
}

.template-card.builtin-template {
    border-left: 4px solid var(--ios-blue);
}

.template-card.user-template {
    border-left: 4px solid var(--ios-green);
}

/* 卡片头部 */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: var(--ios-spacing-md);
    border-bottom: 1px solid var(--ios-separator);
}

.card-title-section {
    flex: 1;
}

.card-title {
    font-size: var(--ios-font-size-md);
    font-weight: var(--ios-font-weight-semibold);
    color: var(--ios-text-primary);
    margin: 0 0 var(--ios-spacing-xs) 0;
    word-break: break-word;
}

.card-badges {
    display: flex;
    gap: var(--ios-spacing-xs);
    flex-wrap: wrap;
}

.category-badge,
.builtin-badge,
.user-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: var(--ios-border-radius-sm);
    font-size: 11px;
    font-weight: var(--ios-font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.category-badge {
    background: var(--ios-gray-light);
    color: var(--ios-gray-dark);
}

.category-badge.category-sales {
    background: var(--ios-blue-light);
    color: var(--ios-blue-dark);
}

.category-badge.category-finance {
    background: var(--ios-green-light);
    color: var(--ios-green-dark);
}

.category-badge.category-inventory {
    background: var(--ios-orange-light);
    color: var(--ios-orange-dark);
}

.category-badge.category-hr {
    background: var(--ios-purple-light);
    color: var(--ios-purple-dark);
}

.builtin-badge {
    background: var(--ios-blue-light);
    color: var(--ios-blue-dark);
}

.user-badge {
    background: var(--ios-green-light);
    color: var(--ios-green-dark);
}

/* 卡片操作 */
.card-actions {
    display: flex;
    gap: var(--ios-spacing-xs);
}

.card-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: transparent;
    border: 1px solid var(--ios-separator);
    border-radius: var(--ios-border-radius-sm);
    cursor: pointer;
    transition: all 0.2s ease;
}

.card-action-btn:hover {
    background: var(--ios-background-tertiary);
    border-color: var(--ios-blue);
}

.card-action-btn svg {
    width: 16px;
    height: 16px;
    fill: var(--ios-text-secondary);
}

.card-action-btn:hover svg {
    fill: var(--ios-blue);
}

.card-action-btn.delete-template:hover {
    background: var(--ios-red-light);
    border-color: var(--ios-red);
}

.card-action-btn.delete-template:hover svg {
    fill: var(--ios-red);
}

/* 卡片内容 */
.card-content {
    padding: var(--ios-spacing-md);
}

.card-description {
    font-size: var(--ios-font-size-sm);
    color: var(--ios-text-secondary);
    margin: 0 0 var(--ios-spacing-md) 0;
    line-height: 1.4;
}

.card-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--ios-spacing-sm);
    margin-bottom: var(--ios-spacing-md);
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: var(--ios-font-size-xs);
    color: var(--ios-text-tertiary);
    margin-bottom: 2px;
}

.stat-value {
    display: block;
    font-size: var(--ios-font-size-sm);
    font-weight: var(--ios-font-weight-medium);
    color: var(--ios-text-primary);
}

/* 映射预览 */
.mapping-preview {
    border-top: 1px solid var(--ios-separator);
    padding-top: var(--ios-spacing-sm);
}

.preview-title {
    font-size: var(--ios-font-size-sm);
    font-weight: var(--ios-font-weight-medium);
    color: var(--ios-text-primary);
    margin: 0 0 var(--ios-spacing-xs) 0;
}

.preview-list {
    display: flex;
    flex-direction: column;
    gap: var(--ios-spacing-xs);
}

.preview-item {
    display: flex;
    align-items: center;
    gap: var(--ios-spacing-xs);
    font-size: var(--ios-font-size-xs);
}

.source-field,
.target-field {
    color: var(--ios-text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80px;
}

.mapping-arrow {
    width: 12px;
    height: 12px;
    fill: var(--ios-text-tertiary);
    flex-shrink: 0;
}

/* 卡片底部 */
.card-footer {
    display: flex;
    gap: var(--ios-spacing-sm);
    padding: var(--ios-spacing-md);
    background: var(--ios-background-tertiary);
    border-top: 1px solid var(--ios-separator);
}

.card-footer .btn-secondary,
.card-footer .btn-primary {
    flex: 1;
    min-height: 36px;
    padding: var(--ios-spacing-xs) var(--ios-spacing-sm);
    font-size: var(--ios-font-size-sm);
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--ios-spacing-xl);
    text-align: center;
    background: var(--ios-background-secondary);
    border-radius: var(--ios-border-radius-lg);
    border: 1px solid var(--ios-separator);
}

.empty-icon {
    width: 64px;
    height: 64px;
    margin-bottom: var(--ios-spacing-lg);
    opacity: 0.3;
}

.empty-icon svg {
    width: 100%;
    height: 100%;
    fill: var(--ios-text-tertiary);
}

.empty-title {
    font-size: var(--ios-font-size-lg);
    font-weight: var(--ios-font-weight-semibold);
    color: var(--ios-text-primary);
    margin: 0 0 var(--ios-spacing-sm) 0;
}

.empty-description {
    font-size: var(--ios-font-size-md);
    color: var(--ios-text-secondary);
    margin: 0 0 var(--ios-spacing-lg) 0;
    max-width: 400px;
}

.empty-actions {
    display: flex;
    gap: var(--ios-spacing-md);
}

.empty-actions .btn-primary,
.empty-actions .btn-secondary {
    min-height: 44px;
    padding: var(--ios-spacing-md) var(--ios-spacing-lg);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .templates-filter {
        flex-direction: column;
        gap: var(--ios-spacing-md);
    }
    
    .filter-left {
        width: 100%;
        flex-direction: column;
        gap: var(--ios-spacing-md);
    }
    
    .template-type-filter {
        justify-content: center;
    }
    
    .search-container {
        width: 100%;
    }
    
    .search-input {
        width: 100%;
    }
    
    .templates-list {
        grid-template-columns: 1fr;
    }
    
    .templates-header {
        flex-direction: column;
        gap: var(--ios-spacing-md);
    }
    
    .header-actions {
        width: 100%;
        justify-content: center;
    }
    
    .card-stats {
        grid-template-columns: 1fr;
        gap: var(--ios-spacing-xs);
    }
    
    .empty-actions {
        flex-direction: column;
        width: 100%;
    }
}

/* 模板保存对话框 */
.template-save-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--ios-spacing-lg);
}

.dialog-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
}

.dialog-content {
    position: relative;
    background: var(--ios-background-primary);
    border-radius: var(--ios-border-radius-lg);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    width: 100%;
    max-width: 480px;
    max-height: 90vh;
    overflow: hidden;
    animation: dialogSlideIn 0.3s ease-out;
}

@keyframes dialogSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--ios-spacing-lg);
    border-bottom: 1px solid var(--ios-separator);
    background: var(--ios-background-secondary);
}

.dialog-title {
    font-size: var(--ios-font-size-lg);
    font-weight: var(--ios-font-weight-semibold);
    color: var(--ios-text-primary);
    margin: 0;
}

.dialog-close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: transparent;
    border: none;
    border-radius: var(--ios-border-radius-sm);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.dialog-close:hover {
    background: var(--ios-background-tertiary);
}

.dialog-close svg {
    width: 20px;
    height: 20px;
    fill: var(--ios-text-secondary);
}

.dialog-body {
    padding: var(--ios-spacing-lg);
    max-height: 60vh;
    overflow-y: auto;
}

.form-group {
    margin-bottom: var(--ios-spacing-lg);
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-label {
    display: block;
    font-size: var(--ios-font-size-sm);
    font-weight: var(--ios-font-weight-medium);
    color: var(--ios-text-primary);
    margin-bottom: var(--ios-spacing-xs);
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: var(--ios-spacing-sm);
    border: 1px solid var(--ios-separator);
    border-radius: var(--ios-border-radius-sm);
    background: var(--ios-background-primary);
    color: var(--ios-text-primary);
    font-size: var(--ios-font-size-md);
    font-family: inherit;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: var(--ios-blue);
    box-shadow: 0 0 0 2px var(--ios-blue-light);
}

.form-input {
    height: 44px;
}

.form-textarea {
    min-height: 80px;
    resize: vertical;
}

.form-select {
    height: 44px;
    cursor: pointer;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--ios-spacing-md);
    padding: var(--ios-spacing-lg);
    border-top: 1px solid var(--ios-separator);
    background: var(--ios-background-secondary);
}

.dialog-footer .btn-secondary,
.dialog-footer .btn-primary {
    min-height: 44px;
    min-width: 100px;
    padding: var(--ios-spacing-md) var(--ios-spacing-lg);
}

/* 移动端对话框适配 */
@media (max-width: 768px) {
    .template-save-dialog {
        padding: var(--ios-spacing-md);
    }

    .dialog-content {
        max-width: none;
        width: 100%;
    }

    .dialog-footer {
        flex-direction: column;
    }

    .dialog-footer .btn-secondary,
    .dialog-footer .btn-primary {
        width: 100%;
    }
}
