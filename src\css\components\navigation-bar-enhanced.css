/**
 * @file 重构后的导航栏样式
 * @description iOS Human Interface Guidelines风格的固定定位导航栏样式
 * <AUTHOR> Team
 * @version 1.0.0
 */

/* 导航栏容器 - 固定定位 */
.nav-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 64px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 1000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    transition: all 0.3s ease;
}

/* 导航栏内容容器 */
.nav-content-enhanced {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 20px;
    max-width: 1200px;
    margin: 0 auto;
}

/* 导航栏区域 */
.nav-left,
.nav-center,
.nav-right {
    display: flex;
    align-items: center;
}

.nav-left {
    flex: 0 0 auto;
    min-width: 60px;
}

.nav-center {
    flex: 1;
    justify-content: center;
    text-align: center;
    margin: 0 20px;
}

.nav-right {
    flex: 0 0 auto;
    gap: 8px;
}

/* 导航标题 */
.nav-title {
    font-size: 20px;
    font-weight: 700;
    color: #1c1c1e;
    margin: 0;
    line-height: 1.2;
    text-align: center;
}

/* 面包屑导航 */
.nav-breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #8e8e93;
    margin-top: 4px;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

.breadcrumb-separator {
    width: 12px;
    height: 12px;
    fill: #c7c7cc;
}

/* 导航按钮 */
.nav-button {
    width: 44px;
    height: 44px;
    border: none;
    background: transparent;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.nav-button:hover {
    background: rgba(0, 0, 0, 0.05);
    transform: scale(1.05);
}

.nav-button:active {
    transform: scale(0.95);
    background: rgba(0, 0, 0, 0.1);
}

/* 导航图标 */
.nav-icon {
    width: 24px;
    height: 24px;
    fill: #007aff;
    transition: fill 0.2s ease;
}

/* 返回按钮 */
.nav-back {
    background: rgba(0, 122, 255, 0.1);
}

.nav-back.nav-back-hidden {
    opacity: 0;
    pointer-events: none;
    transform: translateX(-20px);
}

.nav-back:hover {
    background: rgba(0, 122, 255, 0.2);
}

/* 时间段管理按钮 */
.nav-time-range {
    position: relative;
}

.nav-time-range.active {
    background: rgba(0, 122, 255, 0.1);
}

.nav-time-range.active .nav-icon {
    fill: #007aff;
}

/* 字段映射按钮 */
.nav-field-mapping .nav-icon {
    fill: #34c759;
}

.nav-field-mapping:hover {
    background: rgba(52, 199, 89, 0.1);
}

/* 主操作按钮 */
.nav-add {
    background: #007aff;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.nav-add .nav-icon {
    fill: white;
}

.nav-add:hover {
    background: #0056b3;
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4);
}

.nav-add:active {
    transform: scale(0.95);
}

/* 时间段状态指示器 */
.time-range-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: rgba(52, 199, 89, 0.1);
    border-radius: 16px;
    font-size: 12px;
    color: #34c759;
    font-weight: 600;
    margin-right: 8px;
}

.indicator-dot {
    width: 8px;
    height: 8px;
    background: #34c759;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.indicator-text {
    white-space: nowrap;
}

/* 时间段选择器 */
.time-range-selector {
    position: absolute;
    top: 100%;
    right: 20px;
    width: 400px;
    max-height: 500px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all 0.3s ease;
    overflow: hidden;
}

.time-range-selector.selector-visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* 选择器头部 */
.selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 16px;
    border-bottom: 1px solid #e5e5ea;
}

.selector-title {
    font-size: 18px;
    font-weight: 600;
    color: #1c1c1e;
    margin: 0;
}

.selector-close {
    width: 32px;
    height: 32px;
    border: none;
    background: #f2f2f7;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.selector-close:hover {
    background: #e5e5ea;
}

.selector-close svg {
    width: 16px;
    height: 16px;
    fill: #636366;
}

/* 选择器内容 */
.selector-content {
    padding: 16px 20px 20px;
    max-height: 400px;
    overflow-y: auto;
}

/* 区域标签 */
.section-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    color: #8e8e93;
    margin-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 激活时间段区域 */
.active-time-ranges {
    margin-bottom: 24px;
}

.active-ranges-list {
    background: #f2f2f7;
    border-radius: 12px;
    padding: 12px;
    min-height: 60px;
}

.no-active-ranges {
    text-align: center;
    color: #8e8e93;
    font-size: 14px;
    padding: 20px;
}

.active-range-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: white;
    border-radius: 8px;
    margin-bottom: 8px;
    border: 1px solid #e5e5ea;
}

.active-range-item:last-child {
    margin-bottom: 0;
}

.active-range-info {
    flex: 1;
}

.active-range-name {
    display: block;
    font-weight: 600;
    color: #1c1c1e;
    font-size: 14px;
    margin-bottom: 2px;
}

.active-range-time {
    display: block;
    font-size: 12px;
    color: #8e8e93;
}

.active-range-remove {
    width: 28px;
    height: 28px;
    border: none;
    background: #ff3b30;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.active-range-remove:hover {
    background: #d70015;
}

.active-range-remove svg {
    width: 14px;
    height: 14px;
    fill: white;
}

/* 可用时间段区域 */
.available-time-ranges {
    margin-bottom: 20px;
}

.btn-create-time-range {
    background: #007aff;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-create-time-range:hover {
    background: #0056b3;
}

.btn-create-time-range svg {
    width: 12px;
    height: 12px;
    fill: currentColor;
}

.available-ranges-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.empty-time-ranges {
    text-align: center;
    padding: 40px 20px;
    color: #8e8e93;
}

.empty-hint {
    font-size: 12px;
    margin-top: 4px;
}

/* 时间段项 */
.time-range-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: white;
    border: 2px solid #e5e5ea;
    border-radius: 12px;
    transition: all 0.2s ease;
}

.time-range-item:hover {
    border-color: #007aff;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
}

.time-range-item.active {
    border-color: #34c759;
    background: rgba(52, 199, 89, 0.05);
}

.range-info {
    flex: 1;
    margin-right: 12px;
}

.range-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.range-name {
    font-weight: 600;
    color: #1c1c1e;
    font-size: 16px;
}

.range-type-badge {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
}

.range-type-badge.preset {
    background: #e3f2fd;
    color: #1976d2;
}

.range-type-badge.custom {
    background: #f3e5f5;
    color: #7b1fa2;
}

.range-time {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    color: #636366;
    margin-bottom: 4px;
}

.time-separator {
    color: #c7c7cc;
}

.range-description {
    font-size: 12px;
    color: #8e8e93;
    line-height: 1.3;
}

.range-actions {
    display: flex;
    gap: 4px;
}

.range-action-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: #f2f2f7;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.range-action-btn:hover {
    background: #e5e5ea;
}

.range-action-btn svg {
    width: 18px;
    height: 18px;
    fill: #636366;
}

.range-action-btn.toggle-range.active {
    background: #34c759;
}

.range-action-btn.toggle-range.active svg {
    fill: white;
}

.range-action-btn.edit-range:hover {
    background: #007aff;
}

.range-action-btn.edit-range:hover svg {
    fill: white;
}

.range-action-btn.delete-range:hover {
    background: #ff3b30;
}

.range-action-btn.delete-range:hover svg {
    fill: white;
}

/* 快速操作 */
.quick-actions {
    display: flex;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid #e5e5ea;
}

.btn-secondary,
.btn-primary {
    flex: 1;
    padding: 12px 16px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    min-height: 44px;
}

.btn-secondary {
    background: #f2f2f7;
    color: #636366;
}

.btn-secondary:hover {
    background: #e5e5ea;
}

.btn-primary {
    background: #007aff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

/* 页面内容偏移 */
body {
    padding-top: 64px;
}

.main-content {
    padding-top: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-content-enhanced {
        padding: 0 16px;
    }
    
    .nav-center {
        margin: 0 12px;
    }
    
    .nav-title {
        font-size: 18px;
    }
    
    .nav-right {
        gap: 4px;
    }
    
    .nav-button {
        width: 40px;
        height: 40px;
    }
    
    .nav-icon {
        width: 20px;
        height: 20px;
    }
    
    .time-range-indicator {
        display: none; /* 移动端隐藏指示器 */
    }
    
    .time-range-selector {
        right: 16px;
        left: 16px;
        width: auto;
    }
    
    .selector-content {
        max-height: 300px;
    }
    
    .quick-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .nav-bar {
        height: 56px;
    }
    
    body {
        padding-top: 56px;
    }
    
    .nav-title {
        font-size: 16px;
    }
    
    .nav-button {
        width: 36px;
        height: 36px;
    }
    
    .nav-icon {
        width: 18px;
        height: 18px;
    }
}
