/**
 * 模板组件样式
 * iOS风格的模板选择和管理界面
 */

/* 模板选择器容器 */
.template-selector {
    background: #ffffff;
    border-radius: 12px;
    margin: 16px 0;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.template-selector-header {
    padding: 16px;
    border-bottom: 1px solid #e5e5ea;
    background: #f2f2f7;
}

.template-selector-title {
    font-size: 18px;
    font-weight: 600;
    color: #1c1c1e;
    margin: 0 0 8px 0;
}

.template-selector-subtitle {
    font-size: 14px;
    color: #8e8e93;
    margin: 0;
}

/* 模板搜索 */
.template-search {
    padding: 16px;
    border-bottom: 1px solid #e5e5ea;
}

.template-search-input {
    width: 100%;
    background: #f2f2f7;
    border: none;
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 16px;
    color: #1c1c1e;
    transition: background-color 0.2s ease;
}

.template-search-input:focus {
    outline: none;
    background: #e5e5ea;
}

.template-search-input::placeholder {
    color: #8e8e93;
}

/* 模板分类 */
.template-categories {
    display: flex;
    padding: 12px 16px;
    gap: 8px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-bottom: 1px solid #e5e5ea;
}

.template-category {
    background: #f2f2f7;
    border: none;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 14px;
    color: #8e8e93;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    flex-shrink: 0;
}

.template-category.active {
    background: #007aff;
    color: #ffffff;
}

.template-category:hover {
    background: #e5e5ea;
}

.template-category.active:hover {
    background: #0056cc;
}

/* 模板列表 */
.template-list {
    max-height: 400px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.template-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #e5e5ea;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.template-item:last-child {
    border-bottom: none;
}

.template-item:hover {
    background: #f2f2f7;
}

.template-item:active {
    background: #e5e5ea;
}

.template-item.selected {
    background: #e3f2fd;
}

.template-icon {
    width: 40px;
    height: 40px;
    background: #f2f2f7;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-right: 12px;
    flex-shrink: 0;
}

.template-content {
    flex: 1;
    min-width: 0;
}

.template-name {
    font-size: 16px;
    font-weight: 600;
    color: #1c1c1e;
    margin: 0 0 4px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.template-description {
    font-size: 14px;
    color: #8e8e93;
    margin: 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.template-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
    flex-shrink: 0;
}

.template-badge {
    background: #34c759;
    color: #ffffff;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.template-badge.builtin {
    background: #007aff;
}

.template-badge.user {
    background: #ff9500;
}

.template-match-score {
    font-size: 12px;
    color: #8e8e93;
}

/* 推荐模板 */
.template-recommendations {
    padding: 16px;
    background: #f2f2f7;
}

.template-recommendations-title {
    font-size: 16px;
    font-weight: 600;
    color: #1c1c1e;
    margin: 0 0 12px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.template-recommendations-icon {
    font-size: 18px;
}

.template-recommendations-list {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.template-recommendation-card {
    background: #ffffff;
    border-radius: 10px;
    padding: 12px;
    min-width: 200px;
    flex-shrink: 0;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.template-recommendation-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.template-recommendation-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.template-recommendation-icon {
    font-size: 16px;
}

.template-recommendation-name {
    font-size: 14px;
    font-weight: 600;
    color: #1c1c1e;
    margin: 0;
}

.template-recommendation-description {
    font-size: 12px;
    color: #8e8e93;
    margin: 0 0 8px 0;
    line-height: 1.3;
}

.template-recommendation-match {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #34c759;
}

/* 模板操作按钮 */
.template-actions {
    display: flex;
    gap: 8px;
    padding: 16px;
    border-top: 1px solid #e5e5ea;
    background: #f2f2f7;
}

.template-action-button {
    flex: 1;
    background: #007aff;
    color: #ffffff;
    border: none;
    border-radius: 10px;
    padding: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.template-action-button:hover {
    background: #0056cc;
}

.template-action-button:active {
    transform: scale(0.95);
}

.template-action-button.secondary {
    background: #f2f2f7;
    color: #007aff;
}

.template-action-button.secondary:hover {
    background: #e5e5ea;
}

/* 空状态 */
.template-empty {
    padding: 40px 20px;
    text-align: center;
    color: #8e8e93;
}

.template-empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.template-empty-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 8px 0;
}

.template-empty-description {
    font-size: 14px;
    margin: 0;
    line-height: 1.4;
}

/* 模板详情模态框 */
.template-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: 20px;
}

.template-modal-content {
    background: #ffffff;
    border-radius: 16px;
    max-width: 500px;
    width: 100%;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.template-modal-header {
    padding: 20px;
    border-bottom: 1px solid #e5e5ea;
}

.template-modal-title {
    font-size: 20px;
    font-weight: 600;
    color: #1c1c1e;
    margin: 0;
}

.template-modal-body {
    padding: 20px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.template-modal-footer {
    padding: 20px;
    border-top: 1px solid #e5e5ea;
    display: flex;
    gap: 12px;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .template-selector {
        margin: 12px 0;
    }
    
    .template-selector-header,
    .template-search,
    .template-item,
    .template-actions {
        padding: 12px;
    }
    
    .template-categories {
        padding: 8px 12px;
    }
    
    .template-recommendations {
        padding: 12px;
    }
    
    .template-recommendation-card {
        min-width: 160px;
    }
    
    .template-modal {
        padding: 12px;
    }
    
    .template-modal-content {
        max-height: 90vh;
    }
    
    .template-modal-header,
    .template-modal-body,
    .template-modal-footer {
        padding: 16px;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .template-selector {
        background: #1c1c1e;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    }
    
    .template-selector-header,
    .template-search,
    .template-recommendations,
    .template-actions {
        background: #2c2c2e;
        border-color: #3a3a3c;
    }
    
    .template-selector-title,
    .template-name,
    .template-recommendations-title {
        color: #ffffff;
    }
    
    .template-search-input {
        background: #3a3a3c;
        color: #ffffff;
    }
    
    .template-search-input::placeholder {
        color: #8e8e93;
    }
    
    .template-category {
        background: #3a3a3c;
        color: #8e8e93;
    }
    
    .template-category:hover {
        background: #48484a;
    }
    
    .template-item {
        border-color: #3a3a3c;
    }
    
    .template-item:hover {
        background: #2c2c2e;
    }
    
    .template-item.selected {
        background: #1a365d;
    }
    
    .template-icon {
        background: #3a3a3c;
    }
    
    .template-recommendation-card {
        background: #1c1c1e;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }
    
    .template-modal-content {
        background: #1c1c1e;
    }
    
    .template-modal-header,
    .template-modal-footer {
        border-color: #3a3a3c;
    }
    
    .template-modal-title {
        color: #ffffff;
    }
}
