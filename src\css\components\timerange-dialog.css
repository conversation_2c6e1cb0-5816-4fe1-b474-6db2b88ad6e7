/**
 * @file 时间段配置对话框样式
 * @description iOS Human Interface Guidelines风格的时间段配置对话框样式
 * <AUTHOR> Team
 * @version 1.0.0
 */

/* 对话框遮罩层 */
.timerange-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10001;
    opacity: 0;
    transition: opacity 0.3s ease;
    padding: 20px;
    box-sizing: border-box;
}

.dialog-visible {
    opacity: 1;
}

/* 对话框主体 */
.timerange-dialog {
    background: white;
    border-radius: 20px;
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transform: scale(0.9) translateY(20px);
    transition: transform 0.3s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
}

.dialog-visible .timerange-dialog {
    transform: scale(1) translateY(0);
}

/* 对话框头部 */
.dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 0 24px;
    border-bottom: 1px solid #e5e5ea;
    margin-bottom: 24px;
}

.dialog-title {
    font-size: 24px;
    font-weight: 700;
    color: #1c1c1e;
    margin: 0;
}

.dialog-close {
    width: 44px;
    height: 44px;
    border: none;
    background: #f2f2f7;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dialog-close:hover {
    background: #e5e5ea;
}

.dialog-close svg {
    width: 20px;
    height: 20px;
    fill: #636366;
}

/* 对话框内容 */
.dialog-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 24px;
}

/* 表单样式 */
.timerange-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-section {
    background: #f2f2f7;
    border-radius: 16px;
    padding: 20px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #1c1c1e;
    margin: 0 0 16px 0;
}

.form-group {
    margin-bottom: 16px;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-label {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #1c1c1e;
    margin-bottom: 8px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e5ea;
    border-radius: 12px;
    font-size: 16px;
    background: white;
    transition: all 0.2s ease;
    box-sizing: border-box;
    min-height: 44px;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: #007aff;
    box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

.form-checkbox {
    width: 20px;
    height: 20px;
    accent-color: #007aff;
}

.help-text {
    font-size: 12px;
    color: #8e8e93;
    margin-top: 4px;
    line-height: 1.3;
}

/* 时间输入区域 */
.time-inputs {
    display: flex;
    align-items: end;
    gap: 16px;
}

.time-input {
    font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
    font-size: 18px;
    text-align: center;
    letter-spacing: 1px;
}

.time-separator {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 44px;
    margin-bottom: 16px;
}

.time-separator svg {
    width: 20px;
    height: 20px;
    fill: #8e8e93;
}

/* 时间验证 */
.time-validation {
    margin-top: 12px;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
}

.time-validation.error {
    background: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
}

.time-validation.warning {
    background: #fff8e1;
    color: #f57c00;
    border: 1px solid #ffecb3;
}

.validation-message {
    margin: 0;
}

/* 预设按钮 */
.preset-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.preset-btn {
    background: white;
    color: #007aff;
    border: 2px solid #007aff;
    border-radius: 12px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 36px;
}

.preset-btn:hover {
    background: #007aff;
    color: white;
}

.preset-btn:active {
    transform: scale(0.95);
}

/* 颜色选择器 */
.color-picker {
    display: flex;
    align-items: center;
    gap: 12px;
}

.color-input {
    width: 44px;
    height: 44px;
    border: 2px solid #e5e5ea;
    border-radius: 12px;
    cursor: pointer;
    background: none;
    padding: 0;
}

.color-input::-webkit-color-swatch-wrapper {
    padding: 0;
    border: none;
    border-radius: 8px;
    overflow: hidden;
}

.color-input::-webkit-color-swatch {
    border: none;
    border-radius: 8px;
}

.color-presets {
    display: flex;
    gap: 8px;
}

.color-preset {
    width: 32px;
    height: 32px;
    border: 2px solid #e5e5ea;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.color-preset:hover {
    transform: scale(1.1);
    border-color: #007aff;
}

/* 预览区域 */
.timerange-preview {
    background: white;
    border-radius: 12px;
    padding: 16px;
    border: 1px solid #e5e5ea;
}

.preview-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.preview-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #007aff;
    flex-shrink: 0;
}

.preview-info {
    flex: 1;
}

.preview-name {
    font-weight: 600;
    color: #1c1c1e;
    font-size: 16px;
    margin-bottom: 2px;
}

.preview-time {
    font-size: 14px;
    color: #8e8e93;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
}

/* 对话框底部 */
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 24px;
    border-top: 1px solid #e5e5ea;
    background: #f2f2f7;
    border-radius: 0 0 20px 20px;
}

.btn-secondary,
.btn-primary {
    padding: 12px 24px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    min-height: 44px;
}

.btn-secondary {
    background: white;
    color: #636366;
    border: 2px solid #e5e5ea;
}

.btn-secondary:hover {
    background: #f2f2f7;
}

.btn-primary {
    background: #007aff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .timerange-dialog {
        margin: 10px;
        max-height: 95vh;
    }
    
    .dialog-header,
    .dialog-content,
    .dialog-footer {
        padding-left: 16px;
        padding-right: 16px;
    }
    
    .time-inputs {
        flex-direction: column;
        gap: 12px;
    }
    
    .time-separator {
        transform: rotate(90deg);
        margin: 8px 0;
    }
    
    .preset-buttons {
        justify-content: center;
    }
    
    .color-picker {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .dialog-footer {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .timerange-dialog {
        border-radius: 16px;
    }
    
    .dialog-title {
        font-size: 20px;
    }
    
    .form-section {
        padding: 16px;
    }
    
    .section-title {
        font-size: 16px;
    }
    
    .preset-buttons {
        gap: 6px;
    }
    
    .preset-btn {
        font-size: 12px;
        padding: 6px 12px;
    }
}

/* 防止页面滚动 */
body.dialog-open {
    overflow: hidden;
}
