/**
 * @file Toast 提示组件样式
 * @description iOS风格的轻量级提示消息组件
 * <AUTHOR> Team
 */

/* Toast 容器 */
.toast-container {
    position: fixed;
    top: env(safe-area-inset-top, 20px);
    left: 0;
    right: 0;
    z-index: 10000;
    pointer-events: none;
    padding: var(--spacing-md);
}

/* Toast 消息 */
.ios-toast {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-medium);
    font-size: var(--font-size-subhead);
    font-weight: 500;
    margin: 0 auto var(--spacing-sm);
    max-width: 320px;
    transform: translateY(-100px);
    opacity: 0;
    transition: all var(--animation-normal) cubic-bezier(0.4, 0.0, 0.2, 1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    pointer-events: auto;
    box-shadow: var(--shadow-medium);
}

.ios-toast.show {
    transform: translateY(0);
    opacity: 1;
}

/* Toast 图标 */
.toast-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    fill: currentColor;
}

/* Toast 变体 */
.ios-toast.success {
    background-color: rgba(52, 199, 89, 0.9);
}

.ios-toast.error {
    background-color: rgba(255, 59, 48, 0.9);
}

.ios-toast.warning {
    background-color: rgba(255, 149, 0, 0.9);
}

.ios-toast.info {
    background-color: rgba(0, 122, 255, 0.9);
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .ios-toast {
        background-color: rgba(28, 28, 30, 0.9);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .ios-toast.success {
        background-color: rgba(52, 199, 89, 0.9);
    }
    
    .ios-toast.error {
        background-color: rgba(255, 59, 48, 0.9);
    }
    
    .ios-toast.warning {
        background-color: rgba(255, 149, 0, 0.9);
    }
    
    .ios-toast.info {
        background-color: rgba(0, 122, 255, 0.9);
    }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
    .ios-toast {
        transition: opacity var(--animation-fast) ease;
        transform: none !important;
    }
    
    .ios-toast.show {
        transform: none;
    }
}

/* 移动端优化 */
@media (max-width: 375px) {
    .toast-container {
        padding: var(--spacing-sm);
    }
    
    .ios-toast {
        max-width: 100%;
        font-size: var(--font-size-footnote);
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .toast-icon {
        width: 16px;
        height: 16px;
    }
}
