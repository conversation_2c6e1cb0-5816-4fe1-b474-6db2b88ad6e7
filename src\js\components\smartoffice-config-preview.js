/**
 * @file SmartOffice配置预览组件
 * @description 在文件上传主页显示已保存配置的预览列表
 * <AUTHOR> Team
 */

/**
 * @function ConfigPreviewComponent
 * @description 配置预览组件构造函数
 * @param {HTMLElement} container - 容器元素
 * @constructor
 */
function ConfigPreviewComponent(container) {
    /**
     * @property {HTMLElement} container - 组件容器
     */
    this.container = container;
    
    /**
     * @property {Array} configs - 配置列表
     */
    this.configs = [];
    
    /**
     * @property {Object} eventBus - 事件总线引用
     */
    this.eventBus = SmartOffice.Core.EventBus;
    
    /**
     * @property {Object} configManager - 配置管理器引用
     */
    this.configManager = SmartOffice.Data.ConfigManager;
    
    /**
     * @property {Object} helpers - 工具函数引用
     */
    this.helpers = SmartOffice.Utils.Helpers;
    
    /**
     * @property {Object} dom - DOM工具引用
     */
    this.dom = SmartOffice.Utils.DOM;
    
    // 初始化组件
    this.init();
    
    SmartOffice.log('info', 'ConfigPreview组件实例创建完成');
}

/**
 * @function ConfigPreviewComponent.prototype.init
 * @description 初始化组件
 */
ConfigPreviewComponent.prototype.init = function() {
    // 绑定事件监听器
    this.bindEvents();
    
    // 加载配置数据
    this.loadConfigs();
    
    // 渲染组件
    this.render();
    
    SmartOffice.log('info', 'ConfigPreview组件初始化完成');
};

/**
 * @function ConfigPreviewComponent.prototype.bindEvents
 * @description 绑定事件监听器
 */
ConfigPreviewComponent.prototype.bindEvents = function() {
    const self = this;
    
    // 监听配置更新事件
    this.eventBus.on(SmartOffice.Events.CONFIG_SAVED, function() {
        self.loadConfigs();
        self.render();
    });
    
    this.eventBus.on(SmartOffice.Events.CONFIG_DELETED, function() {
        self.loadConfigs();
        self.render();
    });
    
    // 监听配置预览刷新事件
    this.eventBus.on('configPreview:refresh', function() {
        self.loadConfigs();
        self.render();
    });
};

/**
 * @function ConfigPreviewComponent.prototype.loadConfigs
 * @description 加载配置数据
 */
ConfigPreviewComponent.prototype.loadConfigs = function() {
    try {
        this.configs = this.configManager.getAllConfigs();
        SmartOffice.log('info', '配置预览数据加载完成，共' + this.configs.length + '个配置');
    } catch (error) {
        SmartOffice.log('error', '配置预览数据加载失败:', error);
        this.configs = [];
    }
};

/**
 * @function ConfigPreviewComponent.prototype.render
 * @description 渲染组件
 */
ConfigPreviewComponent.prototype.render = function() {
    if (!this.container) {
        SmartOffice.log('error', 'ConfigPreview容器不存在');
        return;
    }
    
    // 清空容器
    this.container.innerHTML = '';
    
    if (this.configs.length === 0) {
        this.renderEmptyState();
    } else {
        this.renderConfigList();
    }
};

/**
 * @function ConfigPreviewComponent.prototype.renderEmptyState
 * @description 渲染空状态
 */
ConfigPreviewComponent.prototype.renderEmptyState = function() {
    const emptyState = this.dom.createElement('div', {
        className: 'empty-configs-state'
    });
    
    emptyState.innerHTML = `
        <div class="empty-icon">
            <svg viewBox="0 0 24 24">
                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
            </svg>
        </div>
        <p class="empty-description">还没有保存的透视表配置<br>上传文件后可以创建配置</p>
    `;
    
    this.container.appendChild(emptyState);
};

/**
 * @function ConfigPreviewComponent.prototype.renderConfigList
 * @description 渲染配置列表
 */
ConfigPreviewComponent.prototype.renderConfigList = function() {
    const self = this;
    
    this.configs.forEach(function(config) {
        const configCard = self.createConfigCard(config);
        self.container.appendChild(configCard);
    });
};

/**
 * @function ConfigPreviewComponent.prototype.createConfigCard
 * @description 创建配置卡片
 * @param {Object} config - 配置对象
 * @returns {HTMLElement} 配置卡片元素
 */
ConfigPreviewComponent.prototype.createConfigCard = function(config) {
    const self = this;
    
    const card = this.dom.createElement('div', {
        className: 'config-preview-card'
    });
    
    // 格式化日期
    const formattedDate = this.helpers.formatDate(new Date(config.createdAt));
    
    // 统计字段数量
    const fieldCount = (config.rowFields || []).length + 
                      (config.columnFields || []).length + 
                      (config.valueFields || []).length;
    
    card.innerHTML = `
        <div class="config-preview-header">
            <h4 class="config-preview-name">${this.helpers.escapeHtml(config.name)}</h4>
            <div class="config-preview-actions">
                <button type="button" class="config-preview-btn edit-btn" title="编辑配置" data-config-id="${config.id}">
                    <svg viewBox="0 0 24 24">
                        <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                    </svg>
                </button>
                <button type="button" class="config-preview-btn delete-btn" title="删除配置" data-config-id="${config.id}">
                    <svg viewBox="0 0 24 24">
                        <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                    </svg>
                </button>
            </div>
        </div>
        <p class="config-preview-description">${this.helpers.escapeHtml(config.description || '无描述')}</p>
        <div class="config-preview-meta">
            <span class="config-preview-date">
                <svg viewBox="0 0 24 24" style="width: 12px; height: 12px; fill: currentColor;">
                    <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                </svg>
                ${formattedDate}
            </span>
            <span class="config-preview-fields">
                <svg viewBox="0 0 24 24" style="width: 12px; height: 12px; fill: currentColor;">
                    <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                </svg>
                ${fieldCount} 个字段
            </span>
        </div>
    `;
    
    // 绑定编辑按钮事件
    const editBtn = card.querySelector('.edit-btn');
    if (editBtn) {
        editBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            self.handleEditConfig(config);
        });
    }
    
    // 绑定删除按钮事件
    const deleteBtn = card.querySelector('.delete-btn');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            self.handleDeleteConfig(config);
        });
    }
    
    // 绑定卡片点击事件（编辑配置）
    card.addEventListener('click', function() {
        self.handleEditConfig(config);
    });
    
    return card;
};

/**
 * @function ConfigPreviewComponent.prototype.handleEditConfig
 * @description 处理编辑配置
 * @param {Object} config - 配置对象
 */
ConfigPreviewComponent.prototype.handleEditConfig = function(config) {
    // 触发触觉反馈
    SmartOffice.triggerHapticFeedback('light');
    
    // 触发配置选择事件
    this.eventBus.emit(SmartOffice.Events.CONFIG_SELECT, config);
    
    SmartOffice.log('info', '编辑配置:', config.name);
};

/**
 * @function ConfigPreviewComponent.prototype.handleDeleteConfig
 * @description 处理删除配置
 * @param {Object} config - 配置对象
 */
ConfigPreviewComponent.prototype.handleDeleteConfig = function(config) {
    const self = this;
    
    // 触发触觉反馈
    SmartOffice.triggerHapticFeedback('medium');
    
    // 显示确认对话框
    if (confirm('确定要删除配置 "' + config.name + '" 吗？此操作无法撤销。')) {
        try {
            // 删除配置
            this.configManager.deleteConfig(config.id);
            
            // 触发删除事件
            this.eventBus.emit(SmartOffice.Events.CONFIG_DELETED, config.id);
            
            // 显示成功提示
            this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '配置已删除', 'success');
            
            SmartOffice.log('info', '配置删除成功:', config.name);
            
        } catch (error) {
            SmartOffice.log('error', '配置删除失败:', error);
            this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '删除失败：' + error.message, 'error');
        }
    }
};

// 注册到全局命名空间
SmartOffice.Components.ConfigPreview = ConfigPreviewComponent;

SmartOffice.log('info', 'SmartOffice配置预览组件模块初始化完成');
