/**
 * @file SmartOffice下拉菜单组件
 * @description iOS风格的下拉选择器组件，支持单选和多选
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function DropdownComponent
 * @description 下拉菜单组件构造函数
 * @constructor
 * @param {Object} options - 配置选项
 * @param {string} options.containerId - 容器元素ID
 * @param {Array} options.items - 选项数据数组
 * @param {boolean} options.multiple - 是否支持多选（默认false）
 * @param {string} options.placeholder - 占位符文本
 * @param {Function} options.onSelect - 选择回调函数
 * @param {Function} options.onOpen - 打开回调函数
 * @param {Function} options.onClose - 关闭回调函数
 */
function DropdownComponent(options) {
    // 默认配置
    this.options = SmartOffice.Utils.Helpers.extend({
        containerId: 'dropdownContainer',
        items: [],
        multiple: false,
        placeholder: '请选择',
        searchable: false,
        maxHeight: 300,
        onSelect: null,
        onOpen: null,
        onClose: null
    }, options || {});

    // 组件状态
    this.isOpen = false;
    this.selectedItems = [];
    this.filteredItems = [];
    this.searchTerm = '';

    // DOM元素引用
    this.containerElement = null;
    this.buttonElement = null;
    this.dropdownElement = null;
    this.searchElement = null;
    this.listElement = null;
    this.backdropElement = null;

    // 依赖注入
    this.dom = SmartOffice.Utils.DOM;
    this.helpers = SmartOffice.Utils.Helpers;
    this.eventBus = SmartOffice.Core.EventBus;

    SmartOffice.log('info', 'DropdownComponent初始化完成');
}

/**
 * @function DropdownComponent.prototype.init
 * @description 初始化下拉菜单组件
 * @returns {boolean} 初始化是否成功
 */
DropdownComponent.prototype.init = function() {
    try {
        // 获取容器元素
        this.containerElement = document.getElementById(this.options.containerId);
        if (!this.containerElement) {
            throw new Error('找不到下拉菜单容器元素: ' + this.options.containerId);
        }

        // 初始化数据
        this.filteredItems = [...this.options.items];

        // 渲染组件界面
        this.render();

        // 绑定事件监听器
        this.bindEvents();

        SmartOffice.log('info', '下拉菜单组件初始化成功');
        return true;
    } catch (error) {
        SmartOffice.log('error', '下拉菜单组件初始化失败:', error);
        return false;
    }
};

/**
 * @function DropdownComponent.prototype.render
 * @description 渲染下拉菜单界面
 */
DropdownComponent.prototype.render = function() {
    const html = `
        <div class="so-dropdown">
            <!-- 下拉按钮 -->
            <button type="button" class="so-dropdown-button" id="dropdownButton">
                <span class="so-dropdown-text" id="dropdownText">${this.getDisplayText()}</span>
                <span class="so-dropdown-arrow">
                    <svg viewBox="0 0 24 24" class="arrow-icon">
                        <path d="M7 10l5 5 5-5z"/>
                    </svg>
                </span>
            </button>

            <!-- 下拉面板 -->
            <div class="so-dropdown-panel" id="dropdownPanel" style="display: none;">
                ${this.options.searchable ? this.renderSearchBox() : ''}
                <div class="so-dropdown-list" id="dropdownList">
                    ${this.renderItems()}
                </div>
            </div>
        </div>

        <!-- 背景遮罩 -->
        <div class="so-dropdown-backdrop" id="dropdownBackdrop" style="display: none;"></div>
    `;

    this.containerElement.innerHTML = html;

    // 获取DOM元素引用
    this.buttonElement = document.getElementById('dropdownButton');
    this.dropdownElement = document.getElementById('dropdownPanel');
    this.listElement = document.getElementById('dropdownList');
    this.backdropElement = document.getElementById('dropdownBackdrop');

    if (this.options.searchable) {
        this.searchElement = document.getElementById('dropdownSearch');
    }
};

/**
 * @function DropdownComponent.prototype.renderSearchBox
 * @description 渲染搜索框
 * @returns {string} 搜索框HTML
 */
DropdownComponent.prototype.renderSearchBox = function() {
    return `
        <div class="so-dropdown-search">
            <input type="text"
                   id="dropdownSearch"
                   class="so-dropdown-search-input"
                   placeholder="搜索选项..."
                   autocomplete="off">
        </div>
    `;
};

/**
 * @function DropdownComponent.prototype.renderItems
 * @description 渲染选项列表
 * @returns {string} 选项列表HTML
 */
DropdownComponent.prototype.renderItems = function() {
    if (this.filteredItems.length === 0) {
        return '<div class="so-dropdown-empty">暂无选项</div>';
    }

    let html = '';
    for (let i = 0; i < this.filteredItems.length; i++) {
        const item = this.filteredItems[i];
        const isSelected = this.isItemSelected(item);

        html += `
            <div class="so-dropdown-item ${isSelected ? 'selected' : ''}"
                 data-value="${this.helpers.escapeHtml(item.value)}"
                 data-index="${i}">
                ${this.options.multiple ? this.renderCheckbox(isSelected) : ''}
                <span class="so-dropdown-item-text">${this.helpers.escapeHtml(item.label)}</span>
                ${isSelected && !this.options.multiple ? '<span class="so-dropdown-check">✓</span>' : ''}
            </div>
        `;
    }

    return html;
};

/**
 * @function DropdownComponent.prototype.renderCheckbox
 * @description 渲染复选框
 * @param {boolean} checked - 是否选中
 * @returns {string} 复选框HTML
 */
DropdownComponent.prototype.renderCheckbox = function(checked) {
    return `
        <span class="so-dropdown-checkbox ${checked ? 'checked' : ''}">
            <svg viewBox="0 0 24 24" class="checkbox-icon">
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
            </svg>
        </span>
    `;
};

/**
 * @function DropdownComponent.prototype.bindEvents
 * @description 绑定事件监听器
 */
DropdownComponent.prototype.bindEvents = function() {
    const self = this;

    // 下拉按钮点击事件
    if (this.buttonElement) {
        this.buttonElement.addEventListener('click', function(event) {
            event.preventDefault();
            event.stopPropagation();
            self.toggle();
        });

        // 添加触摸反馈
        this.dom.addTouchFeedback(this.buttonElement, 'light');
    }

    // 背景遮罩点击事件
    if (this.backdropElement) {
        this.backdropElement.addEventListener('click', function() {
            self.close();
        });
    }

    // 选项点击事件
    if (this.listElement) {
        this.listElement.addEventListener('click', function(event) {
            const item = event.target.closest('.so-dropdown-item');
            if (item) {
                event.preventDefault();
                event.stopPropagation();

                const value = item.dataset.value;
                const index = parseInt(item.dataset.index);
                self.selectItem(self.filteredItems[index]);
            }
        });
    }

    // 搜索框事件
    if (this.searchElement) {
        this.searchElement.addEventListener('input', function() {
            self.handleSearch(this.value);
        });

        this.searchElement.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                self.close();
            }
        });
    }

    // 键盘事件
    document.addEventListener('keydown', function(event) {
        if (self.isOpen) {
            if (event.key === 'Escape') {
                self.close();
            }
        }
    });

    // 点击外部关闭
    document.addEventListener('click', function(event) {
        if (self.isOpen && !self.containerElement.contains(event.target)) {
            self.close();
        }
    });
};

/**
 * @function DropdownComponent.prototype.toggle
 * @description 切换下拉菜单开关状态
 */
DropdownComponent.prototype.toggle = function() {
    if (this.isOpen) {
        this.close();
    } else {
        this.open();
    }
};

/**
 * @function DropdownComponent.prototype.open
 * @description 打开下拉菜单
 */
DropdownComponent.prototype.open = function() {
    if (this.isOpen) return;

    this.isOpen = true;

    // 显示下拉面板和背景遮罩
    this.dom.setStyle(this.dropdownElement, { display: 'block' });
    this.dom.setStyle(this.backdropElement, { display: 'block' });

    // 添加打开状态类
    this.dom.addClass(this.buttonElement, 'open');
    this.dom.addClass(this.containerElement, 'so-dropdown-open');

    // 设置面板位置
    this.positionPanel();

    // 聚焦搜索框
    if (this.searchElement) {
        setTimeout(() => {
            this.searchElement.focus();
        }, 100);
    }

    // 触发打开事件
    if (this.options.onOpen) {
        this.options.onOpen();
    }

    this.eventBus.emit(SmartOffice.Events.DROPDOWN_OPEN, {
        dropdown: this
    });

    SmartOffice.log('info', '下拉菜单已打开');
};

/**
 * @function DropdownComponent.prototype.close
 * @description 关闭下拉菜单
 */
DropdownComponent.prototype.close = function() {
    if (!this.isOpen) return;

    this.isOpen = false;

    // 隐藏下拉面板和背景遮罩
    this.dom.setStyle(this.dropdownElement, { display: 'none' });
    this.dom.setStyle(this.backdropElement, { display: 'none' });

    // 移除打开状态类
    this.dom.removeClass(this.buttonElement, 'open');
    this.dom.removeClass(this.containerElement, 'so-dropdown-open');

    // 清空搜索
    if (this.searchElement) {
        this.searchElement.value = '';
        this.searchTerm = '';
        this.filteredItems = [...this.options.items];
    }

    // 触发关闭事件
    if (this.options.onClose) {
        this.options.onClose();
    }

    this.eventBus.emit(SmartOffice.Events.DROPDOWN_CLOSE, {
        dropdown: this
    });

    SmartOffice.log('info', '下拉菜单已关闭');
};

/**
 * @function DropdownComponent.prototype.selectItem
 * @description 选择选项
 * @param {Object} item - 选项数据
 */
DropdownComponent.prototype.selectItem = function(item) {
    if (!item) return;

    if (this.options.multiple) {
        // 多选模式
        const index = this.selectedItems.findIndex(selected => selected.value === item.value);
        if (index > -1) {
            // 取消选择
            this.selectedItems.splice(index, 1);
        } else {
            // 添加选择
            this.selectedItems.push(item);
        }
    } else {
        // 单选模式
        this.selectedItems = [item];
        this.close();
    }

    // 更新显示
    this.updateDisplay();
    this.updateItemStates();

    // 触发选择事件
    if (this.options.onSelect) {
        this.options.onSelect(this.options.multiple ? this.selectedItems : item, this);
    }

    this.eventBus.emit(SmartOffice.Events.DROPDOWN_SELECT, {
        dropdown: this,
        selected: this.options.multiple ? this.selectedItems : item
    });

    SmartOffice.log('info', '下拉菜单选项已选择:', item);
};

/**
 * @function DropdownComponent.prototype.handleSearch
 * @description 处理搜索
 * @param {string} searchTerm - 搜索词
 */
DropdownComponent.prototype.handleSearch = function(searchTerm) {
    this.searchTerm = searchTerm.toLowerCase();

    // 过滤选项
    if (this.searchTerm) {
        this.filteredItems = this.options.items.filter(item =>
            item.label.toLowerCase().includes(this.searchTerm) ||
            item.value.toLowerCase().includes(this.searchTerm)
        );
    } else {
        this.filteredItems = [...this.options.items];
    }

    // 重新渲染列表
    this.listElement.innerHTML = this.renderItems();
};

/**
 * @function DropdownComponent.prototype.positionPanel
 * @description 设置下拉面板位置
 */
DropdownComponent.prototype.positionPanel = function() {
    if (!this.dropdownElement || !this.buttonElement) return;

    const buttonRect = this.buttonElement.getBoundingClientRect();
    const panelHeight = Math.min(this.options.maxHeight, this.filteredItems.length * 44 + 20);
    const viewportHeight = window.innerHeight;

    // 计算是否有足够空间向下展开
    const spaceBelow = viewportHeight - buttonRect.bottom;
    const spaceAbove = buttonRect.top;

    if (spaceBelow >= panelHeight || spaceBelow >= spaceAbove) {
        // 向下展开
        this.dom.setStyle(this.dropdownElement, {
            top: '100%',
            bottom: 'auto',
            maxHeight: Math.min(this.options.maxHeight, spaceBelow - 10) + 'px'
        });
        this.dom.removeClass(this.dropdownElement, 'so-dropdown-panel-up');
    } else {
        // 向上展开
        this.dom.setStyle(this.dropdownElement, {
            top: 'auto',
            bottom: '100%',
            maxHeight: Math.min(this.options.maxHeight, spaceAbove - 10) + 'px'
        });
        this.dom.addClass(this.dropdownElement, 'so-dropdown-panel-up');
    }
};

/**
 * @function DropdownComponent.prototype.updateDisplay
 * @description 更新显示文本
 */
DropdownComponent.prototype.updateDisplay = function() {
    const textElement = document.getElementById('dropdownText');
    if (textElement) {
        textElement.textContent = this.getDisplayText();
    }
};

/**
 * @function DropdownComponent.prototype.getDisplayText
 * @description 获取显示文本
 * @returns {string} 显示文本
 */
DropdownComponent.prototype.getDisplayText = function() {
    if (this.selectedItems.length === 0) {
        return this.options.placeholder;
    }

    if (this.options.multiple) {
        if (this.selectedItems.length === 1) {
            return this.selectedItems[0].label;
        } else {
            return `已选择 ${this.selectedItems.length} 项`;
        }
    } else {
        return this.selectedItems[0].label;
    }
};

/**
 * @function DropdownComponent.prototype.updateItemStates
 * @description 更新选项状态
 */
DropdownComponent.prototype.updateItemStates = function() {
    const items = this.listElement.querySelectorAll('.so-dropdown-item');
    for (let i = 0; i < items.length; i++) {
        const item = items[i];
        const value = item.dataset.value;
        const isSelected = this.selectedItems.some(selected => selected.value === value);

        if (isSelected) {
            this.dom.addClass(item, 'selected');
        } else {
            this.dom.removeClass(item, 'selected');
        }

        // 更新复选框状态
        if (this.options.multiple) {
            const checkbox = item.querySelector('.so-dropdown-checkbox');
            if (checkbox) {
                if (isSelected) {
                    this.dom.addClass(checkbox, 'checked');
                } else {
                    this.dom.removeClass(checkbox, 'checked');
                }
            }
        }
    }
};

/**
 * @function DropdownComponent.prototype.isItemSelected
 * @description 检查选项是否已选择
 * @param {Object} item - 选项数据
 * @returns {boolean} 是否已选择
 */
DropdownComponent.prototype.isItemSelected = function(item) {
    return this.selectedItems.some(selected => selected.value === item.value);
};

/**
 * @function DropdownComponent.prototype.setItems
 * @description 设置选项数据
 * @param {Array} items - 选项数组
 */
DropdownComponent.prototype.setItems = function(items) {
    this.options.items = items || [];
    this.filteredItems = [...this.options.items];

    // 清除无效的选择
    this.selectedItems = this.selectedItems.filter(selected =>
        this.options.items.some(item => item.value === selected.value)
    );

    // 重新渲染
    if (this.listElement) {
        this.listElement.innerHTML = this.renderItems();
    }

    this.updateDisplay();
};

/**
 * @function DropdownComponent.prototype.getSelectedItems
 * @description 获取选中的选项
 * @returns {Array|Object} 选中的选项
 */
DropdownComponent.prototype.getSelectedItems = function() {
    return this.options.multiple ? this.selectedItems : (this.selectedItems[0] || null);
};

/**
 * @function DropdownComponent.prototype.setSelectedItems
 * @description 设置选中的选项
 * @param {Array|Object} items - 要选中的选项
 */
DropdownComponent.prototype.setSelectedItems = function(items) {
    if (this.options.multiple) {
        this.selectedItems = Array.isArray(items) ? items : [];
    } else {
        this.selectedItems = items ? [items] : [];
    }

    this.updateDisplay();
    this.updateItemStates();
};

/**
 * @function DropdownComponent.prototype.clearSelection
 * @description 清空选择
 */
DropdownComponent.prototype.clearSelection = function() {
    this.selectedItems = [];
    this.updateDisplay();
    this.updateItemStates();
};

/**
 * @function DropdownComponent.prototype.destroy
 * @description 销毁组件
 */
DropdownComponent.prototype.destroy = function() {
    try {
        // 关闭下拉菜单
        if (this.isOpen) {
            this.close();
        }

        // 移除事件监听器
        if (this.buttonElement) {
            this.buttonElement.removeEventListener('click', this.toggle);
        }

        if (this.backdropElement) {
            this.backdropElement.removeEventListener('click', this.close);
        }

        // 清空容器
        if (this.containerElement) {
            this.containerElement.innerHTML = '';
        }

        // 清理引用
        this.containerElement = null;
        this.buttonElement = null;
        this.dropdownElement = null;
        this.searchElement = null;
        this.listElement = null;
        this.backdropElement = null;
        this.selectedItems = [];
        this.filteredItems = [];

        SmartOffice.log('info', '下拉菜单组件已销毁');

    } catch (error) {
        SmartOffice.log('error', '下拉菜单组件销毁失败:', error);
    }
};

/**
 * @function DropdownComponent.prototype.isOpen
 * @description 检查下拉菜单是否打开
 * @returns {boolean} 是否打开
 */
DropdownComponent.prototype.isDropdownOpen = function() {
    return this.isOpen;
};

/**
 * @function DropdownComponent.prototype.refresh
 * @description 刷新组件
 */
DropdownComponent.prototype.refresh = function() {
    if (this.listElement) {
        this.listElement.innerHTML = this.renderItems();
    }
    this.updateDisplay();
    this.updateItemStates();
};

// 注册到全局命名空间
SmartOffice.Components.Dropdown = DropdownComponent;

SmartOffice.log('info', 'SmartOffice下拉菜单组件模块初始化完成');
