/**
 * @file SmartOffice字段映射管理页面组件
 * @description 提供字段映射模板的列表展示、增删改查操作界面
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function FieldMappingPageComponent
 * @description 字段映射管理页面组件构造函数
 * @constructor
 * @param {Object} options - 组件配置选项
 */
function FieldMappingPageComponent(options) {
    /**
     * @property {Object} options - 组件配置
     */
    this.options = Object.assign({
        containerId: 'fieldMappingPage',
        showEmptyState: true,
        enableSearch: true,
        enableSort: true,
        itemsPerPage: 10
    }, options || {});

    /**
     * @property {HTMLElement} container - 容器元素
     */
    this.container = null;

    /**
     * @property {Object} fieldMapper - 字段映射器引用
     */
    this.fieldMapper = SmartOffice.Data.FieldMapper;

    /**
     * @property {Object} storage - 存储管理器引用
     */
    this.storage = SmartOffice.Core.Storage;

    /**
     * @property {Object} eventBus - 事件总线引用
     */
    this.eventBus = SmartOffice.Core.EventBus;

    /**
     * @property {Array} mappingTemplates - 映射模板列表
     */
    this.mappingTemplates = [];

    /**
     * @property {string} searchQuery - 搜索查询
     */
    this.searchQuery = '';

    /**
     * @property {string} sortBy - 排序字段
     */
    this.sortBy = 'updatedAt';

    /**
     * @property {string} sortOrder - 排序顺序
     */
    this.sortOrder = 'desc';

    /**
     * @property {number} currentPage - 当前页码
     */
    this.currentPage = 1;

    /**
     * @property {Object} mappingDialog - 映射配置对话框引用
     */
    this.mappingDialog = null;

    SmartOffice.log('info', 'FieldMappingPageComponent字段映射管理页面组件初始化完成');
}

/**
 * @function FieldMappingPageComponent.prototype.init
 * @description 初始化组件
 */
FieldMappingPageComponent.prototype.init = function() {
    try {
        this.container = document.getElementById(this.options.containerId);
        if (!this.container) {
            throw new Error('未找到容器元素: ' + this.options.containerId);
        }

        // 渲染页面结构
        this.render();

        // 绑定事件
        this.bindEvents();

        // 加载映射模板数据
        this.loadMappingTemplates();

        SmartOffice.log('info', '字段映射管理页面组件初始化成功');
    } catch (error) {
        SmartOffice.log('error', '字段映射管理页面组件初始化失败:', error);
    }
};

/**
 * @function FieldMappingPageComponent.prototype.render
 * @description 渲染页面结构
 */
FieldMappingPageComponent.prototype.render = function() {
    const html = `
        <div class="field-mapping-page">
            <!-- 页面头部 -->
            <div class="page-header">
                <div class="header-content">
                    <h2 class="page-title">字段映射管理</h2>
                    <p class="page-description">管理和配置字段映射模板，提高数据处理效率</p>
                </div>
                <button type="button" class="btn-primary btn-create-mapping" id="createMappingBtn">
                    <svg class="btn-icon" viewBox="0 0 24 24">
                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                    </svg>
                    新建映射模板
                </button>
            </div>

            <!-- 搜索和筛选栏 -->
            <div class="search-filter-bar" id="searchFilterBar">
                <div class="search-container">
                    <div class="search-input-wrapper">
                        <svg class="search-icon" viewBox="0 0 24 24">
                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        </svg>
                        <input type="text" class="search-input" id="searchInput" placeholder="搜索映射模板..." />
                        <button type="button" class="search-clear" id="searchClear" style="display: none;">
                            <svg viewBox="0 0 24 24">
                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                            </svg>
                        </button>
                    </div>
                </div>
                
                <div class="filter-container">
                    <div class="sort-dropdown">
                        <button type="button" class="sort-btn" id="sortBtn">
                            <svg class="sort-icon" viewBox="0 0 24 24">
                                <path d="M3 18h6v-2H3v2zM3 6v2h18V6H3zm0 7h12v-2H3v2z"/>
                            </svg>
                            排序
                        </button>
                        <div class="sort-menu" id="sortMenu">
                            <div class="sort-option" data-sort="name" data-order="asc">名称 A-Z</div>
                            <div class="sort-option" data-sort="name" data-order="desc">名称 Z-A</div>
                            <div class="sort-option" data-sort="updatedAt" data-order="desc">最近更新</div>
                            <div class="sort-option" data-sort="createdAt" data-order="desc">最近创建</div>
                            <div class="sort-option" data-sort="usageCount" data-order="desc">使用次数</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 映射模板列表 -->
            <div class="mapping-templates-container">
                <div class="mapping-templates-list" id="mappingTemplatesList">
                    <!-- 映射模板卡片将在这里动态生成 -->
                </div>

                <!-- 分页控件 -->
                <div class="pagination-container" id="paginationContainer" style="display: none;">
                    <div class="pagination">
                        <button type="button" class="pagination-btn pagination-prev" id="paginationPrev" disabled>
                            <svg viewBox="0 0 24 24">
                                <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                            </svg>
                        </button>
                        <span class="pagination-info" id="paginationInfo">1 / 1</span>
                        <button type="button" class="pagination-btn pagination-next" id="paginationNext" disabled>
                            <svg viewBox="0 0 24 24">
                                <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- 空状态 -->
                <div class="empty-state" id="emptyState" style="display: none;">
                    <div class="empty-icon">
                        <svg viewBox="0 0 24 24">
                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                    </div>
                    <h3 class="empty-title">还没有映射模板</h3>
                    <p class="empty-description">创建您的第一个字段映射模板，提高数据处理效率</p>
                    <button type="button" class="btn-primary btn-create-first" id="createFirstMappingBtn">
                        创建映射模板
                    </button>
                </div>

                <!-- 搜索无结果状态 -->
                <div class="no-results-state" id="noResultsState" style="display: none;">
                    <div class="no-results-icon">
                        <svg viewBox="0 0 24 24">
                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        </svg>
                    </div>
                    <h3 class="no-results-title">未找到匹配的模板</h3>
                    <p class="no-results-description">尝试使用不同的关键词搜索</p>
                    <button type="button" class="btn-secondary btn-clear-search" id="clearSearchBtn">
                        清除搜索
                    </button>
                </div>
            </div>

            <!-- 加载状态 -->
            <div class="loading-state" id="loadingState" style="display: none;">
                <div class="loading-spinner">
                    <div class="spinner-ring"></div>
                </div>
                <p class="loading-text">正在加载映射模板...</p>
            </div>
        </div>
    `;

    this.container.innerHTML = html;
};

/**
 * @function FieldMappingPageComponent.prototype.bindEvents
 * @description 绑定事件监听器
 */
FieldMappingPageComponent.prototype.bindEvents = function() {
    const self = this;

    // 创建映射模板按钮
    const createBtn = document.getElementById('createMappingBtn');
    const createFirstBtn = document.getElementById('createFirstMappingBtn');

    if (createBtn) {
        createBtn.addEventListener('click', function() {
            self.showMappingDialog();
        });
    }

    if (createFirstBtn) {
        createFirstBtn.addEventListener('click', function() {
            self.showMappingDialog();
        });
    }

    // 搜索功能
    const searchInput = document.getElementById('searchInput');
    const searchClear = document.getElementById('searchClear');
    const clearSearchBtn = document.getElementById('clearSearchBtn');

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            self.searchQuery = this.value.trim();
            self.toggleSearchClear();
            self.debounceSearch();
        });
    }

    if (searchClear) {
        searchClear.addEventListener('click', function() {
            self.clearSearch();
        });
    }

    if (clearSearchBtn) {
        clearSearchBtn.addEventListener('click', function() {
            self.clearSearch();
        });
    }

    // 排序功能
    const sortBtn = document.getElementById('sortBtn');
    const sortMenu = document.getElementById('sortMenu');

    if (sortBtn) {
        sortBtn.addEventListener('click', function() {
            self.toggleSortMenu();
        });
    }

    if (sortMenu) {
        sortMenu.addEventListener('click', function(e) {
            const option = e.target.closest('.sort-option');
            if (option) {
                const sortBy = option.getAttribute('data-sort');
                const sortOrder = option.getAttribute('data-order');
                self.setSorting(sortBy, sortOrder);
            }
        });
    }

    // 分页功能
    const paginationPrev = document.getElementById('paginationPrev');
    const paginationNext = document.getElementById('paginationNext');

    if (paginationPrev) {
        paginationPrev.addEventListener('click', function() {
            self.goToPreviousPage();
        });
    }

    if (paginationNext) {
        paginationNext.addEventListener('click', function() {
            self.goToNextPage();
        });
    }

    // 点击外部关闭排序菜单
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.sort-dropdown')) {
            self.hideSortMenu();
        }
    });

    // 监听映射模板相关事件
    this.eventBus.on('fieldMapping:templateSaved', function(template) {
        self.onTemplateSaved(template);
    });

    this.eventBus.on('fieldMapping:templateDeleted', function(template) {
        self.onTemplateDeleted(template);
    });

    this.eventBus.on('fieldMapping:templateUpdated', function(template) {
        self.onTemplateUpdated(template);
    });
};

/**
 * @function FieldMappingPageComponent.prototype.loadMappingTemplates
 * @description 加载映射模板数据
 */
FieldMappingPageComponent.prototype.loadMappingTemplates = function() {
    try {
        this.showLoading();

        // 从存储中获取所有映射模板
        const templates = this.storage.getAllFieldMappingTemplates({
            sortBy: this.sortBy,
            sortOrder: this.sortOrder
        });

        this.mappingTemplates = templates || [];
        this.renderMappingTemplates();
        this.hideLoading();

        SmartOffice.log('info', '映射模板加载完成，共' + this.mappingTemplates.length + '个模板');
    } catch (error) {
        SmartOffice.log('error', '加载映射模板失败:', error);
        this.hideLoading();
        this.showError('加载映射模板失败，请刷新页面重试');
    }
};

/**
 * @function FieldMappingPageComponent.prototype.renderMappingTemplates
 * @description 渲染映射模板列表
 */
FieldMappingPageComponent.prototype.renderMappingTemplates = function() {
    const listContainer = document.getElementById('mappingTemplatesList');
    const emptyState = document.getElementById('emptyState');
    const noResultsState = document.getElementById('noResultsState');
    const paginationContainer = document.getElementById('paginationContainer');

    if (!listContainer) return;

    // 过滤和搜索
    let filteredTemplates = this.filterTemplates(this.mappingTemplates);

    // 显示状态判断
    if (this.mappingTemplates.length === 0) {
        // 没有任何模板
        listContainer.style.display = 'none';
        paginationContainer.style.display = 'none';
        emptyState.style.display = 'block';
        noResultsState.style.display = 'none';
        return;
    } else if (filteredTemplates.length === 0) {
        // 有模板但搜索无结果
        listContainer.style.display = 'none';
        paginationContainer.style.display = 'none';
        emptyState.style.display = 'none';
        noResultsState.style.display = 'block';
        return;
    } else {
        // 有结果
        emptyState.style.display = 'none';
        noResultsState.style.display = 'none';
        listContainer.style.display = 'block';
    }

    // 分页处理
    const totalPages = Math.ceil(filteredTemplates.length / this.options.itemsPerPage);
    const startIndex = (this.currentPage - 1) * this.options.itemsPerPage;
    const endIndex = startIndex + this.options.itemsPerPage;
    const pageTemplates = filteredTemplates.slice(startIndex, endIndex);

    // 渲染模板卡片
    let html = '';
    for (let i = 0; i < pageTemplates.length; i++) {
        html += this.renderTemplateCard(pageTemplates[i]);
    }

    listContainer.innerHTML = html;

    // 更新分页
    this.updatePagination(totalPages);

    // 绑定卡片事件
    this.bindTemplateCardEvents();
};

/**
 * @function FieldMappingPageComponent.prototype.renderTemplateCard
 * @description 渲染单个映射模板卡片
 * @param {Object} template - 映射模板数据
 * @returns {string} 卡片HTML
 */
FieldMappingPageComponent.prototype.renderTemplateCard = function(template) {
    const mappingsCount = template.mappings ? template.mappings.length : 0;
    const usageCount = template.usageCount || 0;
    const lastUsed = template.lastUsed ? this.formatDate(template.lastUsed) : '从未使用';
    const isDefault = template.isDefault ? 'default-template' : '';
    const category = template.category || 'custom';
    const categoryLabel = this.getCategoryLabel(category);

    return `
        <div class="mapping-template-card ${isDefault}" data-template-id="${template.id}">
            <div class="card-header">
                <div class="card-title-section">
                    <h3 class="card-title">${SmartOffice.Utils.Helpers.escapeHtml(template.name)}</h3>
                    <div class="card-meta">
                        <span class="category-badge category-${category}">${categoryLabel}</span>
                        ${template.isDefault ? '<span class="default-badge">默认</span>' : ''}
                    </div>
                </div>
                <div class="card-actions">
                    <button type="button" class="card-action-btn edit-template" title="编辑模板" data-template-id="${template.id}">
                        <svg viewBox="0 0 24 24">
                            <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                        </svg>
                    </button>
                    <button type="button" class="card-action-btn duplicate-template" title="复制模板" data-template-id="${template.id}">
                        <svg viewBox="0 0 24 24">
                            <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                        </svg>
                    </button>
                    <button type="button" class="card-action-btn delete-template" title="删除模板" data-template-id="${template.id}">
                        <svg viewBox="0 0 24 24">
                            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="card-content">
                ${template.description ? `<p class="card-description">${SmartOffice.Utils.Helpers.escapeHtml(template.description)}</p>` : ''}

                <div class="card-stats">
                    <div class="stat-item">
                        <span class="stat-label">字段映射</span>
                        <span class="stat-value">${mappingsCount} 个</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">使用次数</span>
                        <span class="stat-value">${usageCount} 次</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">最后使用</span>
                        <span class="stat-value">${lastUsed}</span>
                    </div>
                </div>

                ${mappingsCount > 0 ? this.renderMappingPreview(template.mappings.slice(0, 3)) : ''}
            </div>

            <div class="card-footer">
                <button type="button" class="btn-secondary btn-preview" data-template-id="${template.id}">
                    预览映射
                </button>
                <button type="button" class="btn-primary btn-apply" data-template-id="${template.id}">
                    应用模板
                </button>
            </div>
        </div>
    `;
};

/**
 * @function FieldMappingPageComponent.prototype.renderMappingPreview
 * @description 渲染映射预览
 * @param {Array} mappings - 映射列表（前几个）
 * @returns {string} 预览HTML
 */
FieldMappingPageComponent.prototype.renderMappingPreview = function(mappings) {
    let html = '<div class="mapping-preview">';

    for (let i = 0; i < mappings.length; i++) {
        const mapping = mappings[i];
        html += `
            <div class="mapping-item">
                <span class="source-field">${SmartOffice.Utils.Helpers.escapeHtml(mapping.sourceField)}</span>
                <svg class="mapping-arrow" viewBox="0 0 24 24">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                </svg>
                <span class="target-field">${SmartOffice.Utils.Helpers.escapeHtml(mapping.targetField)}</span>
                <span class="data-type-badge type-${mapping.dataType}">${mapping.dataType}</span>
            </div>
        `;
    }

    html += '</div>';
    return html;
};

/**
 * @function FieldMappingPageComponent.prototype.bindTemplateCardEvents
 * @description 绑定模板卡片事件
 */
FieldMappingPageComponent.prototype.bindTemplateCardEvents = function() {
    const self = this;
    const listContainer = document.getElementById('mappingTemplatesList');

    if (!listContainer) return;

    // 编辑模板
    const editButtons = listContainer.querySelectorAll('.edit-template');
    editButtons.forEach(function(btn) {
        btn.addEventListener('click', function() {
            const templateId = this.getAttribute('data-template-id');
            self.editTemplate(templateId);
        });
    });

    // 复制模板
    const duplicateButtons = listContainer.querySelectorAll('.duplicate-template');
    duplicateButtons.forEach(function(btn) {
        btn.addEventListener('click', function() {
            const templateId = this.getAttribute('data-template-id');
            self.duplicateTemplate(templateId);
        });
    });

    // 删除模板
    const deleteButtons = listContainer.querySelectorAll('.delete-template');
    deleteButtons.forEach(function(btn) {
        btn.addEventListener('click', function() {
            const templateId = this.getAttribute('data-template-id');
            self.deleteTemplate(templateId);
        });
    });

    // 预览映射
    const previewButtons = listContainer.querySelectorAll('.btn-preview');
    previewButtons.forEach(function(btn) {
        btn.addEventListener('click', function() {
            const templateId = this.getAttribute('data-template-id');
            self.previewTemplate(templateId);
        });
    });

    // 应用模板
    const applyButtons = listContainer.querySelectorAll('.btn-apply');
    applyButtons.forEach(function(btn) {
        btn.addEventListener('click', function() {
            const templateId = this.getAttribute('data-template-id');
            self.applyTemplate(templateId);
        });
    });
};

/**
 * @function FieldMappingPageComponent.prototype.filterTemplates
 * @description 过滤映射模板
 * @param {Array} templates - 原始模板列表
 * @returns {Array} 过滤后的模板列表
 */
FieldMappingPageComponent.prototype.filterTemplates = function(templates) {
    let filtered = templates.slice();

    // 搜索过滤
    if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        filtered = filtered.filter(function(template) {
            return template.name.toLowerCase().includes(query) ||
                   (template.description && template.description.toLowerCase().includes(query)) ||
                   (template.category && template.category.toLowerCase().includes(query));
        });
    }

    // 排序
    filtered.sort((a, b) => {
        let valueA = a[this.sortBy];
        let valueB = b[this.sortBy];

        if (this.sortBy === 'name') {
            valueA = valueA ? valueA.toLowerCase() : '';
            valueB = valueB ? valueB.toLowerCase() : '';
        } else if (this.sortBy === 'updatedAt' || this.sortBy === 'createdAt') {
            valueA = new Date(valueA || 0).getTime();
            valueB = new Date(valueB || 0).getTime();
        } else {
            valueA = valueA || 0;
            valueB = valueB || 0;
        }

        if (this.sortOrder === 'desc') {
            return valueA < valueB ? 1 : (valueA > valueB ? -1 : 0);
        } else {
            return valueA > valueB ? 1 : (valueA < valueB ? -1 : 0);
        }
    });

    return filtered;
};

/**
 * @function FieldMappingPageComponent.prototype.showMappingDialog
 * @description 显示映射配置对话框
 * @param {Object} template - 要编辑的模板（可选）
 */
FieldMappingPageComponent.prototype.showMappingDialog = function(template) {
    // 如果映射对话框组件还未初始化，先初始化
    if (!this.mappingDialog) {
        this.mappingDialog = new SmartOffice.Components.MappingDialog({
            onSave: (savedTemplate) => {
                this.onTemplateSaved(savedTemplate);
            },
            onCancel: () => {
                // 对话框取消，无需特殊处理
            }
        });
        this.mappingDialog.init();
    }

    // 显示对话框
    this.mappingDialog.show(template);
};

/**
 * @function FieldMappingPageComponent.prototype.editTemplate
 * @description 编辑映射模板
 * @param {string} templateId - 模板ID
 */
FieldMappingPageComponent.prototype.editTemplate = function(templateId) {
    const template = this.findTemplateById(templateId);
    if (template) {
        this.showMappingDialog(template);
    } else {
        this.showError('未找到要编辑的模板');
    }
};

/**
 * @function FieldMappingPageComponent.prototype.duplicateTemplate
 * @description 复制映射模板
 * @param {string} templateId - 模板ID
 */
FieldMappingPageComponent.prototype.duplicateTemplate = function(templateId) {
    const template = this.findTemplateById(templateId);
    if (template) {
        const duplicatedTemplate = {
            ...template,
            id: SmartOffice.Utils.Helpers.generateId('mapping'),
            name: template.name + ' (副本)',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            usageCount: 0,
            lastUsed: null,
            isDefault: false
        };

        this.showMappingDialog(duplicatedTemplate);
    } else {
        this.showError('未找到要复制的模板');
    }
};

/**
 * @function FieldMappingPageComponent.prototype.deleteTemplate
 * @description 删除映射模板
 * @param {string} templateId - 模板ID
 */
FieldMappingPageComponent.prototype.deleteTemplate = function(templateId) {
    const template = this.findTemplateById(templateId);
    if (!template) {
        this.showError('未找到要删除的模板');
        return;
    }

    // 显示确认对话框
    const confirmed = confirm(`确定要删除映射模板"${template.name}"吗？\n\n此操作不可撤销。`);
    if (confirmed) {
        const success = this.fieldMapper.deleteMappingTemplate(templateId);
        if (success) {
            this.showSuccess('映射模板删除成功');
            this.loadMappingTemplates(); // 重新加载列表
        } else {
            this.showError('删除映射模板失败');
        }
    }
};

/**
 * @function FieldMappingPageComponent.prototype.previewTemplate
 * @description 预览映射模板
 * @param {string} templateId - 模板ID
 */
FieldMappingPageComponent.prototype.previewTemplate = function(templateId) {
    const template = this.findTemplateById(templateId);
    if (template) {
        // 触发预览事件，由其他组件处理
        this.eventBus.emit('fieldMapping:previewTemplate', template);
    } else {
        this.showError('未找到要预览的模板');
    }
};

/**
 * @function FieldMappingPageComponent.prototype.applyTemplate
 * @description 应用映射模板
 * @param {string} templateId - 模板ID
 */
FieldMappingPageComponent.prototype.applyTemplate = function(templateId) {
    const template = this.findTemplateById(templateId);
    if (template) {
        // 触发应用事件，由其他组件处理
        this.eventBus.emit('fieldMapping:applyTemplate', template);
        this.showSuccess('映射模板已应用');
    } else {
        this.showError('未找到要应用的模板');
    }
};

/**
 * @function FieldMappingPageComponent.prototype.findTemplateById
 * @description 根据ID查找模板
 * @param {string} templateId - 模板ID
 * @returns {Object|null} 找到的模板
 */
FieldMappingPageComponent.prototype.findTemplateById = function(templateId) {
    return this.mappingTemplates.find(template => template.id === templateId) || null;
};

/**
 * @function FieldMappingPageComponent.prototype.getCategoryLabel
 * @description 获取类别标签
 * @param {string} category - 类别代码
 * @returns {string} 类别标签
 */
FieldMappingPageComponent.prototype.getCategoryLabel = function(category) {
    const labels = {
        sales: '销售',
        finance: '财务',
        hr: '人事',
        inventory: '库存',
        custom: '自定义'
    };
    return labels[category] || '自定义';
};

/**
 * @function FieldMappingPageComponent.prototype.formatDate
 * @description 格式化日期
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期
 */
FieldMappingPageComponent.prototype.formatDate = function(dateString) {
    try {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffDays === 0) {
            return '今天';
        } else if (diffDays === 1) {
            return '昨天';
        } else if (diffDays < 7) {
            return diffDays + '天前';
        } else {
            return date.toLocaleDateString('zh-CN');
        }
    } catch (error) {
        return '未知';
    }
};

/**
 * @function FieldMappingPageComponent.prototype.toggleSearchClear
 * @description 切换搜索清除按钮显示
 */
FieldMappingPageComponent.prototype.toggleSearchClear = function() {
    const searchClear = document.getElementById('searchClear');
    if (searchClear) {
        searchClear.style.display = this.searchQuery ? 'block' : 'none';
    }
};

/**
 * @function FieldMappingPageComponent.prototype.clearSearch
 * @description 清除搜索
 */
FieldMappingPageComponent.prototype.clearSearch = function() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.value = '';
    }
    this.searchQuery = '';
    this.currentPage = 1;
    this.toggleSearchClear();
    this.renderMappingTemplates();
};

/**
 * @function FieldMappingPageComponent.prototype.debounceSearch
 * @description 防抖搜索
 */
FieldMappingPageComponent.prototype.debounceSearch = function() {
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
        this.currentPage = 1;
        this.renderMappingTemplates();
    }, 300);
};

/**
 * @function FieldMappingPageComponent.prototype.setSorting
 * @description 设置排序
 * @param {string} sortBy - 排序字段
 * @param {string} sortOrder - 排序顺序
 */
FieldMappingPageComponent.prototype.setSorting = function(sortBy, sortOrder) {
    this.sortBy = sortBy;
    this.sortOrder = sortOrder;
    this.currentPage = 1;
    this.hideSortMenu();
    this.renderMappingTemplates();
};

/**
 * @function FieldMappingPageComponent.prototype.toggleSortMenu
 * @description 切换排序菜单显示
 */
FieldMappingPageComponent.prototype.toggleSortMenu = function() {
    const sortMenu = document.getElementById('sortMenu');
    if (sortMenu) {
        sortMenu.classList.toggle('sort-menu-visible');
    }
};

/**
 * @function FieldMappingPageComponent.prototype.hideSortMenu
 * @description 隐藏排序菜单
 */
FieldMappingPageComponent.prototype.hideSortMenu = function() {
    const sortMenu = document.getElementById('sortMenu');
    if (sortMenu) {
        sortMenu.classList.remove('sort-menu-visible');
    }
};

/**
 * @function FieldMappingPageComponent.prototype.updatePagination
 * @description 更新分页控件
 * @param {number} totalPages - 总页数
 */
FieldMappingPageComponent.prototype.updatePagination = function(totalPages) {
    const paginationContainer = document.getElementById('paginationContainer');
    const paginationInfo = document.getElementById('paginationInfo');
    const paginationPrev = document.getElementById('paginationPrev');
    const paginationNext = document.getElementById('paginationNext');

    if (totalPages <= 1) {
        paginationContainer.style.display = 'none';
        return;
    }

    paginationContainer.style.display = 'block';

    if (paginationInfo) {
        paginationInfo.textContent = `${this.currentPage} / ${totalPages}`;
    }

    if (paginationPrev) {
        paginationPrev.disabled = this.currentPage <= 1;
    }

    if (paginationNext) {
        paginationNext.disabled = this.currentPage >= totalPages;
    }
};

/**
 * @function FieldMappingPageComponent.prototype.goToPreviousPage
 * @description 转到上一页
 */
FieldMappingPageComponent.prototype.goToPreviousPage = function() {
    if (this.currentPage > 1) {
        this.currentPage--;
        this.renderMappingTemplates();
    }
};

/**
 * @function FieldMappingPageComponent.prototype.goToNextPage
 * @description 转到下一页
 */
FieldMappingPageComponent.prototype.goToNextPage = function() {
    const filteredTemplates = this.filterTemplates(this.mappingTemplates);
    const totalPages = Math.ceil(filteredTemplates.length / this.options.itemsPerPage);

    if (this.currentPage < totalPages) {
        this.currentPage++;
        this.renderMappingTemplates();
    }
};

// 事件处理方法
FieldMappingPageComponent.prototype.onTemplateSaved = function(template) {
    this.loadMappingTemplates();
    this.showSuccess('映射模板保存成功');
};

FieldMappingPageComponent.prototype.onTemplateDeleted = function(template) {
    this.loadMappingTemplates();
};

FieldMappingPageComponent.prototype.onTemplateUpdated = function(template) {
    this.loadMappingTemplates();
};

// 状态显示方法
FieldMappingPageComponent.prototype.showLoading = function() {
    const loadingState = document.getElementById('loadingState');
    if (loadingState) {
        loadingState.style.display = 'block';
    }
};

FieldMappingPageComponent.prototype.hideLoading = function() {
    const loadingState = document.getElementById('loadingState');
    if (loadingState) {
        loadingState.style.display = 'none';
    }
};

FieldMappingPageComponent.prototype.showSuccess = function(message) {
    this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, message, 'success');
};

FieldMappingPageComponent.prototype.showError = function(message) {
    this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, message, 'error');
};

// 注册组件
SmartOffice.Components.FieldMappingPage = FieldMappingPageComponent;

SmartOffice.log('info', 'SmartOffice字段映射管理页面组件模块初始化完成');
