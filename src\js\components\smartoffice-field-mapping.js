/**
 * @file SmartOffice字段映射主界面组件 - 重构版本
 * @description 提供文件上传、字段映射关系建立、拖拽映射界面的核心组件
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2025-01-03 重构为通用字段映射管理模块
 */

/**
 * @function FieldMappingComponent
 * @description 字段映射主界面组件构造函数
 * @constructor
 * @param {Object} options - 组件配置选项
 */
function FieldMappingComponent(options) {
    /**
     * @property {Object} options - 组件配置
     */
    this.options = Object.assign({
        containerId: 'fieldMappingContainer',
        showFileUpload: true,
        enableDragDrop: true,
        enableSmartSuggestions: true,
        autoSave: true,
        maxMappings: 50
    }, options || {});

    /**
     * @property {HTMLElement} container - 容器元素
     */
    this.container = null;

    /**
     * @property {Object} fieldMappingManager - 字段映射管理器引用
     */
    this.fieldMappingManager = SmartOffice.Data.FieldMappingManager;

    /**
     * @property {Object} fileUpload - 文件上传组件引用
     */
    this.fileUpload = null;

    /**
     * @property {Object} eventBus - 事件总线引用
     */
    this.eventBus = SmartOffice.Core.EventBus;

    /**
     * @property {Object} currentFileData - 当前文件数据
     */
    this.currentFileData = null;

    /**
     * @property {Array} sourceFields - 源字段列表
     */
    this.sourceFields = [];

    /**
     * @property {Array} currentMappings - 当前映射关系列表
     */
    this.currentMappings = [];

    /**
     * @property {Array} suggestions - 智能映射建议列表
     */
    this.suggestions = [];

    /**
     * @property {boolean} isDragMode - 是否处于拖拽模式
     */
    this.isDragMode = false;

    /**
     * @property {HTMLElement} dragElement - 当前拖拽元素
     */
    this.dragElement = null;

    /**
     * @property {Object} dragData - 拖拽数据
     */
    this.dragData = null;

    SmartOffice.log('info', 'FieldMappingComponent字段映射主界面组件初始化完成');
}

/**
 * @function FieldMappingComponent.prototype.init
 * @description 初始化组件
 */
FieldMappingComponent.prototype.init = function() {
    try {
        this.container = document.getElementById(this.options.containerId);
        if (!this.container) {
            throw new Error('未找到容器元素: ' + this.options.containerId);
        }

        // 初始化字段映射管理器
        this.fieldMappingManager.init();

        // 渲染主界面结构
        this.render();

        // 初始化文件上传组件
        this.initFileUpload();

        // 绑定事件
        this.bindEvents();

        // 加载当前映射状态
        this.loadCurrentMapping();

        SmartOffice.log('info', '字段映射主界面组件初始化成功');
    } catch (error) {
        SmartOffice.log('error', '字段映射主界面组件初始化失败:', error);
    }
};

/**
 * @function FieldMappingComponent.prototype.render
 * @description 渲染主界面结构
 */
FieldMappingComponent.prototype.render = function() {
    const html = `
        <div class="field-mapping-container">
            <!-- 文件上传区域 -->
            <div class="file-upload-section" id="fileUploadSection">
                <div class="section-header">
                    <h3 class="section-title">
                        <svg class="section-icon" viewBox="0 0 24 24">
                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                        文件上传
                    </h3>
                    <p class="section-description">上传CSV或Excel文件，自动识别表头字段</p>
                </div>
                <div class="file-upload-container" id="fileUploadContainer">
                    <!-- 文件上传组件将在这里初始化 -->
                </div>
            </div>

            <!-- 字段映射区域 -->
            <div class="field-mapping-section" id="fieldMappingSection" style="display: none;">
                <div class="section-header">
                    <h3 class="section-title">
                        <svg class="section-icon" viewBox="0 0 24 24">
                            <path d="M9,5V9H21V5M9,19H21V15H9M9,14H21V10H9M4,9H8L6,7L4,9M4,19H8L6,17L4,19M4,14H8L6,12L4,14Z"/>
                        </svg>
                        字段映射配置
                    </h3>
                    <p class="section-description">将文件字段映射到标准字段，支持拖拽操作</p>
                </div>

                <!-- 映射操作工具栏 -->
                <div class="mapping-toolbar">
                    <div class="toolbar-left">
                        <button type="button" class="btn-secondary btn-apply-suggestions" id="applySuggestionsBtn">
                            <svg class="btn-icon" viewBox="0 0 24 24">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                            </svg>
                            应用智能建议
                        </button>
                        <button type="button" class="btn-secondary btn-clear-mappings" id="clearMappingsBtn">
                            <svg class="btn-icon" viewBox="0 0 24 24">
                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                            </svg>
                            清除映射
                        </button>
                    </div>
                    <div class="toolbar-right">
                        <button type="button" class="btn-secondary btn-load-template" id="loadTemplateBtn">
                            <svg class="btn-icon" viewBox="0 0 24 24">
                                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                            </svg>
                            加载模板
                        </button>
                        <button type="button" class="btn-primary btn-save-template" id="saveTemplateBtn">
                            <svg class="btn-icon" viewBox="0 0 24 24">
                                <path d="M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z"/>
                            </svg>
                            保存模板
                        </button>
                    </div>
                </div>

                <!-- 字段映射主界面 -->
                <div class="mapping-interface">
                    <!-- 源字段列表 -->
                    <div class="source-fields-panel">
                        <div class="panel-header">
                            <h4 class="panel-title">文件字段</h4>
                            <span class="field-count" id="sourceFieldCount">0 个字段</span>
                        </div>
                        <div class="fields-list" id="sourceFieldsList">
                            <!-- 源字段项将在这里动态生成 -->
                        </div>
                    </div>

                    <!-- 映射连接区域 -->
                    <div class="mapping-connections">
                        <div class="connection-lines" id="connectionLines">
                            <!-- 映射连接线将在这里动态生成 -->
                        </div>
                        <div class="mapping-hint" id="mappingHint">
                            <svg class="hint-icon" viewBox="0 0 24 24">
                                <path d="M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                            </svg>
                            <p>拖拽左侧字段到右侧标准字段建立映射关系</p>
                        </div>
                    </div>

                    <!-- 标准字段列表 -->
                    <div class="target-fields-panel">
                        <div class="panel-header">
                            <h4 class="panel-title">标准字段</h4>
                            <div class="field-filter">
                                <select class="filter-select" id="categoryFilter">
                                    <option value="">所有类别</option>
                                    <option value="financial">财务</option>
                                    <option value="quantity">数量</option>
                                    <option value="temporal">时间</option>
                                    <option value="classification">分类</option>
                                    <option value="location">位置</option>
                                    <option value="entity">实体</option>
                                    <option value="status">状态</option>
                                    <option value="identifier">标识</option>
                                    <option value="content">内容</option>
                                </select>
                            </div>
                        </div>
                        <div class="fields-list" id="targetFieldsList">
                            <!-- 标准字段项将在这里动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 映射结果预览 -->
                <div class="mapping-results" id="mappingResults" style="display: none;">
                    <div class="results-header">
                        <h4 class="results-title">映射结果</h4>
                        <span class="mapping-count" id="mappingCount">0 个映射</span>
                    </div>
                    <div class="results-list" id="resultsList">
                        <!-- 映射结果项将在这里动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 操作按钮区域 -->
            <div class="action-buttons" id="actionButtons" style="display: none;">
                <button type="button" class="btn-secondary btn-cancel" id="cancelBtn">取消</button>
                <button type="button" class="btn-primary btn-save-mapping" id="saveMappingBtn">保存映射</button>
                <button type="button" class="btn-primary btn-continue" id="continueBtn">继续配置透视表</button>
            </div>
        </div>
    `;

    this.container.innerHTML = html;
};

/**
 * @function FieldMappingComponent.prototype.initFileUpload
 * @description 初始化文件上传组件
 */
FieldMappingComponent.prototype.initFileUpload = function() {
    try {
        const fileUploadContainer = document.getElementById('fileUploadContainer');
        if (!fileUploadContainer) {
            throw new Error('未找到文件上传容器');
        }

        // 创建文件上传组件实例
        this.fileUpload = new SmartOffice.Components.FileUpload({
            containerId: 'fileUploadContainer',
            acceptedTypes: ['.csv', '.xlsx', '.xls'],
            maxFileSize: 50 * 1024 * 1024, // 50MB
            showPreview: false,
            autoProcess: false
        });

        // 初始化文件上传组件
        this.fileUpload.init();

        SmartOffice.log('info', '文件上传组件初始化成功');
    } catch (error) {
        SmartOffice.log('error', '文件上传组件初始化失败:', error);
    }
};

/**
 * @function FieldMappingComponent.prototype.bindEvents
 * @description 绑定事件监听器
 */
FieldMappingComponent.prototype.bindEvents = function() {
    const self = this;

    // 监听文件上传事件
    this.eventBus.on('fileUpload:fileSelected', function(data) {
        self.onFileSelected(data);
    });

    this.eventBus.on('fileUpload:uploadComplete', function(data) {
        self.onFileUploaded(data);
    });

    this.eventBus.on('fileUpload:error', function(error) {
        self.onFileUploadError(error);
    });

    // 工具栏按钮事件
    const applySuggestionsBtn = document.getElementById('applySuggestionsBtn');
    const clearMappingsBtn = document.getElementById('clearMappingsBtn');
    const loadTemplateBtn = document.getElementById('loadTemplateBtn');
    const saveTemplateBtn = document.getElementById('saveTemplateBtn');

    if (applySuggestionsBtn) {
        applySuggestionsBtn.addEventListener('click', function() {
            self.applySmartSuggestions();
        });
    }

    if (clearMappingsBtn) {
        clearMappingsBtn.addEventListener('click', function() {
            self.clearAllMappings();
        });
    }

    if (loadTemplateBtn) {
        loadTemplateBtn.addEventListener('click', function() {
            self.showTemplateSelector();
        });
    }

    if (saveTemplateBtn) {
        saveTemplateBtn.addEventListener('click', function() {
            self.showTemplateSaveDialog();
        });
    }

    // 类别筛选
    const categoryFilter = document.getElementById('categoryFilter');
    if (categoryFilter) {
        categoryFilter.addEventListener('change', function() {
            self.filterTargetFields(this.value);
        });
    }

    // 操作按钮事件
    const cancelBtn = document.getElementById('cancelBtn');
    const saveMappingBtn = document.getElementById('saveMappingBtn');
    const continueBtn = document.getElementById('continueBtn');

    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            self.cancelMapping();
        });
    }

    if (saveMappingBtn) {
        saveMappingBtn.addEventListener('click', function() {
            self.saveCurrentMapping();
        });
    }

    if (continueBtn) {
        continueBtn.addEventListener('click', function() {
            self.continueToConfig();
        });
    }

    // 监听字段映射管理器事件
    this.eventBus.on('fieldMapping:mappingAdded', function(mapping) {
        self.onMappingAdded(mapping);
    });

    this.eventBus.on('fieldMapping:mappingRemoved', function(mapping) {
        self.onMappingRemoved(mapping);
    });

    this.eventBus.on('fieldMapping:currentMappingSaved', function(data) {
        self.onMappingSaved(data);
    });
};

/**
 * @function FieldMappingComponent.prototype.onFileSelected
 * @description 处理文件选择事件
 * @param {Object} data - 文件数据
 */
FieldMappingComponent.prototype.onFileSelected = function(data) {
    try {
        SmartOffice.log('info', '文件已选择:', data.file.name);

        // 显示加载状态
        this.showLoading('正在解析文件...');

        // 使用字段映射管理器解析文件
        this.fieldMappingManager.uploadAndParseFile(data.file)
            .then(function(result) {
                this.onFileParseSuccess(result);
            }.bind(this))
            .catch(function(error) {
                this.onFileParseError(error);
            }.bind(this));

    } catch (error) {
        SmartOffice.log('error', '处理文件选择失败:', error);
        this.onFileParseError(error);
    }
};

/**
 * @function FieldMappingComponent.prototype.onFileParseSuccess
 * @description 处理文件解析成功
 * @param {Object} result - 解析结果
 */
FieldMappingComponent.prototype.onFileParseSuccess = function(result) {
    try {
        this.hideLoading();

        // 保存解析结果
        this.currentFileData = result;
        this.sourceFields = result.sourceFields || [];
        this.suggestions = result.suggestions || [];

        // 显示字段映射区域
        this.showMappingSection();

        // 渲染源字段列表
        this.renderSourceFields();

        // 渲染标准字段列表
        this.renderTargetFields();

        // 显示智能建议
        this.showSmartSuggestions();

        // 显示操作按钮
        this.showActionButtons();

        SmartOffice.log('info', '文件解析成功，共', this.sourceFields.length, '个字段');

        // 触发成功事件
        this.eventBus.emit('fieldMapping:fileParseSuccess', {
            fileName: result.fileName,
            sourceFields: this.sourceFields,
            suggestions: this.suggestions
        });

    } catch (error) {
        SmartOffice.log('error', '处理文件解析结果失败:', error);
        this.showError('处理文件解析结果失败: ' + error.message);
    }
};

/**
 * @function FieldMappingComponent.prototype.onFileParseError
 * @description 处理文件解析错误
 * @param {Error} error - 错误对象
 */
FieldMappingComponent.prototype.onFileParseError = function(error) {
    this.hideLoading();

    const errorMessage = error.message || '文件解析失败';
    SmartOffice.log('error', '文件解析失败:', error);

    // 显示错误提示
    this.showError(errorMessage);

    // 触发错误事件
    this.eventBus.emit('fieldMapping:fileParseError', {
        error: error,
        message: errorMessage
    });
};

/**
 * @function FieldMappingComponent.prototype.renderSourceFields
 * @description 渲染源字段列表
 */
FieldMappingComponent.prototype.renderSourceFields = function() {
    const sourceFieldsList = document.getElementById('sourceFieldsList');
    const sourceFieldCount = document.getElementById('sourceFieldCount');

    if (!sourceFieldsList || !this.sourceFields) {
        return;
    }

    // 更新字段数量
    if (sourceFieldCount) {
        sourceFieldCount.textContent = this.sourceFields.length + ' 个字段';
    }

    // 渲染字段项
    let html = '';
    for (let i = 0; i < this.sourceFields.length; i++) {
        const field = this.sourceFields[i];
        html += this.renderSourceFieldItem(field);
    }

    sourceFieldsList.innerHTML = html;

    // 绑定拖拽事件
    this.bindSourceFieldEvents();
};

/**
 * @function FieldMappingComponent.prototype.renderSourceFieldItem
 * @description 渲染单个源字段项
 * @param {Object} field - 字段信息
 * @returns {string} 字段项HTML
 */
FieldMappingComponent.prototype.renderSourceFieldItem = function(field) {
    const typeClass = 'field-type-' + field.type;
    const typeLabel = this.getFieldTypeLabel(field.type);
    const isMapped = this.isFieldMapped(field.name);
    const mappedClass = isMapped ? 'field-mapped' : '';

    // 获取样本值显示
    const sampleValues = field.sampleValues && field.sampleValues.length > 0
        ? field.sampleValues.slice(0, 3).join(', ')
        : '无数据';

    return `
        <div class="field-item source-field ${mappedClass}"
             data-field-name="${field.name}"
             data-field-type="${field.type}"
             draggable="true">
            <div class="field-header">
                <div class="field-info">
                    <h5 class="field-name">${SmartOffice.Utils.Helpers.escapeHtml(field.name)}</h5>
                    <span class="field-type-tag ${typeClass}">${typeLabel}</span>
                </div>
                <div class="field-actions">
                    ${isMapped ? '<span class="mapped-indicator" title="已映射">✓</span>' : ''}
                    <button type="button" class="field-action-btn" data-action="info" title="字段信息">
                        <svg viewBox="0 0 24 24">
                            <path d="M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="field-details">
                <div class="field-stats">
                    <span class="stat-item">
                        <span class="stat-label">唯一值:</span>
                        <span class="stat-value">${field.statistics.uniqueCount}</span>
                    </span>
                    <span class="stat-item">
                        <span class="stat-label">空值:</span>
                        <span class="stat-value">${field.statistics.emptyCount}</span>
                    </span>
                </div>
                <div class="field-samples">
                    <span class="samples-label">样本值:</span>
                    <span class="samples-value">${SmartOffice.Utils.Helpers.escapeHtml(sampleValues)}</span>
                </div>
            </div>
        </div>
    `;
};

/**
 * @function FieldMappingComponent.prototype.renderTargetFields
 * @description 渲染标准字段列表
 * @param {string} category - 筛选类别
 */
FieldMappingComponent.prototype.renderTargetFields = function(category) {
    const targetFieldsList = document.getElementById('targetFieldsList');

    if (!targetFieldsList) {
        return;
    }

    // 获取标准字段列表
    const standardFields = category
        ? this.fieldMappingManager.getStandardFieldsByCategory(category)
        : this.fieldMappingManager.standardFields;

    // 渲染字段项
    let html = '';
    for (let i = 0; i < standardFields.length; i++) {
        const field = standardFields[i];
        html += this.renderTargetFieldItem(field);
    }

    targetFieldsList.innerHTML = html;

    // 绑定拖拽事件
    this.bindTargetFieldEvents();
};

/**
 * @function FieldMappingComponent.prototype.renderTargetFieldItem
 * @description 渲染单个标准字段项
 * @param {Object} field - 标准字段信息
 * @returns {string} 字段项HTML
 */
FieldMappingComponent.prototype.renderTargetFieldItem = function(field) {
    const typeClass = 'field-type-' + field.type;
    const typeLabel = this.getFieldTypeLabel(field.type);
    const categoryClass = 'field-category-' + field.category;
    const categoryLabel = this.getCategoryLabel(field.category);
    const isMapped = this.isStandardFieldMapped(field.name);
    const mappedClass = isMapped ? 'field-mapped' : '';

    // 获取关键词显示
    const keywords = field.keywords && field.keywords.length > 0
        ? field.keywords.slice(0, 5).join(', ')
        : '';

    return `
        <div class="field-item target-field ${mappedClass}"
             data-field-name="${field.name}"
             data-field-type="${field.type}"
             data-field-category="${field.category}">
            <div class="field-header">
                <div class="field-info">
                    <h5 class="field-name">${SmartOffice.Utils.Helpers.escapeHtml(field.label)}</h5>
                    <div class="field-tags">
                        <span class="field-type-tag ${typeClass}">${typeLabel}</span>
                        <span class="field-category-tag ${categoryClass}">${categoryLabel}</span>
                    </div>
                </div>
                <div class="field-actions">
                    ${isMapped ? '<span class="mapped-indicator" title="已映射">✓</span>' : ''}
                    <button type="button" class="field-action-btn" data-action="info" title="字段信息">
                        <svg viewBox="0 0 24 24">
                            <path d="M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="field-details">
                <div class="field-description">
                    <span class="description-label">标识名:</span>
                    <span class="description-value">${field.name}</span>
                </div>
                ${keywords ? `
                <div class="field-keywords">
                    <span class="keywords-label">关键词:</span>
                    <span class="keywords-value">${SmartOffice.Utils.Helpers.escapeHtml(keywords)}</span>
                </div>
                ` : ''}
            </div>
        </div>
    `;
};

/**
 * @function FieldMappingComponent.prototype.getFieldTypeLabel
 * @description 获取字段类型标签
 * @param {string} type - 字段类型
 * @returns {string} 类型标签
 */
FieldMappingComponent.prototype.getFieldTypeLabel = function(type) {
    const typeLabels = {
        'string': '文本',
        'number': '数值',
        'date': '日期',
        'time': '时间',
        'boolean': '布尔',
        'email': '邮箱',
        'phone': '电话',
        'url': '链接'
    };

    return typeLabels[type] || '未知';
};

/**
 * @function FieldMappingComponent.prototype.getCategoryLabel
 * @description 获取字段类别标签
 * @param {string} category - 字段类别
 * @returns {string} 类别标签
 */
FieldMappingComponent.prototype.getCategoryLabel = function(category) {
    const categoryLabels = {
        'financial': '财务',
        'quantity': '数量',
        'temporal': '时间',
        'classification': '分类',
        'location': '位置',
        'entity': '实体',
        'status': '状态',
        'priority': '优先级',
        'identifier': '标识',
        'content': '内容',
        'product': '产品'
    };

    return categoryLabels[category] || '其他';
};

/**
 * @function FieldMappingComponent.prototype.bindSourceFieldEvents
 * @description 绑定源字段事件
 */
FieldMappingComponent.prototype.bindSourceFieldEvents = function() {
    const self = this;
    const sourceFields = document.querySelectorAll('.source-field');

    sourceFields.forEach(function(fieldElement) {
        // 拖拽开始
        fieldElement.addEventListener('dragstart', function(e) {
            self.onDragStart(e, 'source');
        });

        // 拖拽结束
        fieldElement.addEventListener('dragend', function(e) {
            self.onDragEnd(e);
        });

        // 点击事件
        fieldElement.addEventListener('click', function(e) {
            if (!e.target.closest('.field-action-btn')) {
                self.onSourceFieldClick(this);
            }
        });

        // 字段操作按钮
        const actionBtns = fieldElement.querySelectorAll('.field-action-btn');
        actionBtns.forEach(function(btn) {
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                const action = this.getAttribute('data-action');
                const fieldName = fieldElement.getAttribute('data-field-name');
                self.onFieldAction(action, fieldName, 'source');
            });
        });
    });
};

/**
 * @function FieldMappingComponent.prototype.bindTargetFieldEvents
 * @description 绑定标准字段事件
 */
FieldMappingComponent.prototype.bindTargetFieldEvents = function() {
    const self = this;
    const targetFields = document.querySelectorAll('.target-field');

    targetFields.forEach(function(fieldElement) {
        // 拖拽悬停
        fieldElement.addEventListener('dragover', function(e) {
            e.preventDefault();
            self.onDragOver(e, this);
        });

        // 拖拽进入
        fieldElement.addEventListener('dragenter', function(e) {
            e.preventDefault();
            self.onDragEnter(e, this);
        });

        // 拖拽离开
        fieldElement.addEventListener('dragleave', function(e) {
            self.onDragLeave(e, this);
        });

        // 拖拽放下
        fieldElement.addEventListener('drop', function(e) {
            e.preventDefault();
            self.onDrop(e, this);
        });

        // 点击事件
        fieldElement.addEventListener('click', function(e) {
            if (!e.target.closest('.field-action-btn')) {
                self.onTargetFieldClick(this);
            }
        });

        // 字段操作按钮
        const actionBtns = fieldElement.querySelectorAll('.field-action-btn');
        actionBtns.forEach(function(btn) {
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                const action = this.getAttribute('data-action');
                const fieldName = fieldElement.getAttribute('data-field-name');
                self.onFieldAction(action, fieldName, 'target');
            });
        });
    });
};

/**
 * @function FieldMappingComponent.prototype.onDragStart
 * @description 处理拖拽开始事件
 * @param {Event} e - 拖拽事件
 * @param {string} type - 字段类型 ('source' 或 'target')
 */
FieldMappingComponent.prototype.onDragStart = function(e, type) {
    const fieldElement = e.target.closest('.field-item');
    if (!fieldElement) return;

    this.isDragMode = true;
    this.dragElement = fieldElement;

    // 设置拖拽数据
    this.dragData = {
        type: type,
        fieldName: fieldElement.getAttribute('data-field-name'),
        fieldType: fieldElement.getAttribute('data-field-type'),
        element: fieldElement
    };

    // 设置拖拽效果
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', this.dragData.fieldName);

    // 添加拖拽样式
    fieldElement.classList.add('dragging');

    // 高亮可放置区域
    this.highlightDropZones(true);

    SmartOffice.log('info', '开始拖拽字段:', this.dragData.fieldName);
};

/**
 * @function FieldMappingComponent.prototype.onDragEnd
 * @description 处理拖拽结束事件
 * @param {Event} e - 拖拽事件
 */
FieldMappingComponent.prototype.onDragEnd = function(e) {
    this.isDragMode = false;

    if (this.dragElement) {
        this.dragElement.classList.remove('dragging');
        this.dragElement = null;
    }

    this.dragData = null;

    // 移除高亮效果
    this.highlightDropZones(false);
    this.clearDropIndicators();

    SmartOffice.log('info', '拖拽结束');
};

/**
 * @function FieldMappingComponent.prototype.onDragOver
 * @description 处理拖拽悬停事件
 * @param {Event} e - 拖拽事件
 * @param {HTMLElement} targetElement - 目标元素
 */
FieldMappingComponent.prototype.onDragOver = function(e, targetElement) {
    if (!this.isDragMode || !this.dragData) return;

    // 只允许从源字段拖拽到标准字段
    if (this.dragData.type !== 'source') return;

    e.dataTransfer.dropEffect = 'move';
};

/**
 * @function FieldMappingComponent.prototype.onDragEnter
 * @description 处理拖拽进入事件
 * @param {Event} e - 拖拽事件
 * @param {HTMLElement} targetElement - 目标元素
 */
FieldMappingComponent.prototype.onDragEnter = function(e, targetElement) {
    if (!this.isDragMode || !this.dragData) return;

    // 只允许从源字段拖拽到标准字段
    if (this.dragData.type !== 'source') return;

    // 添加拖拽悬停样式
    targetElement.classList.add('drag-over');
};

/**
 * @function FieldMappingComponent.prototype.onDragLeave
 * @description 处理拖拽离开事件
 * @param {Event} e - 拖拽事件
 * @param {HTMLElement} targetElement - 目标元素
 */
FieldMappingComponent.prototype.onDragLeave = function(e, targetElement) {
    // 检查是否真的离开了元素（而不是进入子元素）
    if (!targetElement.contains(e.relatedTarget)) {
        targetElement.classList.remove('drag-over');
    }
};

/**
 * @function FieldMappingComponent.prototype.onDrop
 * @description 处理拖拽放下事件
 * @param {Event} e - 拖拽事件
 * @param {HTMLElement} targetElement - 目标元素
 */
FieldMappingComponent.prototype.onDrop = function(e, targetElement) {
    if (!this.isDragMode || !this.dragData) return;

    // 只允许从源字段拖拽到标准字段
    if (this.dragData.type !== 'source') return;

    // 移除拖拽样式
    targetElement.classList.remove('drag-over');

    // 获取目标字段信息
    const targetFieldName = targetElement.getAttribute('data-field-name');
    const targetFieldType = targetElement.getAttribute('data-field-type');

    // 创建映射关系
    this.createMapping(this.dragData.fieldName, targetFieldName, targetFieldType);

    SmartOffice.log('info', '创建映射:', this.dragData.fieldName, '->', targetFieldName);
};

/**
 * @function FieldMappingComponent.prototype.createMapping
 * @description 创建字段映射关系
 * @param {string} sourceField - 源字段名
 * @param {string} targetField - 目标字段名
 * @param {string} dataType - 数据类型
 */
FieldMappingComponent.prototype.createMapping = function(sourceField, targetField, dataType) {
    try {
        // 检查是否已存在映射
        if (this.isFieldMapped(sourceField)) {
            // 移除现有映射
            this.fieldMappingManager.removeMapping(sourceField);
        }

        // 创建新映射
        const mapping = this.fieldMappingManager.addMapping(sourceField, targetField, dataType);

        // 更新当前映射列表
        this.currentMappings = this.fieldMappingManager.getCurrentMappings();

        // 更新界面显示
        this.updateMappingDisplay();

        // 显示成功提示
        SmartOffice.Components.Toast.show({
            message: `已创建映射: ${sourceField} → ${this.fieldMappingManager.getStandardFieldLabel(targetField)}`,
            type: 'success',
            duration: 3000
        });

    } catch (error) {
        SmartOffice.log('error', '创建映射失败:', error);
        SmartOffice.Components.Toast.show({
            message: '创建映射失败: ' + error.message,
            type: 'error',
            duration: 5000
        });
    }
};

/**
 * @function FieldMappingComponent.prototype.removeMapping
 * @description 移除字段映射关系
 * @param {string} sourceField - 源字段名
 */
FieldMappingComponent.prototype.removeMapping = function(sourceField) {
    try {
        const success = this.fieldMappingManager.removeMapping(sourceField);

        if (success) {
            // 更新当前映射列表
            this.currentMappings = this.fieldMappingManager.getCurrentMappings();

            // 更新界面显示
            this.updateMappingDisplay();

            // 显示成功提示
            SmartOffice.Components.Toast.show({
                message: `已移除映射: ${sourceField}`,
                type: 'success',
                duration: 3000
            });
        }

    } catch (error) {
        SmartOffice.log('error', '移除映射失败:', error);
        SmartOffice.Components.Toast.show({
            message: '移除映射失败: ' + error.message,
            type: 'error',
            duration: 5000
        });
    }
};

/**
 * @function FieldMappingComponent.prototype.updateMappingDisplay
 * @description 更新映射显示
 */
FieldMappingComponent.prototype.updateMappingDisplay = function() {
    // 更新字段映射状态
    this.updateFieldMappingStatus();

    // 更新映射结果预览
    this.updateMappingResults();

    // 更新连接线
    this.updateConnectionLines();

    // 更新操作按钮状态
    this.updateActionButtonsState();
};

/**
 * @function FieldMappingComponent.prototype.updateFieldMappingStatus
 * @description 更新字段映射状态显示
 */
FieldMappingComponent.prototype.updateFieldMappingStatus = function() {
    // 更新源字段映射状态
    const sourceFields = document.querySelectorAll('.source-field');
    sourceFields.forEach(function(fieldElement) {
        const fieldName = fieldElement.getAttribute('data-field-name');
        const isMapped = this.isFieldMapped(fieldName);

        if (isMapped) {
            fieldElement.classList.add('field-mapped');
        } else {
            fieldElement.classList.remove('field-mapped');
        }

        // 更新映射指示器
        const indicator = fieldElement.querySelector('.mapped-indicator');
        if (isMapped && !indicator) {
            const actionsDiv = fieldElement.querySelector('.field-actions');
            if (actionsDiv) {
                actionsDiv.insertAdjacentHTML('afterbegin', '<span class="mapped-indicator" title="已映射">✓</span>');
            }
        } else if (!isMapped && indicator) {
            indicator.remove();
        }
    }.bind(this));

    // 更新标准字段映射状态
    const targetFields = document.querySelectorAll('.target-field');
    targetFields.forEach(function(fieldElement) {
        const fieldName = fieldElement.getAttribute('data-field-name');
        const isMapped = this.isStandardFieldMapped(fieldName);

        if (isMapped) {
            fieldElement.classList.add('field-mapped');
        } else {
            fieldElement.classList.remove('field-mapped');
        }

        // 更新映射指示器
        const indicator = fieldElement.querySelector('.mapped-indicator');
        if (isMapped && !indicator) {
            const actionsDiv = fieldElement.querySelector('.field-actions');
            if (actionsDiv) {
                actionsDiv.insertAdjacentHTML('afterbegin', '<span class="mapped-indicator" title="已映射">✓</span>');
            }
        } else if (!isMapped && indicator) {
            indicator.remove();
        }
    }.bind(this));
};

/**
 * @function FieldMappingComponent.prototype.updateMappingResults
 * @description 更新映射结果预览
 */
FieldMappingComponent.prototype.updateMappingResults = function() {
    const mappingResults = document.getElementById('mappingResults');
    const mappingCount = document.getElementById('mappingCount');
    const resultsList = document.getElementById('resultsList');

    if (!mappingResults || !this.currentMappings) {
        return;
    }

    // 更新映射数量
    if (mappingCount) {
        mappingCount.textContent = this.currentMappings.length + ' 个映射';
    }

    if (this.currentMappings.length === 0) {
        mappingResults.style.display = 'none';
        return;
    }

    // 显示映射结果区域
    mappingResults.style.display = 'block';

    // 渲染映射结果列表
    if (resultsList) {
        let html = '';
        for (let i = 0; i < this.currentMappings.length; i++) {
            const mapping = this.currentMappings[i];
            html += this.renderMappingResultItem(mapping);
        }
        resultsList.innerHTML = html;

        // 绑定结果项事件
        this.bindMappingResultEvents();
    }
};

/**
 * @function FieldMappingComponent.prototype.renderMappingResultItem
 * @description 渲染映射结果项
 * @param {Object} mapping - 映射对象
 * @returns {string} 结果项HTML
 */
FieldMappingComponent.prototype.renderMappingResultItem = function(mapping) {
    const targetLabel = this.fieldMappingManager.getStandardFieldLabel(mapping.targetField);
    const confidenceClass = this.getConfidenceClass(mapping.confidence);
    const confidencePercent = Math.round(mapping.confidence * 100);

    return `
        <div class="mapping-result-item" data-mapping-id="${mapping.id}">
            <div class="mapping-info">
                <div class="mapping-fields">
                    <span class="source-field-name">${SmartOffice.Utils.Helpers.escapeHtml(mapping.sourceField)}</span>
                    <svg class="mapping-arrow" viewBox="0 0 24 24">
                        <path d="M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z"/>
                    </svg>
                    <span class="target-field-name">${SmartOffice.Utils.Helpers.escapeHtml(targetLabel)}</span>
                </div>
                <div class="mapping-meta">
                    <span class="confidence-badge ${confidenceClass}" title="映射置信度">
                        ${confidencePercent}%
                    </span>
                    <span class="data-type-badge">${this.getFieldTypeLabel(mapping.dataType)}</span>
                </div>
            </div>
            <div class="mapping-actions">
                <button type="button" class="mapping-action-btn edit-mapping" title="编辑映射" data-mapping-id="${mapping.id}">
                    <svg viewBox="0 0 24 24">
                        <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                    </svg>
                </button>
                <button type="button" class="mapping-action-btn remove-mapping" title="移除映射" data-mapping-id="${mapping.id}">
                    <svg viewBox="0 0 24 24">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
        </div>
    `;
};

/**
 * @function FieldMappingComponent.prototype.isFieldMapped
 * @description 检查源字段是否已映射
 * @param {string} fieldName - 字段名
 * @returns {boolean} 是否已映射
 */
FieldMappingComponent.prototype.isFieldMapped = function(fieldName) {
    return this.currentMappings.some(function(mapping) {
        return mapping.sourceField === fieldName;
    });
};

/**
 * @function FieldMappingComponent.prototype.isStandardFieldMapped
 * @description 检查标准字段是否已映射
 * @param {string} fieldName - 标准字段名
 * @returns {boolean} 是否已映射
 */
FieldMappingComponent.prototype.isStandardFieldMapped = function(fieldName) {
    return this.currentMappings.some(function(mapping) {
        return mapping.targetField === fieldName;
    });
};

/**
 * @function FieldMappingComponent.prototype.getConfidenceClass
 * @description 获取置信度样式类
 * @param {number} confidence - 置信度 (0-1)
 * @returns {string} 样式类名
 */
FieldMappingComponent.prototype.getConfidenceClass = function(confidence) {
    if (confidence >= 0.8) return 'confidence-high';
    if (confidence >= 0.6) return 'confidence-medium';
    if (confidence >= 0.4) return 'confidence-low';
    return 'confidence-very-low';
};

/**
 * @function FieldMappingComponent.prototype.showMappingSection
 * @description 显示字段映射区域
 */
FieldMappingComponent.prototype.showMappingSection = function() {
    const fieldMappingSection = document.getElementById('fieldMappingSection');
    if (fieldMappingSection) {
        fieldMappingSection.style.display = 'block';
    }
};

/**
 * @function FieldMappingComponent.prototype.showActionButtons
 * @description 显示操作按钮区域
 */
FieldMappingComponent.prototype.showActionButtons = function() {
    const actionButtons = document.getElementById('actionButtons');
    if (actionButtons) {
        actionButtons.style.display = 'flex';
    }
};

/**
 * @function FieldMappingComponent.prototype.showLoading
 * @description 显示加载状态
 * @param {string} message - 加载消息
 */
FieldMappingComponent.prototype.showLoading = function(message) {
    SmartOffice.Components.Loading.show({
        message: message || '正在处理...',
        overlay: true
    });
};

/**
 * @function FieldMappingComponent.prototype.hideLoading
 * @description 隐藏加载状态
 */
FieldMappingComponent.prototype.hideLoading = function() {
    SmartOffice.Components.Loading.hide();
};

/**
 * @function FieldMappingComponent.prototype.showError
 * @description 显示错误提示
 * @param {string} message - 错误消息
 */
FieldMappingComponent.prototype.showError = function(message) {
    SmartOffice.Components.Toast.show({
        message: message,
        type: 'error',
        duration: 5000
    });
};

/**
 * @function FieldMappingComponent.prototype.loadCurrentMapping
 * @description 加载当前映射状态
 */
FieldMappingComponent.prototype.loadCurrentMapping = function() {
    try {
        const currentData = this.fieldMappingManager.loadCurrentMapping();
        if (currentData) {
            this.currentFileData = currentData.fileData;
            this.sourceFields = currentData.sourceFields || [];
            this.currentMappings = currentData.mappings || [];

            // 如果有当前数据，显示映射界面
            if (this.sourceFields.length > 0) {
                this.showMappingSection();
                this.renderSourceFields();
                this.renderTargetFields();
                this.updateMappingDisplay();
                this.showActionButtons();
            }
        }
    } catch (error) {
        SmartOffice.log('error', '加载当前映射状态失败:', error);
    }
};

/**
 * @function FieldMappingComponent.prototype.saveCurrentMapping
 * @description 保存当前映射状态
 */
FieldMappingComponent.prototype.saveCurrentMapping = function() {
    try {
        const success = this.fieldMappingManager.saveCurrentMapping(this.currentMappings);
        if (success) {
            SmartOffice.Components.Toast.show({
                message: '映射关系已保存',
                type: 'success',
                duration: 3000
            });
        }
    } catch (error) {
        SmartOffice.log('error', '保存当前映射状态失败:', error);
        this.showError('保存映射关系失败: ' + error.message);
    }
};

// 创建全局字段映射组件实例
SmartOffice.Components.FieldMapping = FieldMappingComponent;

SmartOffice.log('info', 'SmartOffice字段映射主界面组件模块初始化完成 - 重构版本2.0.0');
