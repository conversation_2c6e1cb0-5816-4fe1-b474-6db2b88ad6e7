/**
 * @file SmartOffice映射配置对话框组件
 * @description 提供拖拽式字段映射配置和智能映射建议显示功能
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function MappingDialogComponent
 * @description 映射配置对话框组件构造函数
 * @constructor
 * @param {Object} options - 组件配置选项
 */
function MappingDialogComponent(options) {
    /**
     * @property {Object} options - 组件配置
     */
    this.options = Object.assign({
        containerId: 'mappingDialog',
        maxMappings: 50,
        enableDragDrop: true,
        enableSmartSuggestions: true,
        onSave: null,
        onCancel: null
    }, options || {});

    /**
     * @property {HTMLElement} dialog - 对话框元素
     */
    this.dialog = null;

    /**
     * @property {HTMLElement} overlay - 遮罩层元素
     */
    this.overlay = null;

    /**
     * @property {Object} fieldMapper - 字段映射器引用
     */
    this.fieldMapper = SmartOffice.Data.FieldMapper;

    /**
     * @property {Object} storage - 存储管理器引用
     */
    this.storage = SmartOffice.Core.Storage;

    /**
     * @property {Object} eventBus - 事件总线引用
     */
    this.eventBus = SmartOffice.Core.EventBus;

    /**
     * @property {Object} currentTemplate - 当前编辑的模板
     */
    this.currentTemplate = null;

    /**
     * @property {Array} sourceFields - 源字段列表
     */
    this.sourceFields = [];

    /**
     * @property {Array} standardFields - 标准字段列表
     */
    this.standardFields = [];

    /**
     * @property {Array} currentMappings - 当前映射配置
     */
    this.currentMappings = [];

    /**
     * @property {Array} smartSuggestions - 智能映射建议
     */
    this.smartSuggestions = [];

    /**
     * @property {boolean} isVisible - 对话框是否可见
     */
    this.isVisible = false;

    /**
     * @property {Object} dragState - 拖拽状态
     */
    this.dragState = {
        isDragging: false,
        dragElement: null,
        dragData: null,
        dropZone: null
    };

    SmartOffice.log('info', 'MappingDialogComponent映射配置对话框组件初始化完成');
}

/**
 * @function MappingDialogComponent.prototype.init
 * @description 初始化组件
 */
MappingDialogComponent.prototype.init = function() {
    try {
        // 创建对话框结构
        this.createDialog();

        // 绑定事件
        this.bindEvents();

        // 加载标准字段
        this.loadStandardFields();

        SmartOffice.log('info', '映射配置对话框组件初始化成功');
    } catch (error) {
        SmartOffice.log('error', '映射配置对话框组件初始化失败:', error);
    }
};

/**
 * @function MappingDialogComponent.prototype.createDialog
 * @description 创建对话框结构
 */
MappingDialogComponent.prototype.createDialog = function() {
    // 创建遮罩层
    this.overlay = document.createElement('div');
    this.overlay.className = 'mapping-dialog-overlay';
    this.overlay.style.display = 'none';

    // 创建对话框
    this.dialog = document.createElement('div');
    this.dialog.className = 'mapping-dialog';
    this.dialog.innerHTML = this.getDialogHTML();

    this.overlay.appendChild(this.dialog);
    document.body.appendChild(this.overlay);
};

/**
 * @function MappingDialogComponent.prototype.getDialogHTML
 * @description 获取对话框HTML结构
 * @returns {string} HTML字符串
 */
MappingDialogComponent.prototype.getDialogHTML = function() {
    return `
        <div class="dialog-header">
            <h2 class="dialog-title" id="dialogTitle">新建映射模板</h2>
            <button type="button" class="dialog-close" id="dialogClose">
                <svg viewBox="0 0 24 24">
                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
            </button>
        </div>

        <div class="dialog-content">
            <!-- 基本信息表单 -->
            <div class="template-info-section">
                <div class="form-group">
                    <label for="templateName" class="form-label">模板名称 *</label>
                    <input type="text" id="templateName" class="form-input" placeholder="请输入映射模板名称" maxlength="100" required />
                </div>
                
                <div class="form-group">
                    <label for="templateDescription" class="form-label">模板描述</label>
                    <textarea id="templateDescription" class="form-textarea" placeholder="请输入模板描述（可选）" maxlength="500" rows="3"></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="templateCategory" class="form-label">模板类别</label>
                        <select id="templateCategory" class="form-select">
                            <option value="custom">自定义</option>
                            <option value="sales">销售</option>
                            <option value="finance">财务</option>
                            <option value="hr">人事</option>
                            <option value="inventory">库存</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" id="isDefaultTemplate" class="form-checkbox" />
                            设为默认模板
                        </label>
                    </div>
                </div>
            </div>

            <!-- 智能建议区域 -->
            <div class="smart-suggestions-section" id="smartSuggestionsSection" style="display: none;">
                <div class="section-header">
                    <h3 class="section-title">智能映射建议</h3>
                    <button type="button" class="btn-secondary btn-apply-suggestions" id="applySuggestionsBtn">
                        应用所有建议
                    </button>
                </div>
                <div class="suggestions-list" id="suggestionsList">
                    <!-- 智能建议将在这里动态生成 -->
                </div>
            </div>

            <!-- 字段映射配置区域 -->
            <div class="mapping-config-section">
                <div class="section-header">
                    <h3 class="section-title">字段映射配置</h3>
                    <div class="mapping-actions">
                        <button type="button" class="btn-secondary btn-load-sample" id="loadSampleBtn">
                            加载示例数据
                        </button>
                        <button type="button" class="btn-secondary btn-clear-mappings" id="clearMappingsBtn">
                            清空映射
                        </button>
                    </div>
                </div>

                <div class="mapping-workspace">
                    <!-- 源字段列表 -->
                    <div class="source-fields-panel">
                        <div class="panel-header">
                            <h4 class="panel-title">源字段</h4>
                            <div class="field-upload-area" id="fieldUploadArea">
                                <svg class="upload-icon" viewBox="0 0 24 24">
                                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                                </svg>
                                <p class="upload-text">拖拽文件到此处或<span class="upload-link">点击上传</span></p>
                                <input type="file" id="fieldFileInput" accept=".csv,.xlsx,.xls" style="display: none;" />
                            </div>
                        </div>
                        <div class="fields-list" id="sourceFieldsList">
                            <!-- 源字段将在这里动态生成 -->
                        </div>
                    </div>

                    <!-- 映射配置区域 -->
                    <div class="mapping-area">
                        <div class="mapping-list" id="mappingList">
                            <!-- 映射配置将在这里动态生成 -->
                        </div>
                        
                        <button type="button" class="btn-secondary btn-add-mapping" id="addMappingBtn">
                            <svg class="btn-icon" viewBox="0 0 24 24">
                                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                            </svg>
                            添加映射
                        </button>
                    </div>

                    <!-- 标准字段列表 -->
                    <div class="target-fields-panel">
                        <div class="panel-header">
                            <h4 class="panel-title">标准字段</h4>
                            <div class="field-search">
                                <input type="text" id="standardFieldSearch" class="field-search-input" placeholder="搜索标准字段..." />
                            </div>
                        </div>
                        <div class="fields-list" id="standardFieldsList">
                            <!-- 标准字段将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 映射预览区域 -->
            <div class="mapping-preview-section" id="mappingPreviewSection" style="display: none;">
                <div class="section-header">
                    <h3 class="section-title">映射预览</h3>
                    <button type="button" class="btn-secondary btn-toggle-preview" id="togglePreviewBtn">
                        隐藏预览
                    </button>
                </div>
                <div class="preview-content" id="previewContent">
                    <!-- 映射预览将在这里动态生成 -->
                </div>
            </div>
        </div>

        <div class="dialog-footer">
            <button type="button" class="btn-secondary" id="cancelBtn">取消</button>
            <button type="button" class="btn-primary" id="saveBtn">保存模板</button>
        </div>
    `;
};

/**
 * @function MappingDialogComponent.prototype.bindEvents
 * @description 绑定事件监听器
 */
MappingDialogComponent.prototype.bindEvents = function() {
    const self = this;

    // 对话框关闭事件
    const closeBtn = this.dialog.querySelector('#dialogClose');
    const cancelBtn = this.dialog.querySelector('#cancelBtn');

    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            self.hide();
        });
    }

    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            self.hide();
        });
    }

    // 遮罩层点击关闭
    this.overlay.addEventListener('click', function(e) {
        if (e.target === self.overlay) {
            self.hide();
        }
    });

    // ESC键关闭
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && self.isVisible) {
            self.hide();
        }
    });

    // 保存按钮
    const saveBtn = this.dialog.querySelector('#saveBtn');
    if (saveBtn) {
        saveBtn.addEventListener('click', function() {
            self.saveTemplate();
        });
    }

    // 应用智能建议
    const applySuggestionsBtn = this.dialog.querySelector('#applySuggestionsBtn');
    if (applySuggestionsBtn) {
        applySuggestionsBtn.addEventListener('click', function() {
            self.applyAllSuggestions();
        });
    }

    // 添加映射
    const addMappingBtn = this.dialog.querySelector('#addMappingBtn');
    if (addMappingBtn) {
        addMappingBtn.addEventListener('click', function() {
            self.addMapping();
        });
    }

    // 清空映射
    const clearMappingsBtn = this.dialog.querySelector('#clearMappingsBtn');
    if (clearMappingsBtn) {
        clearMappingsBtn.addEventListener('click', function() {
            self.clearMappings();
        });
    }

    // 加载示例数据
    const loadSampleBtn = this.dialog.querySelector('#loadSampleBtn');
    if (loadSampleBtn) {
        loadSampleBtn.addEventListener('click', function() {
            self.loadSampleData();
        });
    }

    // 文件上传
    const fieldFileInput = this.dialog.querySelector('#fieldFileInput');
    const fieldUploadArea = this.dialog.querySelector('#fieldUploadArea');

    if (fieldUploadArea) {
        fieldUploadArea.addEventListener('click', function() {
            fieldFileInput.click();
        });

        fieldUploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('drag-over');
        });

        fieldUploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('drag-over');
        });

        fieldUploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('drag-over');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                self.handleFileUpload(files[0]);
            }
        });
    }

    if (fieldFileInput) {
        fieldFileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                self.handleFileUpload(this.files[0]);
            }
        });
    }

    // 标准字段搜索
    const standardFieldSearch = this.dialog.querySelector('#standardFieldSearch');
    if (standardFieldSearch) {
        standardFieldSearch.addEventListener('input', function() {
            self.filterStandardFields(this.value.trim());
        });
    }

    // 切换预览
    const togglePreviewBtn = this.dialog.querySelector('#togglePreviewBtn');
    if (togglePreviewBtn) {
        togglePreviewBtn.addEventListener('click', function() {
            self.togglePreview();
        });
    }
};

/**
 * @function MappingDialogComponent.prototype.show
 * @description 显示对话框
 * @param {Object} template - 要编辑的模板（可选）
 */
MappingDialogComponent.prototype.show = function(template) {
    this.currentTemplate = template || null;
    this.isVisible = true;

    // 重置表单
    this.resetForm();

    // 如果是编辑模式，填充数据
    if (template) {
        this.populateForm(template);
    }

    // 显示对话框
    this.overlay.style.display = 'flex';
    document.body.classList.add('dialog-open');

    // 添加显示动画
    setTimeout(() => {
        this.overlay.classList.add('dialog-visible');
    }, 10);

    // 聚焦到名称输入框
    const nameInput = this.dialog.querySelector('#templateName');
    if (nameInput) {
        setTimeout(() => {
            nameInput.focus();
        }, 300);
    }
};

/**
 * @function MappingDialogComponent.prototype.hide
 * @description 隐藏对话框
 */
MappingDialogComponent.prototype.hide = function() {
    this.isVisible = false;

    // 添加隐藏动画
    this.overlay.classList.remove('dialog-visible');

    setTimeout(() => {
        this.overlay.style.display = 'none';
        document.body.classList.remove('dialog-open');

        // 触发取消回调
        if (this.options.onCancel && typeof this.options.onCancel === 'function') {
            this.options.onCancel();
        }
    }, 300);
};

/**
 * @function MappingDialogComponent.prototype.resetForm
 * @description 重置表单
 */
MappingDialogComponent.prototype.resetForm = function() {
    // 重置基本信息
    const nameInput = this.dialog.querySelector('#templateName');
    const descInput = this.dialog.querySelector('#templateDescription');
    const categorySelect = this.dialog.querySelector('#templateCategory');
    const isDefaultCheckbox = this.dialog.querySelector('#isDefaultTemplate');

    if (nameInput) nameInput.value = '';
    if (descInput) descInput.value = '';
    if (categorySelect) categorySelect.value = 'custom';
    if (isDefaultCheckbox) isDefaultCheckbox.checked = false;

    // 重置映射数据
    this.currentMappings = [];
    this.sourceFields = [];
    this.smartSuggestions = [];

    // 更新标题
    const dialogTitle = this.dialog.querySelector('#dialogTitle');
    if (dialogTitle) {
        dialogTitle.textContent = this.currentTemplate ? '编辑映射模板' : '新建映射模板';
    }

    // 隐藏智能建议和预览区域
    const suggestionsSection = this.dialog.querySelector('#smartSuggestionsSection');
    const previewSection = this.dialog.querySelector('#mappingPreviewSection');

    if (suggestionsSection) suggestionsSection.style.display = 'none';
    if (previewSection) previewSection.style.display = 'none';

    // 重新渲染列表
    this.renderSourceFields();
    this.renderMappingList();
    this.renderStandardFields();
};

/**
 * @function MappingDialogComponent.prototype.populateForm
 * @description 填充表单数据（编辑模式）
 * @param {Object} template - 模板数据
 */
MappingDialogComponent.prototype.populateForm = function(template) {
    // 填充基本信息
    const nameInput = this.dialog.querySelector('#templateName');
    const descInput = this.dialog.querySelector('#templateDescription');
    const categorySelect = this.dialog.querySelector('#templateCategory');
    const isDefaultCheckbox = this.dialog.querySelector('#isDefaultTemplate');

    if (nameInput) nameInput.value = template.name || '';
    if (descInput) descInput.value = template.description || '';
    if (categorySelect) categorySelect.value = template.category || 'custom';
    if (isDefaultCheckbox) isDefaultCheckbox.checked = template.isDefault || false;

    // 填充映射数据
    this.currentMappings = template.mappings ? JSON.parse(JSON.stringify(template.mappings)) : [];

    // 重新渲染
    this.renderMappingList();
    this.updatePreview();
};

/**
 * @function MappingDialogComponent.prototype.loadStandardFields
 * @description 加载标准字段
 */
MappingDialogComponent.prototype.loadStandardFields = function() {
    this.standardFields = this.fieldMapper.standardFields || [];
    this.renderStandardFields();
};

/**
 * @function MappingDialogComponent.prototype.renderStandardFields
 * @description 渲染标准字段列表
 * @param {string} searchQuery - 搜索查询（可选）
 */
MappingDialogComponent.prototype.renderStandardFields = function(searchQuery) {
    const container = this.dialog.querySelector('#standardFieldsList');
    if (!container) return;

    let fields = this.standardFields;

    // 搜索过滤
    if (searchQuery) {
        const query = searchQuery.toLowerCase();
        fields = fields.filter(field =>
            field.name.toLowerCase().includes(query) ||
            field.label.toLowerCase().includes(query) ||
            field.category.toLowerCase().includes(query)
        );
    }

    // 按类别分组
    const groupedFields = {};
    fields.forEach(field => {
        if (!groupedFields[field.category]) {
            groupedFields[field.category] = [];
        }
        groupedFields[field.category].push(field);
    });

    let html = '';
    for (const category in groupedFields) {
        const categoryLabel = this.getCategoryLabel(category);
        html += `
            <div class="field-group">
                <div class="field-group-header">${categoryLabel}</div>
                <div class="field-group-content">
        `;

        groupedFields[category].forEach(field => {
            html += `
                <div class="standard-field-item"
                     data-field-name="${field.name}"
                     data-field-type="${field.type}"
                     data-field-category="${field.category}"
                     draggable="true">
                    <div class="field-info">
                        <span class="field-name">${field.label}</span>
                        <span class="field-type-badge type-${field.type}">${field.type}</span>
                    </div>
                    <div class="field-description">${field.name}</div>
                </div>
            `;
        });

        html += `
                </div>
            </div>
        `;
    }

    container.innerHTML = html;

    // 绑定拖拽事件
    this.bindStandardFieldDragEvents();
};

/**
 * @function MappingDialogComponent.prototype.renderSourceFields
 * @description 渲染源字段列表
 */
MappingDialogComponent.prototype.renderSourceFields = function() {
    const container = this.dialog.querySelector('#sourceFieldsList');
    if (!container) return;

    if (this.sourceFields.length === 0) {
        container.innerHTML = `
            <div class="empty-fields">
                <p>暂无源字段数据</p>
                <p class="empty-hint">请上传文件或加载示例数据</p>
            </div>
        `;
        return;
    }

    let html = '';
    this.sourceFields.forEach(field => {
        const fieldName = typeof field === 'string' ? field : field.name;
        const fieldType = typeof field === 'object' ? field.type : 'string';

        html += `
            <div class="source-field-item"
                 data-field-name="${fieldName}"
                 data-field-type="${fieldType}"
                 draggable="true">
                <div class="field-info">
                    <span class="field-name">${SmartOffice.Utils.Helpers.escapeHtml(fieldName)}</span>
                    <span class="field-type-badge type-${fieldType}">${fieldType}</span>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;

    // 绑定拖拽事件
    this.bindSourceFieldDragEvents();

    // 生成智能建议
    this.generateSmartSuggestions();
};

/**
 * @function MappingDialogComponent.prototype.renderMappingList
 * @description 渲染映射配置列表
 */
MappingDialogComponent.prototype.renderMappingList = function() {
    const container = this.dialog.querySelector('#mappingList');
    if (!container) return;

    if (this.currentMappings.length === 0) {
        container.innerHTML = `
            <div class="empty-mappings">
                <p>暂无字段映射</p>
                <p class="empty-hint">拖拽字段到此处创建映射，或点击"添加映射"按钮</p>
            </div>
        `;
        return;
    }

    let html = '';
    this.currentMappings.forEach((mapping, index) => {
        html += this.renderMappingItem(mapping, index);
    });

    container.innerHTML = html;

    // 绑定映射项事件
    this.bindMappingItemEvents();

    // 更新预览
    this.updatePreview();
};

/**
 * @function MappingDialogComponent.prototype.renderMappingItem
 * @description 渲染单个映射项
 * @param {Object} mapping - 映射配置
 * @param {number} index - 索引
 * @returns {string} 映射项HTML
 */
MappingDialogComponent.prototype.renderMappingItem = function(mapping, index) {
    const confidence = mapping.confidence || 0;
    const confidenceClass = confidence >= 0.8 ? 'high' : confidence >= 0.6 ? 'medium' : 'low';
    const confidenceText = Math.round(confidence * 100) + '%';

    return `
        <div class="mapping-item" data-mapping-index="${index}">
            <div class="mapping-content">
                <div class="source-field-section">
                    <label class="field-label">源字段</label>
                    <input type="text" class="mapping-input source-field-input"
                           value="${SmartOffice.Utils.Helpers.escapeHtml(mapping.sourceField || '')}"
                           placeholder="选择或输入源字段" />
                </div>

                <div class="mapping-arrow">
                    <svg viewBox="0 0 24 24">
                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                    </svg>
                </div>

                <div class="target-field-section">
                    <label class="field-label">目标字段</label>
                    <select class="mapping-select target-field-select">
                        <option value="">选择标准字段</option>
                        ${this.getStandardFieldOptions(mapping.targetField)}
                    </select>
                </div>

                <div class="data-type-section">
                    <label class="field-label">数据类型</label>
                    <select class="mapping-select data-type-select">
                        <option value="string" ${mapping.dataType === 'string' ? 'selected' : ''}>文本</option>
                        <option value="number" ${mapping.dataType === 'number' ? 'selected' : ''}>数值</option>
                        <option value="date" ${mapping.dataType === 'date' ? 'selected' : ''}>日期</option>
                        <option value="time" ${mapping.dataType === 'time' ? 'selected' : ''}>时间</option>
                    </select>
                </div>

                ${confidence > 0 ? `
                    <div class="confidence-section">
                        <span class="confidence-label">置信度</span>
                        <span class="confidence-value confidence-${confidenceClass}">${confidenceText}</span>
                    </div>
                ` : ''}

                <div class="mapping-actions">
                    <button type="button" class="mapping-action-btn required-toggle ${mapping.required ? 'active' : ''}"
                            title="必需字段" data-action="toggle-required">
                        <svg viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                    </button>
                    <button type="button" class="mapping-action-btn delete-mapping"
                            title="删除映射" data-action="delete">
                        <svg viewBox="0 0 24 24">
                            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    `;
};

/**
 * @function MappingDialogComponent.prototype.getStandardFieldOptions
 * @description 获取标准字段选项HTML
 * @param {string} selectedValue - 选中的值
 * @returns {string} 选项HTML
 */
MappingDialogComponent.prototype.getStandardFieldOptions = function(selectedValue) {
    let html = '';

    // 按类别分组
    const groupedFields = {};
    this.standardFields.forEach(field => {
        if (!groupedFields[field.category]) {
            groupedFields[field.category] = [];
        }
        groupedFields[field.category].push(field);
    });

    for (const category in groupedFields) {
        const categoryLabel = this.getCategoryLabel(category);
        html += `<optgroup label="${categoryLabel}">`;

        groupedFields[category].forEach(field => {
            const selected = field.name === selectedValue ? 'selected' : '';
            html += `<option value="${field.name}" ${selected}>${field.label} (${field.name})</option>`;
        });

        html += '</optgroup>';
    }

    return html;
};

/**
 * @function MappingDialogComponent.prototype.getCategoryLabel
 * @description 获取类别标签
 * @param {string} category - 类别代码
 * @returns {string} 类别标签
 */
MappingDialogComponent.prototype.getCategoryLabel = function(category) {
    const labels = {
        financial: '财务',
        quantity: '数量',
        temporal: '时间',
        classification: '分类',
        location: '位置',
        product: '产品',
        entity: '实体',
        status: '状态',
        priority: '优先级',
        identifier: '标识',
        sales: '销售',
        finance: '财务',
        hr: '人事',
        inventory: '库存',
        custom: '自定义'
    };
    return labels[category] || '其他';
};

/**
 * @function MappingDialogComponent.prototype.generateSmartSuggestions
 * @description 生成智能映射建议
 */
MappingDialogComponent.prototype.generateSmartSuggestions = function() {
    if (!this.options.enableSmartSuggestions || this.sourceFields.length === 0) {
        return;
    }

    // 获取源字段名称列表
    const sourceFieldNames = this.sourceFields.map(field =>
        typeof field === 'string' ? field : field.name
    );

    // 生成智能建议
    this.smartSuggestions = this.fieldMapper.autoSuggestMapping(sourceFieldNames, {
        minConfidence: 0.6,
        maxSuggestions: 10
    });

    // 渲染建议
    this.renderSmartSuggestions();
};

/**
 * @function MappingDialogComponent.prototype.renderSmartSuggestions
 * @description 渲染智能映射建议
 */
MappingDialogComponent.prototype.renderSmartSuggestions = function() {
    const section = this.dialog.querySelector('#smartSuggestionsSection');
    const container = this.dialog.querySelector('#suggestionsList');

    if (!section || !container) return;

    if (this.smartSuggestions.length === 0) {
        section.style.display = 'none';
        return;
    }

    section.style.display = 'block';

    let html = '';
    this.smartSuggestions.forEach((suggestion, index) => {
        const confidenceClass = suggestion.confidence >= 0.8 ? 'high' :
                               suggestion.confidence >= 0.7 ? 'medium' : 'low';
        const confidenceText = Math.round(suggestion.confidence * 100) + '%';

        html += `
            <div class="suggestion-item" data-suggestion-index="${index}">
                <div class="suggestion-content">
                    <div class="suggestion-mapping">
                        <span class="source-field">${SmartOffice.Utils.Helpers.escapeHtml(suggestion.sourceField)}</span>
                        <svg class="mapping-arrow" viewBox="0 0 24 24">
                            <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                        </svg>
                        <span class="target-field">${suggestion.targetField}</span>
                        <span class="data-type-badge type-${suggestion.dataType}">${suggestion.dataType}</span>
                    </div>
                    <div class="suggestion-meta">
                        <span class="confidence-badge confidence-${confidenceClass}">${confidenceText}</span>
                        <span class="reason-text">${suggestion.reason}</span>
                    </div>
                </div>
                <div class="suggestion-actions">
                    <button type="button" class="btn-secondary btn-apply-suggestion" data-suggestion-index="${index}">
                        应用
                    </button>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;

    // 绑定建议事件
    this.bindSuggestionEvents();
};

// 工具方法和事件处理
MappingDialogComponent.prototype.addMapping = function(sourceField, targetField, dataType) {
    const mapping = {
        sourceField: sourceField || '',
        targetField: targetField || '',
        dataType: dataType || 'string',
        required: false,
        confidence: 0
    };

    this.currentMappings.push(mapping);
    this.renderMappingList();
};

MappingDialogComponent.prototype.removeMapping = function(index) {
    if (index >= 0 && index < this.currentMappings.length) {
        this.currentMappings.splice(index, 1);
        this.renderMappingList();
    }
};

MappingDialogComponent.prototype.clearMappings = function() {
    if (this.currentMappings.length > 0) {
        const confirmed = confirm('确定要清空所有映射配置吗？');
        if (confirmed) {
            this.currentMappings = [];
            this.renderMappingList();
        }
    }
};

MappingDialogComponent.prototype.applyAllSuggestions = function() {
    this.smartSuggestions.forEach(suggestion => {
        this.addMapping(suggestion.sourceField, suggestion.targetField, suggestion.dataType);
    });

    // 隐藏建议区域
    const section = this.dialog.querySelector('#smartSuggestionsSection');
    if (section) {
        section.style.display = 'none';
    }
};

MappingDialogComponent.prototype.applySuggestion = function(index) {
    const suggestion = this.smartSuggestions[index];
    if (suggestion) {
        this.addMapping(suggestion.sourceField, suggestion.targetField, suggestion.dataType);
    }
};

MappingDialogComponent.prototype.loadSampleData = function() {
    this.sourceFields = [
        { name: '销售金额', type: 'number' },
        { name: '产品名称', type: 'string' },
        { name: '客户姓名', type: 'string' },
        { name: '订单日期', type: 'date' },
        { name: '数量', type: 'number' },
        { name: '区域', type: 'string' },
        { name: '销售员', type: 'string' }
    ];

    this.renderSourceFields();
};

MappingDialogComponent.prototype.handleFileUpload = function(file) {
    // 这里应该解析文件并提取字段信息
    // 为了演示，我们使用示例数据
    this.loadSampleData();
    this.showMessage('文件上传成功，已提取字段信息', 'success');
};

MappingDialogComponent.prototype.filterStandardFields = function(query) {
    this.renderStandardFields(query);
};

MappingDialogComponent.prototype.updatePreview = function() {
    const section = this.dialog.querySelector('#mappingPreviewSection');
    const container = this.dialog.querySelector('#previewContent');

    if (!section || !container) return;

    if (this.currentMappings.length === 0) {
        section.style.display = 'none';
        return;
    }

    section.style.display = 'block';

    let html = '<div class="preview-table">';
    html += '<div class="preview-header">';
    html += '<div class="preview-col">源字段</div>';
    html += '<div class="preview-col">目标字段</div>';
    html += '<div class="preview-col">数据类型</div>';
    html += '<div class="preview-col">必需</div>';
    html += '</div>';

    this.currentMappings.forEach(mapping => {
        html += '<div class="preview-row">';
        html += `<div class="preview-col">${SmartOffice.Utils.Helpers.escapeHtml(mapping.sourceField)}</div>`;
        html += `<div class="preview-col">${mapping.targetField}</div>`;
        html += `<div class="preview-col"><span class="type-badge type-${mapping.dataType}">${mapping.dataType}</span></div>`;
        html += `<div class="preview-col">${mapping.required ? '是' : '否'}</div>`;
        html += '</div>';
    });

    html += '</div>';
    container.innerHTML = html;
};

MappingDialogComponent.prototype.togglePreview = function() {
    const section = this.dialog.querySelector('#mappingPreviewSection');
    const btn = this.dialog.querySelector('#togglePreviewBtn');

    if (section && btn) {
        const isVisible = section.style.display !== 'none';
        section.style.display = isVisible ? 'none' : 'block';
        btn.textContent = isVisible ? '显示预览' : '隐藏预览';
    }
};

MappingDialogComponent.prototype.saveTemplate = function() {
    // 验证表单
    const validation = this.validateForm();
    if (!validation.isValid) {
        this.showMessage(validation.errors[0], 'error');
        return;
    }

    // 构建模板数据
    const template = this.buildTemplateData();

    // 保存模板
    const success = this.fieldMapper.saveMappingTemplate(template);
    if (success) {
        this.showMessage('映射模板保存成功', 'success');

        // 触发保存回调
        if (this.options.onSave && typeof this.options.onSave === 'function') {
            this.options.onSave(template);
        }

        this.hide();
    } else {
        this.showMessage('保存映射模板失败', 'error');
    }
};

MappingDialogComponent.prototype.validateForm = function() {
    const result = { isValid: true, errors: [] };

    const nameInput = this.dialog.querySelector('#templateName');
    if (!nameInput || !nameInput.value.trim()) {
        result.isValid = false;
        result.errors.push('请输入模板名称');
    }

    if (this.currentMappings.length === 0) {
        result.isValid = false;
        result.errors.push('请至少配置一个字段映射');
    }

    // 验证映射配置
    for (let i = 0; i < this.currentMappings.length; i++) {
        const mapping = this.currentMappings[i];
        if (!mapping.sourceField || !mapping.targetField) {
            result.isValid = false;
            result.errors.push(`第${i + 1}个映射配置不完整`);
            break;
        }
    }

    return result;
};

MappingDialogComponent.prototype.buildTemplateData = function() {
    const nameInput = this.dialog.querySelector('#templateName');
    const descInput = this.dialog.querySelector('#templateDescription');
    const categorySelect = this.dialog.querySelector('#templateCategory');
    const isDefaultCheckbox = this.dialog.querySelector('#isDefaultTemplate');

    const template = {
        id: this.currentTemplate ? this.currentTemplate.id : SmartOffice.Utils.Helpers.generateId('mapping'),
        name: nameInput.value.trim(),
        description: descInput.value.trim(),
        category: categorySelect.value,
        isDefault: isDefaultCheckbox.checked,
        mappings: JSON.parse(JSON.stringify(this.currentMappings)),
        updatedAt: new Date().toISOString()
    };

    if (!this.currentTemplate) {
        template.createdAt = new Date().toISOString();
        template.usageCount = 0;
    } else {
        template.createdAt = this.currentTemplate.createdAt;
        template.usageCount = this.currentTemplate.usageCount || 0;
    }

    return template;
};

// 事件绑定方法（简化版本，实际实现会更复杂）
MappingDialogComponent.prototype.bindStandardFieldDragEvents = function() {
    // 拖拽事件绑定
};

MappingDialogComponent.prototype.bindSourceFieldDragEvents = function() {
    // 拖拽事件绑定
};

MappingDialogComponent.prototype.bindMappingItemEvents = function() {
    // 映射项事件绑定
};

MappingDialogComponent.prototype.bindSuggestionEvents = function() {
    // 建议事件绑定
};

MappingDialogComponent.prototype.showMessage = function(message, type) {
    this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, message, type);
};

// 注册组件
SmartOffice.Components.MappingDialog = MappingDialogComponent;

SmartOffice.log('info', 'SmartOffice映射配置对话框组件模块初始化完成');
