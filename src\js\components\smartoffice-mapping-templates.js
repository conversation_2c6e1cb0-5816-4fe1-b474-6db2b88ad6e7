/**
 * @file SmartOffice字段映射模板管理组件 - 重构版本
 * @description 提供字段映射模板的保存、加载、删除、应用功能的专用组件
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2025-01-03 重构为通用字段映射管理模块
 */

/**
 * @function MappingTemplatesComponent
 * @description 字段映射模板管理组件构造函数
 * @constructor
 * @param {Object} options - 组件配置选项
 */
function MappingTemplatesComponent(options) {
    /**
     * @property {Object} options - 组件配置
     */
    this.options = Object.assign({
        containerId: 'mappingTemplatesContainer',
        showBuiltinTemplates: true,
        enableTemplateCreation: true,
        enableTemplateEditing: true,
        maxTemplates: 100
    }, options || {});

    /**
     * @property {HTMLElement} container - 容器元素
     */
    this.container = null;

    /**
     * @property {Object} fieldMappingManager - 字段映射管理器引用
     */
    this.fieldMappingManager = SmartOffice.Data.FieldMappingManager;

    /**
     * @property {Object} storage - 存储管理器引用
     */
    this.storage = SmartOffice.Core.Storage;

    /**
     * @property {Object} eventBus - 事件总线引用
     */
    this.eventBus = SmartOffice.Core.EventBus;

    /**
     * @property {Array} templates - 映射模板列表
     */
    this.templates = [];

    /**
     * @property {Array} builtinTemplates - 内置模板列表
     */
    this.builtinTemplates = [];

    /**
     * @property {Array} userTemplates - 用户自定义模板列表
     */
    this.userTemplates = [];

    /**
     * @property {Object} currentTemplate - 当前选中的模板
     */
    this.currentTemplate = null;

    /**
     * @property {string} storageKey - 用户模板存储键名
     */
    this.storageKey = 'field_mapping_user_templates';

    SmartOffice.log('info', 'MappingTemplatesComponent字段映射模板管理组件初始化完成');
}

/**
 * @function MappingTemplatesComponent.prototype.init
 * @description 初始化组件
 */
MappingTemplatesComponent.prototype.init = function() {
    try {
        this.container = document.getElementById(this.options.containerId);
        if (!this.container) {
            throw new Error('未找到容器元素: ' + this.options.containerId);
        }

        // 加载内置模板
        this.loadBuiltinTemplates();

        // 加载用户模板
        this.loadUserTemplates();

        // 渲染模板界面
        this.render();

        // 绑定事件
        this.bindEvents();

        SmartOffice.log('info', '字段映射模板管理组件初始化成功');
    } catch (error) {
        SmartOffice.log('error', '字段映射模板管理组件初始化失败:', error);
    }
};

/**
 * @function MappingTemplatesComponent.prototype.loadBuiltinTemplates
 * @description 加载内置映射模板
 */
MappingTemplatesComponent.prototype.loadBuiltinTemplates = function() {
    this.builtinTemplates = [
        {
            id: 'sales-basic',
            name: '基础销售数据映射',
            description: '适用于基本销售数据的字段映射模板',
            category: 'sales',
            categoryLabel: '销售',
            isBuiltin: true,
            mappings: [
                { sourceField: '销售额', targetField: 'amount', dataType: 'number', confidence: 1.0 },
                { sourceField: '数量', targetField: 'quantity', dataType: 'number', confidence: 1.0 },
                { sourceField: '日期', targetField: 'date', dataType: 'date', confidence: 1.0 },
                { sourceField: '产品', targetField: 'product', dataType: 'string', confidence: 1.0 },
                { sourceField: '客户', targetField: 'customer', dataType: 'string', confidence: 1.0 },
                { sourceField: '地区', targetField: 'region', dataType: 'string', confidence: 1.0 }
            ],
            createdAt: new Date().toISOString(),
            usageCount: 0
        },
        {
            id: 'finance-basic',
            name: '基础财务数据映射',
            description: '适用于基本财务数据的字段映射模板',
            category: 'finance',
            categoryLabel: '财务',
            isBuiltin: true,
            mappings: [
                { sourceField: '金额', targetField: 'amount', dataType: 'number', confidence: 1.0 },
                { sourceField: '收入', targetField: 'revenue', dataType: 'number', confidence: 1.0 },
                { sourceField: '支出', targetField: 'amount', dataType: 'number', confidence: 0.8 },
                { sourceField: '日期', targetField: 'date', dataType: 'date', confidence: 1.0 },
                { sourceField: '科目', targetField: 'category', dataType: 'string', confidence: 1.0 },
                { sourceField: '部门', targetField: 'category', dataType: 'string', confidence: 0.8 }
            ],
            createdAt: new Date().toISOString(),
            usageCount: 0
        },
        {
            id: 'inventory-basic',
            name: '基础库存数据映射',
            description: '适用于基本库存数据的字段映射模板',
            category: 'inventory',
            categoryLabel: '库存',
            isBuiltin: true,
            mappings: [
                { sourceField: '库存数量', targetField: 'quantity', dataType: 'number', confidence: 1.0 },
                { sourceField: '产品名称', targetField: 'product', dataType: 'string', confidence: 1.0 },
                { sourceField: '仓库', targetField: 'region', dataType: 'string', confidence: 0.9 },
                { sourceField: '更新时间', targetField: 'modified_date', dataType: 'date', confidence: 1.0 },
                { sourceField: '状态', targetField: 'status', dataType: 'string', confidence: 1.0 },
                { sourceField: '供应商', targetField: 'supplier', dataType: 'string', confidence: 1.0 }
            ],
            createdAt: new Date().toISOString(),
            usageCount: 0
        },
        {
            id: 'hr-basic',
            name: '基础人事数据映射',
            description: '适用于基本人事数据的字段映射模板',
            category: 'hr',
            categoryLabel: '人事',
            isBuiltin: true,
            mappings: [
                { sourceField: '姓名', targetField: 'name', dataType: 'string', confidence: 1.0 },
                { sourceField: '员工编号', targetField: 'id', dataType: 'string', confidence: 1.0 },
                { sourceField: '部门', targetField: 'category', dataType: 'string', confidence: 1.0 },
                { sourceField: '职位', targetField: 'category', dataType: 'string', confidence: 0.8 },
                { sourceField: '入职日期', targetField: 'created_date', dataType: 'date', confidence: 1.0 },
                { sourceField: '薪资', targetField: 'amount', dataType: 'number', confidence: 1.0 }
            ],
            createdAt: new Date().toISOString(),
            usageCount: 0
        }
    ];

    SmartOffice.log('info', '已加载', this.builtinTemplates.length, '个内置映射模板');
};

/**
 * @function MappingTemplatesComponent.prototype.loadUserTemplates
 * @description 加载用户自定义模板
 */
MappingTemplatesComponent.prototype.loadUserTemplates = function() {
    try {
        this.userTemplates = this.storage.get(this.storageKey, []);
        SmartOffice.log('info', '已加载', this.userTemplates.length, '个用户自定义映射模板');
    } catch (error) {
        SmartOffice.log('error', '加载用户自定义模板失败:', error);
        this.userTemplates = [];
    }
};

/**
 * @function MappingTemplatesComponent.prototype.getAllTemplates
 * @description 获取所有模板列表
 * @returns {Array} 模板列表
 */
MappingTemplatesComponent.prototype.getAllTemplates = function() {
    this.templates = [];
    
    if (this.options.showBuiltinTemplates) {
        this.templates = this.templates.concat(this.builtinTemplates);
    }
    
    this.templates = this.templates.concat(this.userTemplates);
    
    return this.templates;
};

/**
 * @function MappingTemplatesComponent.prototype.getTemplatesByCategory
 * @description 按类别获取模板
 * @param {string} category - 模板类别
 * @returns {Array} 指定类别的模板列表
 */
MappingTemplatesComponent.prototype.getTemplatesByCategory = function(category) {
    const allTemplates = this.getAllTemplates();
    
    if (!category) {
        return allTemplates;
    }
    
    return allTemplates.filter(function(template) {
        return template.category === category;
    });
};

/**
 * @function MappingTemplatesComponent.prototype.getTemplateById
 * @description 根据ID获取模板
 * @param {string} templateId - 模板ID
 * @returns {Object|null} 模板对象或null
 */
MappingTemplatesComponent.prototype.getTemplateById = function(templateId) {
    const allTemplates = this.getAllTemplates();
    
    return allTemplates.find(function(template) {
        return template.id === templateId;
    }) || null;
};

/**
 * @function MappingTemplatesComponent.prototype.saveTemplate
 * @description 保存映射模板
 * @param {Object} templateData - 模板数据
 * @returns {Object} 保存的模板对象
 */
MappingTemplatesComponent.prototype.saveTemplate = function(templateData) {
    try {
        // 验证模板数据
        if (!templateData.name || !templateData.mappings || !Array.isArray(templateData.mappings)) {
            throw new Error('模板数据不完整');
        }

        // 创建模板对象
        const template = {
            id: templateData.id || this.generateTemplateId(),
            name: templateData.name,
            description: templateData.description || '',
            category: templateData.category || 'custom',
            categoryLabel: templateData.categoryLabel || '自定义',
            isBuiltin: false,
            mappings: templateData.mappings,
            createdAt: templateData.createdAt || new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            usageCount: templateData.usageCount || 0
        };

        // 检查是否为更新操作
        const existingIndex = this.userTemplates.findIndex(function(t) {
            return t.id === template.id;
        });

        if (existingIndex >= 0) {
            // 更新现有模板
            this.userTemplates[existingIndex] = template;
            SmartOffice.log('info', '更新映射模板:', template.name);
        } else {
            // 添加新模板
            this.userTemplates.push(template);
            SmartOffice.log('info', '保存新映射模板:', template.name);
        }

        // 保存到存储
        this.storage.set(this.storageKey, this.userTemplates);

        // 触发事件
        this.eventBus.emit('mappingTemplate:saved', {
            template: template,
            isNew: existingIndex < 0
        });

        return template;

    } catch (error) {
        SmartOffice.log('error', '保存映射模板失败:', error);
        throw error;
    }
};

/**
 * @function MappingTemplatesComponent.prototype.deleteTemplate
 * @description 删除映射模板
 * @param {string} templateId - 模板ID
 * @returns {boolean} 是否删除成功
 */
MappingTemplatesComponent.prototype.deleteTemplate = function(templateId) {
    try {
        // 查找模板
        const templateIndex = this.userTemplates.findIndex(function(template) {
            return template.id === templateId;
        });

        if (templateIndex < 0) {
            throw new Error('模板不存在或不可删除');
        }

        const template = this.userTemplates[templateIndex];

        // 检查是否为内置模板
        if (template.isBuiltin) {
            throw new Error('内置模板不可删除');
        }

        // 删除模板
        this.userTemplates.splice(templateIndex, 1);

        // 保存到存储
        this.storage.set(this.storageKey, this.userTemplates);

        // 触发事件
        this.eventBus.emit('mappingTemplate:deleted', {
            template: template
        });

        SmartOffice.log('info', '删除映射模板:', template.name);
        return true;

    } catch (error) {
        SmartOffice.log('error', '删除映射模板失败:', error);
        throw error;
    }
};

/**
 * @function MappingTemplatesComponent.prototype.applyTemplate
 * @description 应用映射模板
 * @param {string} templateId - 模板ID
 * @returns {Array} 映射关系列表
 */
MappingTemplatesComponent.prototype.applyTemplate = function(templateId) {
    try {
        const template = this.getTemplateById(templateId);
        if (!template) {
            throw new Error('模板不存在');
        }

        // 清除当前映射
        this.fieldMappingManager.clearCurrentMapping();

        // 应用模板映射
        const appliedMappings = [];
        for (let i = 0; i < template.mappings.length; i++) {
            const mapping = template.mappings[i];
            try {
                const newMapping = this.fieldMappingManager.addMapping(
                    mapping.sourceField,
                    mapping.targetField,
                    mapping.dataType
                );
                appliedMappings.push(newMapping);
            } catch (error) {
                SmartOffice.log('warn', '应用映射失败:', mapping.sourceField, '->', mapping.targetField, error);
            }
        }

        // 保存当前映射
        this.fieldMappingManager.saveCurrentMapping(appliedMappings);

        // 更新模板使用次数
        this.incrementTemplateUsage(templateId);

        // 触发事件
        this.eventBus.emit('mappingTemplate:applied', {
            template: template,
            mappings: appliedMappings
        });

        SmartOffice.log('info', '应用映射模板:', template.name, '成功应用', appliedMappings.length, '个映射');
        return appliedMappings;

    } catch (error) {
        SmartOffice.log('error', '应用映射模板失败:', error);
        throw error;
    }
};

/**
 * @function MappingTemplatesComponent.prototype.incrementTemplateUsage
 * @description 增加模板使用次数
 * @param {string} templateId - 模板ID
 */
MappingTemplatesComponent.prototype.incrementTemplateUsage = function(templateId) {
    try {
        // 查找用户模板
        const template = this.userTemplates.find(function(t) {
            return t.id === templateId;
        });

        if (template) {
            template.usageCount = (template.usageCount || 0) + 1;
            template.lastUsed = new Date().toISOString();

            // 保存到存储
            this.storage.set(this.storageKey, this.userTemplates);
        }

        // 内置模板的使用次数暂时不持久化，只在内存中记录
        const builtinTemplate = this.builtinTemplates.find(function(t) {
            return t.id === templateId;
        });

        if (builtinTemplate) {
            builtinTemplate.usageCount = (builtinTemplate.usageCount || 0) + 1;
            builtinTemplate.lastUsed = new Date().toISOString();
        }

    } catch (error) {
        SmartOffice.log('error', '更新模板使用次数失败:', error);
    }
};

/**
 * @function MappingTemplatesComponent.prototype.generateTemplateId
 * @description 生成模板ID
 * @returns {string} 模板ID
 */
MappingTemplatesComponent.prototype.generateTemplateId = function() {
    return 'user_template_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
};

/**
 * @function MappingTemplatesComponent.prototype.validateTemplate
 * @description 验证模板数据
 * @param {Object} template - 模板对象
 * @returns {boolean} 是否有效
 */
MappingTemplatesComponent.prototype.validateTemplate = function(template) {
    if (!template || typeof template !== 'object') {
        return false;
    }

    // 检查必需字段
    if (!template.id || !template.name || !template.mappings) {
        return false;
    }

    // 检查映射数据
    if (!Array.isArray(template.mappings)) {
        return false;
    }

    // 验证每个映射
    for (let i = 0; i < template.mappings.length; i++) {
        const mapping = template.mappings[i];
        if (!mapping.sourceField || !mapping.targetField || !mapping.dataType) {
            return false;
        }
    }

    return true;
};

/**
 * @function MappingTemplatesComponent.prototype.searchTemplates
 * @description 搜索模板
 * @param {string} keyword - 搜索关键词
 * @returns {Array} 搜索结果
 */
MappingTemplatesComponent.prototype.searchTemplates = function(keyword) {
    const allTemplates = this.getAllTemplates();

    if (!keyword || keyword.trim() === '') {
        return allTemplates;
    }

    const lowerKeyword = keyword.toLowerCase();

    return allTemplates.filter(function(template) {
        return template.name.toLowerCase().includes(lowerKeyword) ||
               template.description.toLowerCase().includes(lowerKeyword) ||
               template.categoryLabel.toLowerCase().includes(lowerKeyword);
    });
};

/**
 * @function MappingTemplatesComponent.prototype.getTemplateCategories
 * @description 获取模板类别列表
 * @returns {Array} 类别列表
 */
MappingTemplatesComponent.prototype.getTemplateCategories = function() {
    const allTemplates = this.getAllTemplates();
    const categoryMap = new Map();

    allTemplates.forEach(function(template) {
        if (template.category && template.categoryLabel) {
            if (!categoryMap.has(template.category)) {
                categoryMap.set(template.category, {
                    id: template.category,
                    label: template.categoryLabel,
                    count: 0
                });
            }
            categoryMap.get(template.category).count++;
        }
    });

    return Array.from(categoryMap.values());
};

/**
 * @function MappingTemplatesComponent.prototype.exportTemplate
 * @description 导出模板为JSON
 * @param {string} templateId - 模板ID
 * @returns {string} JSON字符串
 */
MappingTemplatesComponent.prototype.exportTemplate = function(templateId) {
    const template = this.getTemplateById(templateId);
    if (!template) {
        throw new Error('模板不存在');
    }

    // 创建导出数据
    const exportData = {
        name: template.name,
        description: template.description,
        category: template.category,
        categoryLabel: template.categoryLabel,
        mappings: template.mappings,
        exportedAt: new Date().toISOString(),
        version: '2.0.0'
    };

    return JSON.stringify(exportData, null, 2);
};

/**
 * @function MappingTemplatesComponent.prototype.importTemplate
 * @description 从JSON导入模板
 * @param {string} jsonData - JSON数据
 * @returns {Object} 导入的模板对象
 */
MappingTemplatesComponent.prototype.importTemplate = function(jsonData) {
    try {
        const templateData = JSON.parse(jsonData);

        // 验证导入数据
        if (!templateData.name || !templateData.mappings) {
            throw new Error('导入数据格式不正确');
        }

        // 生成新的ID避免冲突
        templateData.id = this.generateTemplateId();
        templateData.name = templateData.name + ' (导入)';

        // 保存模板
        return this.saveTemplate(templateData);

    } catch (error) {
        SmartOffice.log('error', '导入模板失败:', error);
        throw new Error('导入模板失败: ' + error.message);
    }
};

/**
 * @function MappingTemplatesComponent.prototype.render
 * @description 渲染模板管理界面
 */
MappingTemplatesComponent.prototype.render = function() {
    const html = `
        <div class="mapping-templates-container">
            <!-- 模板管理头部 -->
            <div class="templates-header">
                <div class="header-content">
                    <h3 class="header-title">
                        <svg class="header-icon" viewBox="0 0 24 24">
                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                        映射模板管理
                    </h3>
                    <p class="header-description">保存和重用字段映射配置，提高工作效率</p>
                </div>
                <div class="header-actions">
                    <button type="button" class="btn-secondary btn-import-template" id="importTemplateBtn">
                        <svg class="btn-icon" viewBox="0 0 24 24">
                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                        导入模板
                    </button>
                    <button type="button" class="btn-primary btn-create-template" id="createTemplateBtn">
                        <svg class="btn-icon" viewBox="0 0 24 24">
                            <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                        </svg>
                        新建模板
                    </button>
                </div>
            </div>

            <!-- 模板筛选和搜索 -->
            <div class="templates-filter">
                <div class="filter-left">
                    <div class="category-filter">
                        <select class="filter-select" id="categoryFilter">
                            <option value="">所有类别</option>
                            <option value="sales">销售</option>
                            <option value="finance">财务</option>
                            <option value="inventory">库存</option>
                            <option value="hr">人事</option>
                            <option value="custom">自定义</option>
                        </select>
                    </div>
                    <div class="template-type-filter">
                        <label class="filter-checkbox">
                            <input type="checkbox" id="showBuiltinTemplates" checked>
                            <span class="checkbox-label">显示内置模板</span>
                        </label>
                        <label class="filter-checkbox">
                            <input type="checkbox" id="showUserTemplates" checked>
                            <span class="checkbox-label">显示用户模板</span>
                        </label>
                    </div>
                </div>
                <div class="filter-right">
                    <div class="search-container">
                        <input type="text" class="search-input" id="templateSearch" placeholder="搜索模板..." />
                        <svg class="search-icon" viewBox="0 0 24 24">
                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- 模板列表 -->
            <div class="templates-list" id="templatesList">
                <!-- 模板卡片将在这里动态生成 -->
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <div class="empty-icon">
                    <svg viewBox="0 0 24 24">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                </div>
                <h4 class="empty-title">还没有映射模板</h4>
                <p class="empty-description">创建您的第一个字段映射模板，或导入现有模板</p>
                <div class="empty-actions">
                    <button type="button" class="btn-primary btn-create-first" id="createFirstTemplateBtn">
                        创建模板
                    </button>
                    <button type="button" class="btn-secondary btn-import-first" id="importFirstTemplateBtn">
                        导入模板
                    </button>
                </div>
            </div>
        </div>

        <!-- 模板保存对话框 -->
        <div class="template-save-dialog" id="templateSaveDialog" style="display: none;">
            <div class="dialog-overlay"></div>
            <div class="dialog-content">
                <div class="dialog-header">
                    <h4 class="dialog-title">保存映射模板</h4>
                    <button type="button" class="dialog-close" id="dialogCloseBtn">
                        <svg viewBox="0 0 24 24">
                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                        </svg>
                    </button>
                </div>
                <div class="dialog-body">
                    <div class="form-group">
                        <label for="templateNameInput" class="form-label">模板名称 *</label>
                        <input type="text" id="templateNameInput" class="form-input" placeholder="请输入模板名称" maxlength="100" required />
                    </div>
                    <div class="form-group">
                        <label for="templateDescInput" class="form-label">模板描述</label>
                        <textarea id="templateDescInput" class="form-textarea" placeholder="请输入模板描述（可选）" maxlength="500" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="templateCategorySelect" class="form-label">模板类别</label>
                        <select id="templateCategorySelect" class="form-select">
                            <option value="custom">自定义</option>
                            <option value="sales">销售</option>
                            <option value="finance">财务</option>
                            <option value="inventory">库存</option>
                            <option value="hr">人事</option>
                        </select>
                    </div>
                </div>
                <div class="dialog-footer">
                    <button type="button" class="btn-secondary" id="dialogCancelBtn">取消</button>
                    <button type="button" class="btn-primary" id="dialogSaveBtn">保存模板</button>
                </div>
            </div>
        </div>

        <!-- 文件导入输入 -->
        <input type="file" id="templateFileInput" accept=".json" style="display: none;" />
    `;

    this.container.innerHTML = html;

    // 渲染模板列表
    this.renderTemplatesList();
};

/**
 * @function MappingTemplatesComponent.prototype.renderTemplatesList
 * @description 渲染模板列表
 */
MappingTemplatesComponent.prototype.renderTemplatesList = function() {
    const templatesList = document.getElementById('templatesList');
    const emptyState = document.getElementById('emptyState');

    if (!templatesList) return;

    const templates = this.getAllTemplates();

    if (templates.length === 0) {
        templatesList.style.display = 'none';
        emptyState.style.display = 'block';
        return;
    }

    emptyState.style.display = 'none';
    templatesList.style.display = 'grid';

    let html = '';
    for (let i = 0; i < templates.length; i++) {
        html += this.renderTemplateCard(templates[i]);
    }

    templatesList.innerHTML = html;

    // 绑定模板卡片事件
    this.bindTemplateCardEvents();
};

/**
 * @function MappingTemplatesComponent.prototype.renderTemplateCard
 * @description 渲染单个模板卡片
 * @param {Object} template - 模板对象
 * @returns {string} 卡片HTML
 */
MappingTemplatesComponent.prototype.renderTemplateCard = function(template) {
    const mappingsCount = template.mappings ? template.mappings.length : 0;
    const usageCount = template.usageCount || 0;
    const isBuiltin = template.isBuiltin ? 'builtin-template' : 'user-template';
    const categoryClass = 'category-' + (template.category || 'custom');

    const lastUsed = template.lastUsed
        ? this.formatDate(template.lastUsed)
        : '从未使用';

    return `
        <div class="template-card ${isBuiltin} ${categoryClass}" data-template-id="${template.id}">
            <div class="card-header">
                <div class="card-title-section">
                    <h4 class="card-title">${SmartOffice.Utils.Helpers.escapeHtml(template.name)}</h4>
                    <div class="card-badges">
                        <span class="category-badge">${template.categoryLabel || '自定义'}</span>
                        ${template.isBuiltin ? '<span class="builtin-badge">内置</span>' : '<span class="user-badge">自定义</span>'}
                    </div>
                </div>
                <div class="card-actions">
                    <button type="button" class="card-action-btn preview-template" title="预览模板" data-template-id="${template.id}">
                        <svg viewBox="0 0 24 24">
                            <path d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"/>
                        </svg>
                    </button>
                    <button type="button" class="card-action-btn export-template" title="导出模板" data-template-id="${template.id}">
                        <svg viewBox="0 0 24 24">
                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                    </button>
                    ${!template.isBuiltin ? `
                    <button type="button" class="card-action-btn edit-template" title="编辑模板" data-template-id="${template.id}">
                        <svg viewBox="0 0 24 24">
                            <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                        </svg>
                    </button>
                    <button type="button" class="card-action-btn delete-template" title="删除模板" data-template-id="${template.id}">
                        <svg viewBox="0 0 24 24">
                            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                        </svg>
                    </button>
                    ` : ''}
                </div>
            </div>

            <div class="card-content">
                ${template.description ? `<p class="card-description">${SmartOffice.Utils.Helpers.escapeHtml(template.description)}</p>` : ''}

                <div class="card-stats">
                    <div class="stat-item">
                        <span class="stat-label">映射数量</span>
                        <span class="stat-value">${mappingsCount} 个</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">使用次数</span>
                        <span class="stat-value">${usageCount} 次</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">最后使用</span>
                        <span class="stat-value">${lastUsed}</span>
                    </div>
                </div>

                ${mappingsCount > 0 ? this.renderMappingPreview(template.mappings.slice(0, 3)) : ''}
            </div>

            <div class="card-footer">
                <button type="button" class="btn-secondary btn-preview-mappings" data-template-id="${template.id}">
                    查看映射
                </button>
                <button type="button" class="btn-primary btn-apply-template" data-template-id="${template.id}">
                    应用模板
                </button>
            </div>
        </div>
    `;
};

/**
 * @function MappingTemplatesComponent.prototype.renderMappingPreview
 * @description 渲染映射预览
 * @param {Array} mappings - 映射列表（前几个）
 * @returns {string} 预览HTML
 */
MappingTemplatesComponent.prototype.renderMappingPreview = function(mappings) {
    let html = '<div class="mapping-preview">';
    html += '<h5 class="preview-title">映射预览</h5>';
    html += '<div class="preview-list">';

    for (let i = 0; i < mappings.length; i++) {
        const mapping = mappings[i];
        const targetLabel = this.fieldMappingManager.getStandardFieldLabel(mapping.targetField);

        html += `
            <div class="preview-item">
                <span class="source-field">${SmartOffice.Utils.Helpers.escapeHtml(mapping.sourceField)}</span>
                <svg class="mapping-arrow" viewBox="0 0 24 24">
                    <path d="M4,11V13H16L10.5,18.5L11.92,19.92L19.84,12L11.92,4.08L10.5,5.5L16,11H4Z"/>
                </svg>
                <span class="target-field">${SmartOffice.Utils.Helpers.escapeHtml(targetLabel)}</span>
            </div>
        `;
    }

    html += '</div>';
    html += '</div>';

    return html;
};

/**
 * @function MappingTemplatesComponent.prototype.bindEvents
 * @description 绑定事件监听器
 */
MappingTemplatesComponent.prototype.bindEvents = function() {
    const self = this;

    // 创建模板按钮
    const createTemplateBtn = document.getElementById('createTemplateBtn');
    const createFirstTemplateBtn = document.getElementById('createFirstTemplateBtn');

    if (createTemplateBtn) {
        createTemplateBtn.addEventListener('click', function() {
            self.showCreateTemplateDialog();
        });
    }

    if (createFirstTemplateBtn) {
        createFirstTemplateBtn.addEventListener('click', function() {
            self.showCreateTemplateDialog();
        });
    }

    // 导入模板按钮
    const importTemplateBtn = document.getElementById('importTemplateBtn');
    const importFirstTemplateBtn = document.getElementById('importFirstTemplateBtn');
    const templateFileInput = document.getElementById('templateFileInput');

    if (importTemplateBtn) {
        importTemplateBtn.addEventListener('click', function() {
            templateFileInput.click();
        });
    }

    if (importFirstTemplateBtn) {
        importFirstTemplateBtn.addEventListener('click', function() {
            templateFileInput.click();
        });
    }

    if (templateFileInput) {
        templateFileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                self.handleTemplateImport(this.files[0]);
            }
        });
    }

    // 筛选和搜索
    const categoryFilter = document.getElementById('categoryFilter');
    const showBuiltinTemplates = document.getElementById('showBuiltinTemplates');
    const showUserTemplates = document.getElementById('showUserTemplates');
    const templateSearch = document.getElementById('templateSearch');

    if (categoryFilter) {
        categoryFilter.addEventListener('change', function() {
            self.filterTemplates();
        });
    }

    if (showBuiltinTemplates) {
        showBuiltinTemplates.addEventListener('change', function() {
            self.filterTemplates();
        });
    }

    if (showUserTemplates) {
        showUserTemplates.addEventListener('change', function() {
            self.filterTemplates();
        });
    }

    if (templateSearch) {
        templateSearch.addEventListener('input', function() {
            self.debounceSearch();
        });
    }

    // 模板保存对话框事件
    this.bindDialogEvents();
};

/**
 * @function MappingTemplatesComponent.prototype.bindTemplateCardEvents
 * @description 绑定模板卡片事件
 */
MappingTemplatesComponent.prototype.bindTemplateCardEvents = function() {
    const self = this;

    // 应用模板按钮
    const applyBtns = document.querySelectorAll('.btn-apply-template');
    applyBtns.forEach(function(btn) {
        btn.addEventListener('click', function() {
            const templateId = this.getAttribute('data-template-id');
            self.handleApplyTemplate(templateId);
        });
    });

    // 预览映射按钮
    const previewBtns = document.querySelectorAll('.btn-preview-mappings');
    previewBtns.forEach(function(btn) {
        btn.addEventListener('click', function() {
            const templateId = this.getAttribute('data-template-id');
            self.showTemplateMappings(templateId);
        });
    });

    // 卡片操作按钮
    const actionBtns = document.querySelectorAll('.card-action-btn');
    actionBtns.forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            const templateId = this.getAttribute('data-template-id');
            const action = this.classList.contains('preview-template') ? 'preview' :
                          this.classList.contains('export-template') ? 'export' :
                          this.classList.contains('edit-template') ? 'edit' :
                          this.classList.contains('delete-template') ? 'delete' : '';

            self.handleTemplateAction(action, templateId);
        });
    });
};

/**
 * @function MappingTemplatesComponent.prototype.bindDialogEvents
 * @description 绑定对话框事件
 */
MappingTemplatesComponent.prototype.bindDialogEvents = function() {
    const self = this;
    const dialog = document.getElementById('templateSaveDialog');
    const closeBtn = document.getElementById('dialogCloseBtn');
    const cancelBtn = document.getElementById('dialogCancelBtn');
    const saveBtn = document.getElementById('dialogSaveBtn');

    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            self.hideCreateTemplateDialog();
        });
    }

    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            self.hideCreateTemplateDialog();
        });
    }

    if (saveBtn) {
        saveBtn.addEventListener('click', function() {
            self.handleSaveTemplate();
        });
    }

    // 点击遮罩关闭对话框
    if (dialog) {
        dialog.addEventListener('click', function(e) {
            if (e.target.classList.contains('dialog-overlay')) {
                self.hideCreateTemplateDialog();
            }
        });
    }
};

/**
 * @function MappingTemplatesComponent.prototype.showCreateTemplateDialog
 * @description 显示创建模板对话框
 */
MappingTemplatesComponent.prototype.showCreateTemplateDialog = function() {
    const dialog = document.getElementById('templateSaveDialog');
    if (dialog) {
        dialog.style.display = 'flex';

        // 清空表单
        const nameInput = document.getElementById('templateNameInput');
        const descInput = document.getElementById('templateDescInput');
        const categorySelect = document.getElementById('templateCategorySelect');

        if (nameInput) nameInput.value = '';
        if (descInput) descInput.value = '';
        if (categorySelect) categorySelect.value = 'custom';

        // 聚焦到名称输入框
        if (nameInput) {
            setTimeout(function() {
                nameInput.focus();
            }, 100);
        }
    }
};

/**
 * @function MappingTemplatesComponent.prototype.hideCreateTemplateDialog
 * @description 隐藏创建模板对话框
 */
MappingTemplatesComponent.prototype.hideCreateTemplateDialog = function() {
    const dialog = document.getElementById('templateSaveDialog');
    if (dialog) {
        dialog.style.display = 'none';
    }
};

/**
 * @function MappingTemplatesComponent.prototype.handleSaveTemplate
 * @description 处理保存模板
 */
MappingTemplatesComponent.prototype.handleSaveTemplate = function() {
    try {
        const nameInput = document.getElementById('templateNameInput');
        const descInput = document.getElementById('templateDescInput');
        const categorySelect = document.getElementById('templateCategorySelect');

        const name = nameInput ? nameInput.value.trim() : '';
        const description = descInput ? descInput.value.trim() : '';
        const category = categorySelect ? categorySelect.value : 'custom';

        if (!name) {
            SmartOffice.Components.Toast.show({
                message: '请输入模板名称',
                type: 'warning',
                duration: 3000
            });
            if (nameInput) nameInput.focus();
            return;
        }

        // 获取当前映射关系
        const currentMappings = this.fieldMappingManager.getCurrentMappings();
        if (currentMappings.length === 0) {
            SmartOffice.Components.Toast.show({
                message: '当前没有映射关系，无法保存模板',
                type: 'warning',
                duration: 3000
            });
            return;
        }

        // 创建模板数据
        const templateData = {
            name: name,
            description: description,
            category: category,
            categoryLabel: this.getCategoryLabel(category),
            mappings: currentMappings.map(function(mapping) {
                return {
                    sourceField: mapping.sourceField,
                    targetField: mapping.targetField,
                    dataType: mapping.dataType,
                    confidence: mapping.confidence || 1.0
                };
            })
        };

        // 保存模板
        const savedTemplate = this.saveTemplate(templateData);

        // 隐藏对话框
        this.hideCreateTemplateDialog();

        // 刷新模板列表
        this.renderTemplatesList();

        // 显示成功提示
        SmartOffice.Components.Toast.show({
            message: '模板保存成功: ' + savedTemplate.name,
            type: 'success',
            duration: 3000
        });

    } catch (error) {
        SmartOffice.log('error', '保存模板失败:', error);
        SmartOffice.Components.Toast.show({
            message: '保存模板失败: ' + error.message,
            type: 'error',
            duration: 5000
        });
    }
};

/**
 * @function MappingTemplatesComponent.prototype.getCategoryLabel
 * @description 获取类别标签
 * @param {string} category - 类别ID
 * @returns {string} 类别标签
 */
MappingTemplatesComponent.prototype.getCategoryLabel = function(category) {
    const categoryLabels = {
        'sales': '销售',
        'finance': '财务',
        'inventory': '库存',
        'hr': '人事',
        'custom': '自定义'
    };

    return categoryLabels[category] || '自定义';
};

/**
 * @function MappingTemplatesComponent.prototype.formatDate
 * @description 格式化日期
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期
 */
MappingTemplatesComponent.prototype.formatDate = function(dateString) {
    try {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffDays === 0) {
            return '今天';
        } else if (diffDays === 1) {
            return '昨天';
        } else if (diffDays < 7) {
            return diffDays + ' 天前';
        } else if (diffDays < 30) {
            return Math.floor(diffDays / 7) + ' 周前';
        } else {
            return date.toLocaleDateString('zh-CN');
        }
    } catch (error) {
        return '未知';
    }
};

// 创建全局字段映射模板组件实例
SmartOffice.Components.MappingTemplates = MappingTemplatesComponent;

SmartOffice.log('info', 'SmartOffice字段映射模板管理组件模块初始化完成 - 重构版本2.0.0');
