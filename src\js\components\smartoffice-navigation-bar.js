/**
 * @file SmartOffice导航栏组件
 * @description 重构后的固定定位导航栏，集成全局时间段管理功能
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function NavigationBarComponent
 * @description 导航栏组件构造函数
 * @constructor
 * @param {Object} options - 组件配置选项
 */
function NavigationBarComponent(options) {
    /**
     * @property {Object} options - 组件配置
     */
    this.options = Object.assign({
        containerId: 'navigationBar',
        enableTimeRangeManagement: true,
        enableFieldMappingAccess: true,
        showBreadcrumb: false,
        fixedPosition: true
    }, options || {});

    /**
     * @property {HTMLElement} container - 导航栏容器
     */
    this.container = null;

    /**
     * @property {Object} router - 路由管理器引用
     */
    this.router = SmartOffice.Core.Router;

    /**
     * @property {Object} eventBus - 事件总线引用
     */
    this.eventBus = SmartOffice.Core.EventBus;

    /**
     * @property {Object} timeRangeManager - 时间段管理器引用
     */
    this.timeRangeManager = SmartOffice.Data.TimeRangeManager;

    /**
     * @property {Object} storage - 存储管理器引用
     */
    this.storage = SmartOffice.Core.Storage;

    /**
     * @property {Array} activeTimeRanges - 当前激活的时间段
     */
    this.activeTimeRanges = [];

    /**
     * @property {Object} currentRoute - 当前路由信息
     */
    this.currentRoute = null;

    /**
     * @property {boolean} timeRangeSelectorVisible - 时间段选择器是否可见
     */
    this.timeRangeSelectorVisible = false;

    /**
     * @property {Object} elements - DOM元素引用
     */
    this.elements = {};

    SmartOffice.log('info', 'NavigationBarComponent导航栏组件初始化完成');
}

/**
 * @function NavigationBarComponent.prototype.init
 * @description 初始化导航栏组件
 */
NavigationBarComponent.prototype.init = function() {
    try {
        this.container = document.querySelector('.nav-bar') || document.getElementById(this.options.containerId);
        if (!this.container) {
            throw new Error('未找到导航栏容器元素');
        }

        // 重构导航栏结构
        this.rebuildNavigationBar();

        // 获取DOM元素引用
        this.cacheElements();

        // 绑定事件
        this.bindEvents();

        // 加载时间段配置
        this.loadTimeRangeConfiguration();

        // 监听路由变化
        this.listenToRouteChanges();

        SmartOffice.log('info', '导航栏组件初始化成功');
    } catch (error) {
        SmartOffice.log('error', '导航栏组件初始化失败:', error);
    }
};

/**
 * @function NavigationBarComponent.prototype.rebuildNavigationBar
 * @description 重构导航栏HTML结构
 */
NavigationBarComponent.prototype.rebuildNavigationBar = function() {
    const html = `
        <div class="nav-content-enhanced">
            <!-- 左侧区域 -->
            <div class="nav-left">
                <button type="button" class="nav-button nav-back nav-back-hidden" id="navBack" title="返回" aria-label="返回上一页">
                    <svg class="nav-icon" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                    </svg>
                </button>
            </div>

            <!-- 中央区域 -->
            <div class="nav-center">
                <h1 class="nav-title" id="navTitle">透视分析</h1>
                <div class="nav-breadcrumb" id="navBreadcrumb" style="display: none;">
                    <!-- 面包屑导航将在这里动态生成 -->
                </div>
            </div>

            <!-- 右侧区域 -->
            <div class="nav-right">
                <!-- 时间段状态指示器 -->
                <div class="time-range-indicator" id="timeRangeIndicator" style="display: none;">
                    <div class="indicator-dot"></div>
                    <span class="indicator-text">时间段</span>
                </div>

                <!-- 时间段管理按钮 -->
                <button type="button" class="nav-button nav-time-range" id="navTimeRange" title="时间段管理" aria-label="管理全局时间段">
                    <svg class="nav-icon" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.9L16.2,16.2Z"/>
                    </svg>
                </button>

                <!-- 字段映射管理按钮 -->
                <button type="button" class="nav-button nav-field-mapping" id="navFieldMapping" title="字段映射" aria-label="管理字段映射">
                    <svg class="nav-icon" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                </button>

                <!-- 主操作按钮 -->
                <button type="button" class="nav-button nav-add" id="navAdd" title="新建配置" aria-label="创建新的透视表配置">
                    <svg class="nav-icon" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                    </svg>
                </button>
            </div>
        </div>

        <!-- 时间段选择器下拉面板 -->
        <div class="time-range-selector" id="timeRangeSelector" style="display: none;">
            <div class="selector-header">
                <h3 class="selector-title">全局时间段管理</h3>
                <button type="button" class="selector-close" id="selectorClose">
                    <svg viewBox="0 0 24 24">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>

            <div class="selector-content">
                <!-- 当前激活的时间段 -->
                <div class="active-time-ranges" id="activeTimeRanges">
                    <div class="section-label">当前激活时间段</div>
                    <div class="active-ranges-list" id="activeRangesList">
                        <div class="no-active-ranges">未选择时间段</div>
                    </div>
                </div>

                <!-- 可用时间段列表 -->
                <div class="available-time-ranges" id="availableTimeRanges">
                    <div class="section-label">
                        <span>可用时间段</span>
                        <button type="button" class="btn-create-time-range" id="createTimeRangeBtn">
                            <svg viewBox="0 0 24 24">
                                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                            </svg>
                            新建
                        </button>
                    </div>
                    <div class="available-ranges-list" id="availableRangesList">
                        <!-- 时间段列表将在这里动态生成 -->
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="quick-actions">
                    <button type="button" class="btn-secondary btn-clear-all" id="clearAllTimeRanges">
                        清除所有
                    </button>
                    <button type="button" class="btn-primary btn-apply-time-ranges" id="applyTimeRanges">
                        应用选择
                    </button>
                </div>
            </div>
        </div>
    `;

    this.container.innerHTML = html;
};

/**
 * @function NavigationBarComponent.prototype.cacheElements
 * @description 缓存DOM元素引用
 */
NavigationBarComponent.prototype.cacheElements = function() {
    this.elements = {
        // 导航按钮
        navBack: this.container.querySelector('#navBack'),
        navTitle: this.container.querySelector('#navTitle'),
        navBreadcrumb: this.container.querySelector('#navBreadcrumb'),
        navTimeRange: this.container.querySelector('#navTimeRange'),
        navFieldMapping: this.container.querySelector('#navFieldMapping'),
        navAdd: this.container.querySelector('#navAdd'),

        // 时间段指示器
        timeRangeIndicator: this.container.querySelector('#timeRangeIndicator'),

        // 时间段选择器
        timeRangeSelector: this.container.querySelector('#timeRangeSelector'),
        selectorClose: this.container.querySelector('#selectorClose'),
        activeRangesList: this.container.querySelector('#activeRangesList'),
        availableRangesList: this.container.querySelector('#availableRangesList'),
        createTimeRangeBtn: this.container.querySelector('#createTimeRangeBtn'),
        clearAllTimeRanges: this.container.querySelector('#clearAllTimeRanges'),
        applyTimeRanges: this.container.querySelector('#applyTimeRanges')
    };
};

/**
 * @function NavigationBarComponent.prototype.bindEvents
 * @description 绑定事件监听器
 */
NavigationBarComponent.prototype.bindEvents = function() {
    const self = this;

    // 返回按钮
    if (this.elements.navBack) {
        this.elements.navBack.addEventListener('click', function() {
            self.handleBackNavigation();
        });
    }

    // 时间段管理按钮
    if (this.elements.navTimeRange) {
        this.elements.navTimeRange.addEventListener('click', function() {
            self.toggleTimeRangeSelector();
        });
    }

    // 字段映射管理按钮
    if (this.elements.navFieldMapping) {
        this.elements.navFieldMapping.addEventListener('click', function() {
            self.navigateToFieldMapping();
        });
    }

    // 主操作按钮
    if (this.elements.navAdd) {
        this.elements.navAdd.addEventListener('click', function() {
            self.handleMainAction();
        });
    }

    // 时间段选择器关闭
    if (this.elements.selectorClose) {
        this.elements.selectorClose.addEventListener('click', function() {
            self.hideTimeRangeSelector();
        });
    }

    // 创建时间段按钮
    if (this.elements.createTimeRangeBtn) {
        this.elements.createTimeRangeBtn.addEventListener('click', function() {
            self.createNewTimeRange();
        });
    }

    // 清除所有时间段
    if (this.elements.clearAllTimeRanges) {
        this.elements.clearAllTimeRanges.addEventListener('click', function() {
            self.clearAllActiveTimeRanges();
        });
    }

    // 应用时间段选择
    if (this.elements.applyTimeRanges) {
        this.elements.applyTimeRanges.addEventListener('click', function() {
            self.applyTimeRangeSelection();
        });
    }

    // 点击外部关闭时间段选择器
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.nav-time-range') && !e.target.closest('.time-range-selector')) {
            self.hideTimeRangeSelector();
        }
    });

    // 监听时间段相关事件
    this.eventBus.on('timeRange:created', function(timeRange) {
        self.onTimeRangeCreated(timeRange);
    });

    this.eventBus.on('timeRange:deleted', function(timeRange) {
        self.onTimeRangeDeleted(timeRange);
    });

    this.eventBus.on('timeRange:activeChanged', function(activeRanges) {
        self.onActiveTimeRangesChanged(activeRanges);
    });
};

/**
 * @function NavigationBarComponent.prototype.loadTimeRangeConfiguration
 * @description 加载时间段配置
 */
NavigationBarComponent.prototype.loadTimeRangeConfiguration = function() {
    try {
        // 加载所有时间段配置
        const allTimeRanges = this.timeRangeManager.getAllGlobalTimeRanges();

        // 加载激活的时间段
        const activeRangeIds = this.storage.get('activeTimeRanges') || [];
        this.activeTimeRanges = allTimeRanges.filter(range =>
            activeRangeIds.includes(range.id)
        );

        // 更新UI
        this.updateTimeRangeIndicator();
        this.renderAvailableTimeRanges();
        this.renderActiveTimeRanges();

        SmartOffice.log('info', '时间段配置加载完成，激活' + this.activeTimeRanges.length + '个时间段');
    } catch (error) {
        SmartOffice.log('error', '加载时间段配置失败:', error);
    }
};

/**
 * @function NavigationBarComponent.prototype.listenToRouteChanges
 * @description 监听路由变化
 */
NavigationBarComponent.prototype.listenToRouteChanges = function() {
    const self = this;

    this.eventBus.on('router:routeChanged', function(routeInfo) {
        self.onRouteChanged(routeInfo);
    });

    // 初始化当前路由状态
    this.onRouteChanged({
        route: this.router.currentRoute,
        params: this.router.currentParams
    });
};

/**
 * @function NavigationBarComponent.prototype.onRouteChanged
 * @description 处理路由变化
 * @param {Object} routeInfo - 路由信息
 */
NavigationBarComponent.prototype.onRouteChanged = function(routeInfo) {
    this.currentRoute = routeInfo;

    // 更新导航栏状态
    this.updateNavigationState();

    // 更新标题
    this.updateTitle();

    // 更新按钮可见性
    this.updateButtonVisibility();
};

/**
 * @function NavigationBarComponent.prototype.updateNavigationState
 * @description 更新导航状态
 */
NavigationBarComponent.prototype.updateNavigationState = function() {
    const route = this.currentRoute.route;

    // 更新返回按钮状态
    if (this.elements.navBack) {
        if (route === 'home' || route === 'fileUpload') {
            this.elements.navBack.classList.add('nav-back-hidden');
        } else {
            this.elements.navBack.classList.remove('nav-back-hidden');
        }
    }
};

/**
 * @function NavigationBarComponent.prototype.updateTitle
 * @description 更新导航栏标题
 */
NavigationBarComponent.prototype.updateTitle = function() {
    if (!this.elements.navTitle) return;

    const route = this.currentRoute.route;
    const titles = {
        'home': 'GoMyHire 透视分析',
        'fileUpload': 'GoMyHire 透视分析',
        'configList': '配置管理',
        'configForm': '配置透视表',
        'fieldMapping': '字段映射管理'
    };

    this.elements.navTitle.textContent = titles[route] || 'SmartOffice';
};

/**
 * @function NavigationBarComponent.prototype.updateButtonVisibility
 * @description 更新按钮可见性
 */
NavigationBarComponent.prototype.updateButtonVisibility = function() {
    const route = this.currentRoute.route;

    // 字段映射按钮在字段映射页面隐藏
    if (this.elements.navFieldMapping) {
        this.elements.navFieldMapping.style.display = route === 'fieldMapping' ? 'none' : 'flex';
    }

    // 主操作按钮根据页面调整
    if (this.elements.navAdd) {
        const addButton = this.elements.navAdd;
        switch (route) {
            case 'configList':
                addButton.title = '新建配置';
                addButton.setAttribute('aria-label', '创建新的透视表配置');
                break;
            case 'fieldMapping':
                addButton.title = '新建映射模板';
                addButton.setAttribute('aria-label', '创建新的字段映射模板');
                break;
            default:
                addButton.title = '新建配置';
                addButton.setAttribute('aria-label', '创建新的透视表配置');
        }
    }
};

/**
 * @function NavigationBarComponent.prototype.updateTimeRangeIndicator
 * @description 更新时间段状态指示器
 */
NavigationBarComponent.prototype.updateTimeRangeIndicator = function() {
    if (!this.elements.timeRangeIndicator) return;

    const hasActiveRanges = this.activeTimeRanges.length > 0;

    if (hasActiveRanges) {
        this.elements.timeRangeIndicator.style.display = 'flex';
        const indicatorText = this.elements.timeRangeIndicator.querySelector('.indicator-text');
        if (indicatorText) {
            indicatorText.textContent = this.activeTimeRanges.length === 1
                ? this.activeTimeRanges[0].name
                : `${this.activeTimeRanges.length}个时间段`;
        }
    } else {
        this.elements.timeRangeIndicator.style.display = 'none';
    }
};

/**
 * @function NavigationBarComponent.prototype.renderAvailableTimeRanges
 * @description 渲染可用时间段列表
 */
NavigationBarComponent.prototype.renderAvailableTimeRanges = function() {
    if (!this.elements.availableRangesList) return;

    const allTimeRanges = this.timeRangeManager.getAllGlobalTimeRanges();

    if (allTimeRanges.length === 0) {
        this.elements.availableRangesList.innerHTML = `
            <div class="empty-time-ranges">
                <p>暂无时间段配置</p>
                <p class="empty-hint">点击"新建"创建时间段</p>
            </div>
        `;
        return;
    }

    let html = '';
    allTimeRanges.forEach(timeRange => {
        const isActive = this.activeTimeRanges.some(active => active.id === timeRange.id);
        const typeClass = timeRange.type === 'preset' ? 'preset' : 'custom';

        html += `
            <div class="time-range-item ${isActive ? 'active' : ''}" data-range-id="${timeRange.id}">
                <div class="range-info">
                    <div class="range-header">
                        <span class="range-name">${SmartOffice.Utils.Helpers.escapeHtml(timeRange.name)}</span>
                        <span class="range-type-badge ${typeClass}">${timeRange.type === 'preset' ? '预设' : '自定义'}</span>
                    </div>
                    <div class="range-time">
                        <span class="time-start">${timeRange.start}</span>
                        <span class="time-separator">-</span>
                        <span class="time-end">${timeRange.end}</span>
                    </div>
                    ${timeRange.description ? `<div class="range-description">${SmartOffice.Utils.Helpers.escapeHtml(timeRange.description)}</div>` : ''}
                </div>
                <div class="range-actions">
                    <button type="button" class="range-action-btn toggle-range" data-action="toggle" title="${isActive ? '取消激活' : '激活'}">
                        <svg viewBox="0 0 24 24">
                            ${isActive
                                ? '<path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>'
                                : '<circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>'
                            }
                        </svg>
                    </button>
                    ${timeRange.type === 'custom' ? `
                        <button type="button" class="range-action-btn edit-range" data-action="edit" title="编辑">
                            <svg viewBox="0 0 24 24">
                                <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                            </svg>
                        </button>
                        <button type="button" class="range-action-btn delete-range" data-action="delete" title="删除">
                            <svg viewBox="0 0 24 24">
                                <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                            </svg>
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    });

    this.elements.availableRangesList.innerHTML = html;

    // 绑定时间段项事件
    this.bindTimeRangeItemEvents();
};

/**
 * @function NavigationBarComponent.prototype.renderActiveTimeRanges
 * @description 渲染激活的时间段列表
 */
NavigationBarComponent.prototype.renderActiveTimeRanges = function() {
    if (!this.elements.activeRangesList) return;

    if (this.activeTimeRanges.length === 0) {
        this.elements.activeRangesList.innerHTML = '<div class="no-active-ranges">未选择时间段</div>';
        return;
    }

    let html = '';
    this.activeTimeRanges.forEach(timeRange => {
        html += `
            <div class="active-range-item" data-range-id="${timeRange.id}">
                <div class="active-range-info">
                    <span class="active-range-name">${SmartOffice.Utils.Helpers.escapeHtml(timeRange.name)}</span>
                    <span class="active-range-time">${timeRange.start} - ${timeRange.end}</span>
                </div>
                <button type="button" class="active-range-remove" data-action="remove" title="移除">
                    <svg viewBox="0 0 24 24">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
        `;
    });

    this.elements.activeRangesList.innerHTML = html;

    // 绑定移除按钮事件
    this.bindActiveRangeEvents();
};

/**
 * @function NavigationBarComponent.prototype.bindTimeRangeItemEvents
 * @description 绑定时间段项事件
 */
NavigationBarComponent.prototype.bindTimeRangeItemEvents = function() {
    const self = this;
    const rangeItems = this.elements.availableRangesList.querySelectorAll('.time-range-item');

    rangeItems.forEach(item => {
        const rangeId = item.getAttribute('data-range-id');

        // 切换激活状态
        const toggleBtn = item.querySelector('[data-action="toggle"]');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', function() {
                self.toggleTimeRangeActive(rangeId);
            });
        }

        // 编辑时间段
        const editBtn = item.querySelector('[data-action="edit"]');
        if (editBtn) {
            editBtn.addEventListener('click', function() {
                self.editTimeRange(rangeId);
            });
        }

        // 删除时间段
        const deleteBtn = item.querySelector('[data-action="delete"]');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', function() {
                self.deleteTimeRange(rangeId);
            });
        }
    });
};

/**
 * @function NavigationBarComponent.prototype.bindActiveRangeEvents
 * @description 绑定激活时间段事件
 */
NavigationBarComponent.prototype.bindActiveRangeEvents = function() {
    const self = this;
    const activeItems = this.elements.activeRangesList.querySelectorAll('.active-range-item');

    activeItems.forEach(item => {
        const rangeId = item.getAttribute('data-range-id');
        const removeBtn = item.querySelector('[data-action="remove"]');

        if (removeBtn) {
            removeBtn.addEventListener('click', function() {
                self.removeActiveTimeRange(rangeId);
            });
        }
    });
};

/**
 * @function NavigationBarComponent.prototype.toggleTimeRangeSelector
 * @description 切换时间段选择器显示状态
 */
NavigationBarComponent.prototype.toggleTimeRangeSelector = function() {
    if (this.timeRangeSelectorVisible) {
        this.hideTimeRangeSelector();
    } else {
        this.showTimeRangeSelector();
    }
};

/**
 * @function NavigationBarComponent.prototype.showTimeRangeSelector
 * @description 显示时间段选择器
 */
NavigationBarComponent.prototype.showTimeRangeSelector = function() {
    if (!this.elements.timeRangeSelector) return;

    this.timeRangeSelectorVisible = true;
    this.elements.timeRangeSelector.style.display = 'block';

    // 添加显示动画
    setTimeout(() => {
        this.elements.timeRangeSelector.classList.add('selector-visible');
    }, 10);

    // 刷新数据
    this.loadTimeRangeConfiguration();
};

/**
 * @function NavigationBarComponent.prototype.hideTimeRangeSelector
 * @description 隐藏时间段选择器
 */
NavigationBarComponent.prototype.hideTimeRangeSelector = function() {
    if (!this.elements.timeRangeSelector) return;

    this.timeRangeSelectorVisible = false;
    this.elements.timeRangeSelector.classList.remove('selector-visible');

    setTimeout(() => {
        this.elements.timeRangeSelector.style.display = 'none';
    }, 300);
};

/**
 * @function NavigationBarComponent.prototype.toggleTimeRangeActive
 * @description 切换时间段激活状态
 * @param {string} rangeId - 时间段ID
 */
NavigationBarComponent.prototype.toggleTimeRangeActive = function(rangeId) {
    const isActive = this.activeTimeRanges.some(range => range.id === rangeId);

    if (isActive) {
        this.removeActiveTimeRange(rangeId);
    } else {
        this.addActiveTimeRange(rangeId);
    }
};

/**
 * @function NavigationBarComponent.prototype.addActiveTimeRange
 * @description 添加激活时间段
 * @param {string} rangeId - 时间段ID
 */
NavigationBarComponent.prototype.addActiveTimeRange = function(rangeId) {
    const timeRange = this.timeRangeManager.getGlobalTimeRange(rangeId);
    if (timeRange && !this.activeTimeRanges.some(range => range.id === rangeId)) {
        this.activeTimeRanges.push(timeRange);
        this.updateTimeRangeIndicator();
        this.renderAvailableTimeRanges();
        this.renderActiveTimeRanges();

        // 触发事件
        this.eventBus.emit('timeRange:activeChanged', this.activeTimeRanges);
    }
};

/**
 * @function NavigationBarComponent.prototype.removeActiveTimeRange
 * @description 移除激活时间段
 * @param {string} rangeId - 时间段ID
 */
NavigationBarComponent.prototype.removeActiveTimeRange = function(rangeId) {
    this.activeTimeRanges = this.activeTimeRanges.filter(range => range.id !== rangeId);
    this.updateTimeRangeIndicator();
    this.renderAvailableTimeRanges();
    this.renderActiveTimeRanges();

    // 触发事件
    this.eventBus.emit('timeRange:activeChanged', this.activeTimeRanges);
};

/**
 * @function NavigationBarComponent.prototype.clearAllActiveTimeRanges
 * @description 清除所有激活时间段
 */
NavigationBarComponent.prototype.clearAllActiveTimeRanges = function() {
    if (this.activeTimeRanges.length === 0) return;

    const confirmed = confirm('确定要清除所有激活的时间段吗？');
    if (confirmed) {
        this.activeTimeRanges = [];
        this.updateTimeRangeIndicator();
        this.renderAvailableTimeRanges();
        this.renderActiveTimeRanges();

        // 触发事件
        this.eventBus.emit('timeRange:activeChanged', this.activeTimeRanges);
    }
};

/**
 * @function NavigationBarComponent.prototype.applyTimeRangeSelection
 * @description 应用时间段选择
 */
NavigationBarComponent.prototype.applyTimeRangeSelection = function() {
    // 保存激活的时间段到存储
    const activeRangeIds = this.activeTimeRanges.map(range => range.id);
    this.storage.set('activeTimeRanges', activeRangeIds);

    // 隐藏选择器
    this.hideTimeRangeSelector();

    // 显示成功提示
    this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '时间段配置已应用', 'success');

    // 触发全局时间段应用事件
    this.eventBus.emit('timeRange:applied', this.activeTimeRanges);
};

/**
 * @function NavigationBarComponent.prototype.createNewTimeRange
 * @description 创建新时间段
 */
NavigationBarComponent.prototype.createNewTimeRange = function() {
    // 隐藏选择器
    this.hideTimeRangeSelector();

    // 触发创建时间段事件
    this.eventBus.emit('timeRange:createNew');
};

/**
 * @function NavigationBarComponent.prototype.editTimeRange
 * @description 编辑时间段
 * @param {string} rangeId - 时间段ID
 */
NavigationBarComponent.prototype.editTimeRange = function(rangeId) {
    const timeRange = this.timeRangeManager.getGlobalTimeRange(rangeId);
    if (timeRange) {
        // 隐藏选择器
        this.hideTimeRangeSelector();

        // 触发编辑时间段事件
        this.eventBus.emit('timeRange:edit', timeRange);
    }
};

/**
 * @function NavigationBarComponent.prototype.deleteTimeRange
 * @description 删除时间段
 * @param {string} rangeId - 时间段ID
 */
NavigationBarComponent.prototype.deleteTimeRange = function(rangeId) {
    const timeRange = this.timeRangeManager.getGlobalTimeRange(rangeId);
    if (!timeRange) return;

    const confirmed = confirm(`确定要删除时间段"${timeRange.name}"吗？\n\n此操作不可撤销。`);
    if (confirmed) {
        const success = this.timeRangeManager.deleteGlobalTimeRange(rangeId);
        if (success) {
            // 从激活列表中移除
            this.removeActiveTimeRange(rangeId);

            // 重新渲染
            this.renderAvailableTimeRanges();

            // 显示成功提示
            this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '时间段删除成功', 'success');
        } else {
            this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '删除时间段失败', 'error');
        }
    }
};

/**
 * @function NavigationBarComponent.prototype.handleBackNavigation
 * @description 处理返回导航
 */
NavigationBarComponent.prototype.handleBackNavigation = function() {
    if (this.router.canGoBack()) {
        this.router.goBack();
    } else {
        // 默认返回主页
        this.router.navigate('home');
    }
};

/**
 * @function NavigationBarComponent.prototype.navigateToFieldMapping
 * @description 导航到字段映射管理页面
 */
NavigationBarComponent.prototype.navigateToFieldMapping = function() {
    this.router.navigate('fieldMapping');
};

/**
 * @function NavigationBarComponent.prototype.handleMainAction
 * @description 处理主操作按钮点击
 */
NavigationBarComponent.prototype.handleMainAction = function() {
    const route = this.currentRoute.route;

    switch (route) {
        case 'configList':
            // 创建新配置
            this.router.navigate('configForm');
            break;
        case 'fieldMapping':
            // 创建新映射模板
            this.eventBus.emit('fieldMapping:createNew');
            break;
        default:
            // 默认创建新配置
            this.router.navigate('configForm');
    }
};

// 事件处理方法
/**
 * @function NavigationBarComponent.prototype.onTimeRangeCreated
 * @description 处理时间段创建事件
 * @param {Object} timeRange - 新创建的时间段
 */
NavigationBarComponent.prototype.onTimeRangeCreated = function(timeRange) {
    this.renderAvailableTimeRanges();
    this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '时间段创建成功', 'success');
};

/**
 * @function NavigationBarComponent.prototype.onTimeRangeDeleted
 * @description 处理时间段删除事件
 * @param {Object} timeRange - 被删除的时间段
 */
NavigationBarComponent.prototype.onTimeRangeDeleted = function(timeRange) {
    this.removeActiveTimeRange(timeRange.id);
    this.renderAvailableTimeRanges();
};

/**
 * @function NavigationBarComponent.prototype.onActiveTimeRangesChanged
 * @description 处理激活时间段变化事件
 * @param {Array} activeRanges - 激活的时间段列表
 */
NavigationBarComponent.prototype.onActiveTimeRangesChanged = function(activeRanges) {
    this.activeTimeRanges = activeRanges || [];
    this.updateTimeRangeIndicator();
};

/**
 * @function NavigationBarComponent.prototype.getActiveTimeRanges
 * @description 获取当前激活的时间段
 * @returns {Array} 激活的时间段列表
 */
NavigationBarComponent.prototype.getActiveTimeRanges = function() {
    return this.activeTimeRanges.slice(); // 返回副本
};

/**
 * @function NavigationBarComponent.prototype.setActiveTimeRanges
 * @description 设置激活的时间段
 * @param {Array} timeRanges - 时间段列表
 */
NavigationBarComponent.prototype.setActiveTimeRanges = function(timeRanges) {
    this.activeTimeRanges = timeRanges || [];
    this.updateTimeRangeIndicator();
    this.renderAvailableTimeRanges();
    this.renderActiveTimeRanges();

    // 保存到存储
    const activeRangeIds = this.activeTimeRanges.map(range => range.id);
    this.storage.set('activeTimeRanges', activeRangeIds);

    // 触发事件
    this.eventBus.emit('timeRange:activeChanged', this.activeTimeRanges);
};

/**
 * @function NavigationBarComponent.prototype.refreshTimeRanges
 * @description 刷新时间段数据
 */
NavigationBarComponent.prototype.refreshTimeRanges = function() {
    this.loadTimeRangeConfiguration();
};

/**
 * @function NavigationBarComponent.prototype.destroy
 * @description 销毁组件
 */
NavigationBarComponent.prototype.destroy = function() {
    // 移除事件监听器
    this.eventBus.off('router:routeChanged');
    this.eventBus.off('timeRange:created');
    this.eventBus.off('timeRange:deleted');
    this.eventBus.off('timeRange:activeChanged');

    // 清理DOM
    if (this.container) {
        this.container.innerHTML = '';
    }

    // 清理引用
    this.container = null;
    this.elements = {};
    this.activeTimeRanges = [];
    this.currentRoute = null;

    SmartOffice.log('info', '导航栏组件已销毁');
};

// 注册组件
SmartOffice.Components.NavigationBar = NavigationBarComponent;

SmartOffice.log('info', 'SmartOffice导航栏组件模块初始化完成');
