/**
 * @file SmartOffice时间段配置对话框组件
 * @description 提供时间段创建和编辑功能的对话框组件
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function TimeRangeDialogComponent
 * @description 时间段配置对话框组件构造函数
 * @constructor
 * @param {Object} options - 组件配置选项
 */
function TimeRangeDialogComponent(options) {
    /**
     * @property {Object} options - 组件配置
     */
    this.options = Object.assign({
        containerId: 'timeRangeDialog',
        onSave: null,
        onCancel: null
    }, options || {});

    /**
     * @property {HTMLElement} dialog - 对话框元素
     */
    this.dialog = null;

    /**
     * @property {HTMLElement} overlay - 遮罩层元素
     */
    this.overlay = null;

    /**
     * @property {Object} timeRangeManager - 时间段管理器引用
     */
    this.timeRangeManager = SmartOffice.Data.TimeRangeManager;

    /**
     * @property {Object} eventBus - 事件总线引用
     */
    this.eventBus = SmartOffice.Core.EventBus;

    /**
     * @property {Object} currentTimeRange - 当前编辑的时间段
     */
    this.currentTimeRange = null;

    /**
     * @property {boolean} isVisible - 对话框是否可见
     */
    this.isVisible = false;

    SmartOffice.log('info', 'TimeRangeDialogComponent时间段配置对话框组件初始化完成');
}

/**
 * @function TimeRangeDialogComponent.prototype.init
 * @description 初始化组件
 */
TimeRangeDialogComponent.prototype.init = function() {
    try {
        // 创建对话框结构
        this.createDialog();

        // 绑定事件
        this.bindEvents();

        SmartOffice.log('info', '时间段配置对话框组件初始化成功');
    } catch (error) {
        SmartOffice.log('error', '时间段配置对话框组件初始化失败:', error);
    }
};

/**
 * @function TimeRangeDialogComponent.prototype.createDialog
 * @description 创建对话框结构
 */
TimeRangeDialogComponent.prototype.createDialog = function() {
    // 创建遮罩层
    this.overlay = document.createElement('div');
    this.overlay.className = 'timerange-dialog-overlay';
    this.overlay.style.display = 'none';

    // 创建对话框
    this.dialog = document.createElement('div');
    this.dialog.className = 'timerange-dialog';
    this.dialog.innerHTML = this.getDialogHTML();

    this.overlay.appendChild(this.dialog);
    document.body.appendChild(this.overlay);
};

/**
 * @function TimeRangeDialogComponent.prototype.getDialogHTML
 * @description 获取对话框HTML结构
 * @returns {string} HTML字符串
 */
TimeRangeDialogComponent.prototype.getDialogHTML = function() {
    return `
        <div class="dialog-header">
            <h2 class="dialog-title" id="dialogTitle">新建时间段</h2>
            <button type="button" class="dialog-close" id="dialogClose">
                <svg viewBox="0 0 24 24">
                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
            </button>
        </div>

        <div class="dialog-content">
            <form class="timerange-form" id="timeRangeForm">
                <!-- 基本信息 -->
                <div class="form-section">
                    <div class="form-group">
                        <label for="timeRangeName" class="form-label">时间段名称 *</label>
                        <input type="text" id="timeRangeName" class="form-input" placeholder="请输入时间段名称" maxlength="50" required />
                    </div>
                    
                    <div class="form-group">
                        <label for="timeRangeDescription" class="form-label">描述</label>
                        <textarea id="timeRangeDescription" class="form-textarea" placeholder="请输入时间段描述（可选）" maxlength="200" rows="3"></textarea>
                    </div>
                </div>

                <!-- 时间配置 -->
                <div class="form-section">
                    <h3 class="section-title">时间配置</h3>
                    
                    <div class="time-inputs">
                        <div class="form-group">
                            <label for="startTime" class="form-label">开始时间 *</label>
                            <input type="time" id="startTime" class="form-input time-input" required />
                        </div>
                        
                        <div class="time-separator">
                            <svg viewBox="0 0 24 24">
                                <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                            </svg>
                        </div>
                        
                        <div class="form-group">
                            <label for="endTime" class="form-label">结束时间 *</label>
                            <input type="time" id="endTime" class="form-input time-input" required />
                        </div>
                    </div>

                    <div class="time-validation" id="timeValidation" style="display: none;">
                        <div class="validation-message"></div>
                    </div>
                </div>

                <!-- 快速选择预设 -->
                <div class="form-section">
                    <h3 class="section-title">快速选择</h3>
                    <div class="preset-buttons">
                        <button type="button" class="preset-btn" data-start="09:00" data-end="12:00">上午</button>
                        <button type="button" class="preset-btn" data-start="13:00" data-end="18:00">下午</button>
                        <button type="button" class="preset-btn" data-start="19:00" data-end="23:00">晚上</button>
                        <button type="button" class="preset-btn" data-start="09:00" data-end="18:00">工作时间</button>
                        <button type="button" class="preset-btn" data-start="00:00" data-end="23:59">全天</button>
                    </div>
                </div>

                <!-- 高级选项 -->
                <div class="form-section">
                    <h3 class="section-title">高级选项</h3>
                    
                    <div class="form-group">
                        <label class="form-label checkbox-label">
                            <input type="checkbox" id="isGlobal" class="form-checkbox" checked />
                            设为全局时间段
                        </label>
                        <div class="help-text">全局时间段可在所有页面中使用</div>
                    </div>

                    <div class="form-group">
                        <label for="timeRangeColor" class="form-label">标识颜色</label>
                        <div class="color-picker">
                            <input type="color" id="timeRangeColor" class="color-input" value="#007aff" />
                            <div class="color-presets">
                                <button type="button" class="color-preset" data-color="#007aff" style="background: #007aff;"></button>
                                <button type="button" class="color-preset" data-color="#34c759" style="background: #34c759;"></button>
                                <button type="button" class="color-preset" data-color="#ff9500" style="background: #ff9500;"></button>
                                <button type="button" class="color-preset" data-color="#ff3b30" style="background: #ff3b30;"></button>
                                <button type="button" class="color-preset" data-color="#af52de" style="background: #af52de;"></button>
                                <button type="button" class="color-preset" data-color="#5ac8fa" style="background: #5ac8fa;"></button>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="priority" class="form-label">优先级</label>
                        <select id="priority" class="form-select">
                            <option value="10">低</option>
                            <option value="50" selected>中</option>
                            <option value="90">高</option>
                        </select>
                        <div class="help-text">优先级高的时间段在列表中排序靠前</div>
                    </div>
                </div>

                <!-- 预览 -->
                <div class="form-section">
                    <h3 class="section-title">预览</h3>
                    <div class="timerange-preview" id="timeRangePreview">
                        <div class="preview-item">
                            <div class="preview-color" id="previewColor"></div>
                            <div class="preview-info">
                                <div class="preview-name" id="previewName">时间段名称</div>
                                <div class="preview-time" id="previewTime">00:00 - 00:00</div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="dialog-footer">
            <button type="button" class="btn-secondary" id="cancelBtn">取消</button>
            <button type="button" class="btn-primary" id="saveBtn">保存时间段</button>
        </div>
    `;
};

/**
 * @function TimeRangeDialogComponent.prototype.bindEvents
 * @description 绑定事件监听器
 */
TimeRangeDialogComponent.prototype.bindEvents = function() {
    const self = this;

    // 对话框关闭事件
    const closeBtn = this.dialog.querySelector('#dialogClose');
    const cancelBtn = this.dialog.querySelector('#cancelBtn');
    
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            self.hide();
        });
    }
    
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            self.hide();
        });
    }

    // 遮罩层点击关闭
    this.overlay.addEventListener('click', function(e) {
        if (e.target === self.overlay) {
            self.hide();
        }
    });

    // ESC键关闭
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && self.isVisible) {
            self.hide();
        }
    });

    // 保存按钮
    const saveBtn = this.dialog.querySelector('#saveBtn');
    if (saveBtn) {
        saveBtn.addEventListener('click', function() {
            self.saveTimeRange();
        });
    }

    // 表单输入事件
    const nameInput = this.dialog.querySelector('#timeRangeName');
    const startTimeInput = this.dialog.querySelector('#startTime');
    const endTimeInput = this.dialog.querySelector('#endTime');
    const colorInput = this.dialog.querySelector('#timeRangeColor');

    if (nameInput) {
        nameInput.addEventListener('input', function() {
            self.updatePreview();
        });
    }

    if (startTimeInput) {
        startTimeInput.addEventListener('change', function() {
            self.validateTimeRange();
            self.updatePreview();
        });
    }

    if (endTimeInput) {
        endTimeInput.addEventListener('change', function() {
            self.validateTimeRange();
            self.updatePreview();
        });
    }

    if (colorInput) {
        colorInput.addEventListener('change', function() {
            self.updatePreview();
        });
    }

    // 预设按钮事件
    const presetButtons = this.dialog.querySelectorAll('.preset-btn');
    presetButtons.forEach(function(btn) {
        btn.addEventListener('click', function() {
            const startTime = this.getAttribute('data-start');
            const endTime = this.getAttribute('data-end');
            self.setTimeRange(startTime, endTime);
        });
    });

    // 颜色预设按钮事件
    const colorPresets = this.dialog.querySelectorAll('.color-preset');
    colorPresets.forEach(function(btn) {
        btn.addEventListener('click', function() {
            const color = this.getAttribute('data-color');
            self.setColor(color);
        });
    });

    // 监听导航栏的时间段事件
    this.eventBus.on('timeRange:createNew', function() {
        self.show();
    });

    this.eventBus.on('timeRange:edit', function(timeRange) {
        self.show(timeRange);
    });
};

/**
 * @function TimeRangeDialogComponent.prototype.show
 * @description 显示对话框
 * @param {Object} timeRange - 要编辑的时间段（可选）
 */
TimeRangeDialogComponent.prototype.show = function(timeRange) {
    this.currentTimeRange = timeRange || null;
    this.isVisible = true;

    // 重置表单
    this.resetForm();

    // 如果是编辑模式，填充数据
    if (timeRange) {
        this.populateForm(timeRange);
    }

    // 显示对话框
    this.overlay.style.display = 'flex';
    document.body.classList.add('dialog-open');

    // 添加显示动画
    setTimeout(() => {
        this.overlay.classList.add('dialog-visible');
    }, 10);

    // 聚焦到名称输入框
    const nameInput = this.dialog.querySelector('#timeRangeName');
    if (nameInput) {
        setTimeout(() => {
            nameInput.focus();
        }, 300);
    }

    // 更新预览
    this.updatePreview();
};

/**
 * @function TimeRangeDialogComponent.prototype.hide
 * @description 隐藏对话框
 */
TimeRangeDialogComponent.prototype.hide = function() {
    this.isVisible = false;

    // 添加隐藏动画
    this.overlay.classList.remove('dialog-visible');

    setTimeout(() => {
        this.overlay.style.display = 'none';
        document.body.classList.remove('dialog-open');

        // 触发取消回调
        if (this.options.onCancel && typeof this.options.onCancel === 'function') {
            this.options.onCancel();
        }
    }, 300);
};

/**
 * @function TimeRangeDialogComponent.prototype.resetForm
 * @description 重置表单
 */
TimeRangeDialogComponent.prototype.resetForm = function() {
    const form = this.dialog.querySelector('#timeRangeForm');
    if (form) {
        form.reset();
    }

    // 设置默认值
    const colorInput = this.dialog.querySelector('#timeRangeColor');
    const prioritySelect = this.dialog.querySelector('#priority');
    const isGlobalCheckbox = this.dialog.querySelector('#isGlobal');

    if (colorInput) colorInput.value = '#007aff';
    if (prioritySelect) prioritySelect.value = '50';
    if (isGlobalCheckbox) isGlobalCheckbox.checked = true;

    // 更新标题
    const dialogTitle = this.dialog.querySelector('#dialogTitle');
    if (dialogTitle) {
        dialogTitle.textContent = this.currentTimeRange ? '编辑时间段' : '新建时间段';
    }

    // 清除验证消息
    this.clearValidation();
};

/**
 * @function TimeRangeDialogComponent.prototype.populateForm
 * @description 填充表单数据（编辑模式）
 * @param {Object} timeRange - 时间段数据
 */
TimeRangeDialogComponent.prototype.populateForm = function(timeRange) {
    const nameInput = this.dialog.querySelector('#timeRangeName');
    const descInput = this.dialog.querySelector('#timeRangeDescription');
    const startTimeInput = this.dialog.querySelector('#startTime');
    const endTimeInput = this.dialog.querySelector('#endTime');
    const colorInput = this.dialog.querySelector('#timeRangeColor');
    const prioritySelect = this.dialog.querySelector('#priority');
    const isGlobalCheckbox = this.dialog.querySelector('#isGlobal');

    if (nameInput) nameInput.value = timeRange.name || '';
    if (descInput) descInput.value = timeRange.description || '';
    if (startTimeInput) startTimeInput.value = timeRange.start || '';
    if (endTimeInput) endTimeInput.value = timeRange.end || '';
    if (colorInput) colorInput.value = timeRange.color || '#007aff';
    if (prioritySelect) prioritySelect.value = timeRange.priority || '50';
    if (isGlobalCheckbox) isGlobalCheckbox.checked = timeRange.isGlobal !== false;
};

/**
 * @function TimeRangeDialogComponent.prototype.setTimeRange
 * @description 设置时间范围
 * @param {string} startTime - 开始时间
 * @param {string} endTime - 结束时间
 */
TimeRangeDialogComponent.prototype.setTimeRange = function(startTime, endTime) {
    const startTimeInput = this.dialog.querySelector('#startTime');
    const endTimeInput = this.dialog.querySelector('#endTime');

    if (startTimeInput) startTimeInput.value = startTime;
    if (endTimeInput) endTimeInput.value = endTime;

    this.validateTimeRange();
    this.updatePreview();
};

/**
 * @function TimeRangeDialogComponent.prototype.setColor
 * @description 设置颜色
 * @param {string} color - 颜色值
 */
TimeRangeDialogComponent.prototype.setColor = function(color) {
    const colorInput = this.dialog.querySelector('#timeRangeColor');
    if (colorInput) {
        colorInput.value = color;
        this.updatePreview();
    }
};

/**
 * @function TimeRangeDialogComponent.prototype.validateTimeRange
 * @description 验证时间范围
 * @returns {boolean} 是否有效
 */
TimeRangeDialogComponent.prototype.validateTimeRange = function() {
    const startTimeInput = this.dialog.querySelector('#startTime');
    const endTimeInput = this.dialog.querySelector('#endTime');
    const validationDiv = this.dialog.querySelector('#timeValidation');
    const messageDiv = validationDiv.querySelector('.validation-message');

    const startTime = startTimeInput.value;
    const endTime = endTimeInput.value;

    if (!startTime || !endTime) {
        this.clearValidation();
        return false;
    }

    // 转换为分钟进行比较
    const startMinutes = this.timeToMinutes(startTime);
    const endMinutes = this.timeToMinutes(endTime);

    if (startMinutes >= endMinutes) {
        this.showValidation('结束时间必须晚于开始时间', 'error');
        return false;
    }

    // 检查时间段长度
    const duration = endMinutes - startMinutes;
    if (duration < 30) {
        this.showValidation('时间段长度不能少于30分钟', 'warning');
        return true; // 警告但不阻止保存
    }

    this.clearValidation();
    return true;
};

/**
 * @function TimeRangeDialogComponent.prototype.timeToMinutes
 * @description 将时间字符串转换为分钟数
 * @param {string} timeStr - 时间字符串 (HH:MM)
 * @returns {number} 分钟数
 */
TimeRangeDialogComponent.prototype.timeToMinutes = function(timeStr) {
    const parts = timeStr.split(':');
    return parseInt(parts[0]) * 60 + parseInt(parts[1]);
};

/**
 * @function TimeRangeDialogComponent.prototype.showValidation
 * @description 显示验证消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 ('error' | 'warning')
 */
TimeRangeDialogComponent.prototype.showValidation = function(message, type) {
    const validationDiv = this.dialog.querySelector('#timeValidation');
    const messageDiv = validationDiv.querySelector('.validation-message');

    messageDiv.textContent = message;
    validationDiv.className = 'time-validation ' + type;
    validationDiv.style.display = 'block';
};

/**
 * @function TimeRangeDialogComponent.prototype.clearValidation
 * @description 清除验证消息
 */
TimeRangeDialogComponent.prototype.clearValidation = function() {
    const validationDiv = this.dialog.querySelector('#timeValidation');
    if (validationDiv) {
        validationDiv.style.display = 'none';
    }
};

/**
 * @function TimeRangeDialogComponent.prototype.updatePreview
 * @description 更新预览
 */
TimeRangeDialogComponent.prototype.updatePreview = function() {
    const nameInput = this.dialog.querySelector('#timeRangeName');
    const startTimeInput = this.dialog.querySelector('#startTime');
    const endTimeInput = this.dialog.querySelector('#endTime');
    const colorInput = this.dialog.querySelector('#timeRangeColor');

    const previewName = this.dialog.querySelector('#previewName');
    const previewTime = this.dialog.querySelector('#previewTime');
    const previewColor = this.dialog.querySelector('#previewColor');

    if (previewName) {
        previewName.textContent = nameInput.value || '时间段名称';
    }

    if (previewTime) {
        const startTime = startTimeInput.value || '00:00';
        const endTime = endTimeInput.value || '00:00';
        previewTime.textContent = `${startTime} - ${endTime}`;
    }

    if (previewColor) {
        previewColor.style.backgroundColor = colorInput.value || '#007aff';
    }
};

/**
 * @function TimeRangeDialogComponent.prototype.saveTimeRange
 * @description 保存时间段
 */
TimeRangeDialogComponent.prototype.saveTimeRange = function() {
    // 验证表单
    const validation = this.validateForm();
    if (!validation.isValid) {
        this.showValidation(validation.errors[0], 'error');
        return;
    }

    // 构建时间段数据
    const timeRangeData = this.buildTimeRangeData();

    // 保存时间段
    let success;
    if (this.currentTimeRange) {
        // 更新现有时间段
        success = this.timeRangeManager.updateGlobalTimeRange(timeRangeData);
    } else {
        // 创建新时间段
        success = this.timeRangeManager.createGlobalTimeRange(timeRangeData);
    }

    if (success) {
        // 触发保存回调
        if (this.options.onSave && typeof this.options.onSave === 'function') {
            this.options.onSave(timeRangeData);
        }

        // 触发事件
        this.eventBus.emit('timeRange:saved', timeRangeData);

        this.hide();
    } else {
        this.showValidation('保存时间段失败', 'error');
    }
};

/**
 * @function TimeRangeDialogComponent.prototype.validateForm
 * @description 验证表单
 * @returns {Object} 验证结果
 */
TimeRangeDialogComponent.prototype.validateForm = function() {
    const result = { isValid: true, errors: [] };

    const nameInput = this.dialog.querySelector('#timeRangeName');
    const startTimeInput = this.dialog.querySelector('#startTime');
    const endTimeInput = this.dialog.querySelector('#endTime');

    if (!nameInput.value.trim()) {
        result.isValid = false;
        result.errors.push('请输入时间段名称');
    }

    if (!startTimeInput.value) {
        result.isValid = false;
        result.errors.push('请选择开始时间');
    }

    if (!endTimeInput.value) {
        result.isValid = false;
        result.errors.push('请选择结束时间');
    }

    if (startTimeInput.value && endTimeInput.value) {
        if (!this.validateTimeRange()) {
            result.isValid = false;
            result.errors.push('时间范围设置无效');
        }
    }

    return result;
};

/**
 * @function TimeRangeDialogComponent.prototype.buildTimeRangeData
 * @description 构建时间段数据
 * @returns {Object} 时间段数据
 */
TimeRangeDialogComponent.prototype.buildTimeRangeData = function() {
    const nameInput = this.dialog.querySelector('#timeRangeName');
    const descInput = this.dialog.querySelector('#timeRangeDescription');
    const startTimeInput = this.dialog.querySelector('#startTime');
    const endTimeInput = this.dialog.querySelector('#endTime');
    const colorInput = this.dialog.querySelector('#timeRangeColor');
    const prioritySelect = this.dialog.querySelector('#priority');
    const isGlobalCheckbox = this.dialog.querySelector('#isGlobal');

    const timeRangeData = {
        id: this.currentTimeRange ? this.currentTimeRange.id : SmartOffice.Utils.Helpers.generateId('timerange'),
        name: nameInput.value.trim(),
        description: descInput.value.trim(),
        start: startTimeInput.value,
        end: endTimeInput.value,
        color: colorInput.value,
        priority: parseInt(prioritySelect.value),
        isGlobal: isGlobalCheckbox.checked,
        type: 'custom',
        updatedAt: new Date().toISOString()
    };

    if (!this.currentTimeRange) {
        timeRangeData.createdAt = new Date().toISOString();
        timeRangeData.usageCount = 0;
    } else {
        timeRangeData.createdAt = this.currentTimeRange.createdAt;
        timeRangeData.usageCount = this.currentTimeRange.usageCount || 0;
    }

    return timeRangeData;
};

// 注册组件
SmartOffice.Components.TimeRangeDialog = TimeRangeDialogComponent;

SmartOffice.log('info', 'SmartOffice时间段配置对话框组件模块初始化完成');
