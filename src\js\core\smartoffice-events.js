/**
 * @file SmartOffice事件总线模块
 * @description 应用级事件管理系统，提供发布订阅模式的事件通信
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function EventBus
 * @description 事件总线构造函数
 * @constructor
 */
function EventBus() {
    /**
     * @property {Object} events - 事件监听器存储
     */
    this.events = {};
    
    /**
     * @property {boolean} debug - 调试模式
     */
    this.debug = SmartOffice.Config.DEBUG;
    
    SmartOffice.log('info', 'EventBus实例创建完成');
}

/**
 * @function EventBus.prototype.on
 * @description 注册事件监听器
 * @param {string} eventName - 事件名称
 * @param {Function} callback - 回调函数
 * @param {Object} context - 执行上下文
 * @returns {Function} 用于取消监听的函数
 */
EventBus.prototype.on = function(eventName, callback, context) {
    if (typeof eventName !== 'string' || typeof callback !== 'function') {
        SmartOffice.log('error', '事件注册参数无效:', { eventName, callback });
        return function() {}; // 返回空函数避免错误
    }
    
    if (!this.events[eventName]) {
        this.events[eventName] = [];
    }
    
    const listener = {
        callback: callback,
        context: context || null,
        once: false
    };
    
    this.events[eventName].push(listener);
    
    if (this.debug) {
        SmartOffice.log('debug', '事件监听器已注册:', eventName);
    }
    
    // 返回取消监听的函数
    const self = this;
    return function() {
        self.off(eventName, callback);
    };
};

/**
 * @function EventBus.prototype.once
 * @description 注册一次性事件监听器
 * @param {string} eventName - 事件名称
 * @param {Function} callback - 回调函数
 * @param {Object} context - 执行上下文
 * @returns {Function} 用于取消监听的函数
 */
EventBus.prototype.once = function(eventName, callback, context) {
    if (typeof eventName !== 'string' || typeof callback !== 'function') {
        SmartOffice.log('error', '一次性事件注册参数无效:', { eventName, callback });
        return function() {};
    }
    
    if (!this.events[eventName]) {
        this.events[eventName] = [];
    }
    
    const listener = {
        callback: callback,
        context: context || null,
        once: true
    };
    
    this.events[eventName].push(listener);
    
    if (this.debug) {
        SmartOffice.log('debug', '一次性事件监听器已注册:', eventName);
    }
    
    // 返回取消监听的函数
    const self = this;
    return function() {
        self.off(eventName, callback);
    };
};

/**
 * @function EventBus.prototype.off
 * @description 移除事件监听器
 * @param {string} eventName - 事件名称
 * @param {Function} callback - 要移除的回调函数
 */
EventBus.prototype.off = function(eventName, callback) {
    if (!this.events[eventName]) {
        return;
    }
    
    if (callback) {
        // 移除特定的监听器
        this.events[eventName] = this.events[eventName].filter(function(listener) {
            return listener.callback !== callback;
        });
        
        if (this.debug) {
            SmartOffice.log('debug', '特定事件监听器已移除:', eventName);
        }
    } else {
        // 移除所有监听器
        delete this.events[eventName];
        
        if (this.debug) {
            SmartOffice.log('debug', '所有事件监听器已移除:', eventName);
        }
    }
};

/**
 * @function EventBus.prototype.emit
 * @description 触发事件
 * @param {string} eventName - 事件名称
 * @param {...*} args - 传递给监听器的参数
 */
EventBus.prototype.emit = function(eventName) {
    if (!this.events[eventName] || this.events[eventName].length === 0) {
        if (this.debug) {
            SmartOffice.log('debug', '没有找到事件监听器:', eventName);
        }
        return;
    }
    
    // 获取传递给监听器的参数
    const args = Array.prototype.slice.call(arguments, 1);
    
    // 复制监听器数组，避免在执行过程中修改原数组导致问题
    const listeners = this.events[eventName].slice();
    
    if (this.debug) {
        SmartOffice.log('debug', '触发事件:', eventName, '监听器数量:', listeners.length);
    }
    
    for (let i = 0; i < listeners.length; i++) {
        const listener = listeners[i];
        
        try {
            // 调用监听器
            if (listener.context) {
                listener.callback.apply(listener.context, args);
            } else {
                listener.callback.apply(null, args);
            }
            
            // 如果是一次性监听器，移除它
            if (listener.once) {
                const index = this.events[eventName].indexOf(listener);
                if (index !== -1) {
                    this.events[eventName].splice(index, 1);
                }
            }
            
        } catch (error) {
            SmartOffice.log('error', '事件监听器执行错误:', {
                eventName: eventName,
                error: error.message,
                stack: error.stack
            });
        }
    }
};

/**
 * @function EventBus.prototype.clear
 * @description 清空所有事件监听器
 */
EventBus.prototype.clear = function() {
    this.events = {};
    SmartOffice.log('info', '所有事件监听器已清空');
};

/**
 * @function EventBus.prototype.getListeners
 * @description 获取指定事件的监听器列表
 * @param {string} eventName - 事件名称
 * @returns {Array} 监听器列表
 */
EventBus.prototype.getListeners = function(eventName) {
    return this.events[eventName] ? this.events[eventName].slice() : [];
};

/**
 * @function EventBus.prototype.hasListeners
 * @description 检查是否有指定事件的监听器
 * @param {string} eventName - 事件名称
 * @returns {boolean} 是否有监听器
 */
EventBus.prototype.hasListeners = function(eventName) {
    return !!(this.events[eventName] && this.events[eventName].length > 0);
};

/**
 * @function EventBus.prototype.getEventNames
 * @description 获取所有已注册的事件名称
 * @returns {Array} 事件名称列表
 */
EventBus.prototype.getEventNames = function() {
    return Object.keys(this.events);
};

/**
 * @function EventBus.prototype.getStats
 * @description 获取事件总线统计信息
 * @returns {Object} 统计信息
 */
EventBus.prototype.getStats = function() {
    const stats = {
        totalEvents: 0,
        totalListeners: 0,
        events: {}
    };
    
    for (const eventName in this.events) {
        const listenerCount = this.events[eventName].length;
        stats.events[eventName] = listenerCount;
        stats.totalListeners += listenerCount;
        stats.totalEvents++;
    }
    
    return stats;
};

// 创建全局事件总线实例
SmartOffice.Core.EventBus = new EventBus();

SmartOffice.log('info', 'SmartOffice事件总线模块初始化完成');
