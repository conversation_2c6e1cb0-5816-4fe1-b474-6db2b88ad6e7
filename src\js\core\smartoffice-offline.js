/**
 * SmartOffice离线功能管理器
 * 管理Service Worker、离线状态检测、数据同步等功能
 */

(function() {
    'use strict';

    /**
     * 离线功能管理器构造函数
     */
    function OfflineManager() {
        this.isOnline = navigator.onLine;
        this.isServiceWorkerSupported = 'serviceWorker' in navigator;
        this.isRegistered = false;
        this.registration = null;
        this.updateAvailable = false;
        
        // 事件绑定
        this.eventBus = SmartOffice.Core.EventBus;
        
        // 初始化
        this.init();
    }

    /**
     * 初始化离线功能
     */
    OfflineManager.prototype.init = function() {
        console.log('🔧 离线管理器: 初始化开始');
        
        // 检查Service Worker支持
        if (!this.isServiceWorkerSupported) {
            console.warn('⚠️ 浏览器不支持Service Worker，离线功能不可用');
            return;
        }
        
        // 注册Service Worker
        this.registerServiceWorker();
        
        // 监听网络状态变化
        this.bindNetworkEvents();
        
        // 监听应用更新
        this.bindUpdateEvents();
        
        console.log('✅ 离线管理器: 初始化完成');
    };

    /**
     * 注册Service Worker
     */
    OfflineManager.prototype.registerServiceWorker = function() {
        const self = this;
        
        navigator.serviceWorker.register('/src/js/workers/smartoffice-service-worker.js')
            .then(function(registration) {
                console.log('✅ Service Worker注册成功:', registration.scope);
                self.registration = registration;
                self.isRegistered = true;
                
                // 检查更新
                self.checkForUpdates(registration);
                
                // 触发注册成功事件
                self.eventBus.emit('offline:registered', {
                    registration: registration,
                    scope: registration.scope
                });
            })
            .catch(function(error) {
                console.error('❌ Service Worker注册失败:', error);
                self.eventBus.emit('offline:registration-failed', { error: error });
            });
    };

    /**
     * 检查应用更新
     */
    OfflineManager.prototype.checkForUpdates = function(registration) {
        const self = this;
        
        // 检查是否有等待中的Service Worker
        if (registration.waiting) {
            self.updateAvailable = true;
            self.showUpdatePrompt();
            return;
        }
        
        // 监听Service Worker状态变化
        registration.addEventListener('updatefound', function() {
            const newWorker = registration.installing;
            
            newWorker.addEventListener('statechange', function() {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                    self.updateAvailable = true;
                    self.showUpdatePrompt();
                }
            });
        });
    };

    /**
     * 显示更新提示
     */
    OfflineManager.prototype.showUpdatePrompt = function() {
        const self = this;
        
        // 创建更新提示
        const updateBanner = document.createElement('div');
        updateBanner.className = 'update-banner';
        updateBanner.innerHTML = `
            <div class="update-content">
                <span class="update-message">发现新版本，点击更新</span>
                <button class="update-button" id="updateButton">更新</button>
                <button class="update-dismiss" id="dismissUpdate">稍后</button>
            </div>
        `;
        
        // 添加样式
        updateBanner.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #007aff;
            color: white;
            padding: 12px;
            z-index: 10000;
            text-align: center;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        `;
        
        // 添加到页面
        document.body.insertBefore(updateBanner, document.body.firstChild);
        
        // 绑定事件
        document.getElementById('updateButton').addEventListener('click', function() {
            self.applyUpdate();
            updateBanner.remove();
        });
        
        document.getElementById('dismissUpdate').addEventListener('click', function() {
            updateBanner.remove();
        });
        
        // 触发更新可用事件
        this.eventBus.emit('offline:update-available');
    };

    /**
     * 应用更新
     */
    OfflineManager.prototype.applyUpdate = function() {
        const self = this;
        
        if (!this.registration || !this.registration.waiting) {
            console.warn('⚠️ 没有等待中的Service Worker');
            return;
        }
        
        // 发送跳过等待消息
        this.registration.waiting.postMessage({ type: 'SKIP_WAITING' });
        
        // 监听控制器变化
        navigator.serviceWorker.addEventListener('controllerchange', function() {
            console.log('🔄 Service Worker已更新，重新加载页面');
            window.location.reload();
        });
    };

    /**
     * 绑定网络状态事件
     */
    OfflineManager.prototype.bindNetworkEvents = function() {
        const self = this;
        
        window.addEventListener('online', function() {
            console.log('🌐 网络已连接');
            self.isOnline = true;
            self.showNetworkStatus('已连接到网络', 'success');
            self.eventBus.emit('offline:online');
            
            // 触发后台同步
            self.requestBackgroundSync();
        });
        
        window.addEventListener('offline', function() {
            console.log('🔌 网络已断开');
            self.isOnline = false;
            self.showNetworkStatus('网络已断开，应用将离线运行', 'warning');
            self.eventBus.emit('offline:offline');
        });
    };

    /**
     * 绑定应用更新事件
     */
    OfflineManager.prototype.bindUpdateEvents = function() {
        const self = this;
        
        // 监听应用可见性变化
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden && self.registration) {
                // 应用重新激活时检查更新
                self.registration.update();
            }
        });
    };

    /**
     * 显示网络状态提示
     */
    OfflineManager.prototype.showNetworkStatus = function(message, type) {
        // 创建状态提示
        const statusToast = document.createElement('div');
        statusToast.className = `network-status network-status-${type}`;
        statusToast.textContent = message;
        
        // 添加样式
        statusToast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            background: ${type === 'success' ? '#34c759' : '#ff9500'};
            color: white;
            border-radius: 8px;
            font-size: 14px;
            z-index: 9999;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        // 添加到页面
        document.body.appendChild(statusToast);
        
        // 显示动画
        setTimeout(function() {
            statusToast.style.transform = 'translateX(0)';
        }, 100);
        
        // 自动隐藏
        setTimeout(function() {
            statusToast.style.transform = 'translateX(100%)';
            setTimeout(function() {
                if (statusToast.parentNode) {
                    statusToast.parentNode.removeChild(statusToast);
                }
            }, 300);
        }, 3000);
    };

    /**
     * 请求后台同步
     */
    OfflineManager.prototype.requestBackgroundSync = function() {
        if (!this.registration || !this.registration.sync) {
            console.warn('⚠️ 浏览器不支持后台同步');
            return;
        }
        
        this.registration.sync.register('smartoffice-data-sync')
            .then(function() {
                console.log('🔄 后台同步已注册');
            })
            .catch(function(error) {
                console.error('❌ 后台同步注册失败:', error);
            });
    };

    /**
     * 获取缓存使用情况
     */
    OfflineManager.prototype.getCacheUsage = function() {
        const self = this;
        
        if (!('caches' in window)) {
            return Promise.resolve(null);
        }
        
        return caches.keys()
            .then(function(cacheNames) {
                const promises = cacheNames.map(function(cacheName) {
                    return caches.open(cacheName)
                        .then(function(cache) {
                            return cache.keys()
                                .then(function(keys) {
                                    return {
                                        name: cacheName,
                                        count: keys.length
                                    };
                                });
                        });
                });
                
                return Promise.all(promises);
            })
            .then(function(cacheInfo) {
                const total = cacheInfo.reduce(function(sum, cache) {
                    return sum + cache.count;
                }, 0);
                
                return {
                    caches: cacheInfo,
                    totalItems: total
                };
            });
    };

    /**
     * 清理缓存
     */
    OfflineManager.prototype.clearCache = function() {
        const self = this;
        
        return new Promise(function(resolve, reject) {
            if (!self.registration) {
                reject(new Error('Service Worker未注册'));
                return;
            }
            
            // 创建消息通道
            const messageChannel = new MessageChannel();
            
            messageChannel.port1.onmessage = function(event) {
                if (event.data.type === 'CACHE_CLEARED') {
                    console.log('✅ 缓存已清理');
                    resolve();
                }
            };
            
            // 发送清理缓存消息
            self.registration.active.postMessage({
                type: 'CLEAR_CACHE'
            }, [messageChannel.port2]);
        });
    };

    /**
     * 获取离线状态信息
     */
    OfflineManager.prototype.getStatus = function() {
        return {
            isOnline: this.isOnline,
            isServiceWorkerSupported: this.isServiceWorkerSupported,
            isRegistered: this.isRegistered,
            updateAvailable: this.updateAvailable,
            registration: this.registration
        };
    };

    // 注册到全局命名空间
    SmartOffice.Core.OfflineManager = OfflineManager;

    console.log('📱 SmartOffice离线管理器已加载');

})();
