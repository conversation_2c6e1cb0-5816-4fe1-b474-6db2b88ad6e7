/**
 * @file SmartOffice路由管理模块
 * @description 简单的前端路由管理，支持单页应用的页面切换
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function Router
 * @description 路由管理器构造函数
 * @constructor
 */
function Router() {
    /**
     * @property {Object} routes - 路由配置
     */
    this.routes = {};
    
    /**
     * @property {string} currentRoute - 当前路由
     */
    this.currentRoute = '';
    
    /**
     * @property {Array} history - 路由历史
     */
    this.history = [];
    
    /**
     * @property {Object} eventBus - 事件总线引用
     */
    this.eventBus = SmartOffice.Core.EventBus;
    
    SmartOffice.log('info', 'Router实例创建完成');
}

/**
 * @function Router.prototype.init
 * @description 初始化路由系统
 */
Router.prototype.init = function() {
    // 注册默认路由
    this.registerDefaultRoutes();
    
    // 监听浏览器前进后退
    const self = this;
    window.addEventListener('popstate', function(event) {
        if (event.state && event.state.route) {
            self.navigateToRoute(event.state.route, false);
        }
    });
    
    // 导航到初始路由（文件上传主页）
    this.navigate('home');
    
    SmartOffice.log('info', '路由系统初始化完成');
};

/**
 * @function Router.prototype.registerDefaultRoutes
 * @description 注册默认路由
 */
Router.prototype.registerDefaultRoutes = function() {
    // 文件上传主页
    this.register('home', {
        title: 'GoMyHire 透视分析',
        pageId: 'fileUploadPage',
        showBackButton: false,
        showAddButton: false,
        onEnter: function() {
            SmartOffice.log('info', '进入文件上传主页');
            // 通知文件上传组件初始化
            SmartOffice.Core.EventBus.emit('fileUpload:init');
            // 更新配置预览列表
            SmartOffice.Core.EventBus.emit('configPreview:refresh');
        },
        onExit: function() {
            SmartOffice.log('info', '离开文件上传主页');
        }
    });

    // 配置管理页面
    this.register('configList', {
        title: '配置管理',
        pageId: 'configListPage',
        showBackButton: true,
        showAddButton: true,
        onEnter: function() {
            SmartOffice.log('info', '进入配置管理页面');
        },
        onExit: function() {
            SmartOffice.log('info', '离开配置管理页面');
        }
    });
    
    // 配置表单页面（新建）
    this.register('configForm', {
        title: '新建配置',
        pageId: 'configFormPage',
        showBackButton: true,
        showAddButton: false,
        onEnter: function() {
            SmartOffice.log('info', '进入配置表单页面（新建）');
            // 通知配置表单组件创建新配置
            SmartOffice.Core.EventBus.emit('configForm:createNew');
        },
        onExit: function() {
            SmartOffice.log('info', '离开配置表单页面');
            // 通知配置表单组件清理状态
            SmartOffice.Core.EventBus.emit('configForm:cleanup');
        }
    });
    
    // 配置表单页面（编辑）
    this.register('configEdit', {
        title: '编辑配置',
        pageId: 'configFormPage',
        showBackButton: true,
        showAddButton: false,
        onEnter: function(params) {
            SmartOffice.log('info', '进入配置表单页面（编辑）', params);
            // 通知配置表单组件加载配置数据
            if (params && params.config) {
                SmartOffice.Core.EventBus.emit('configForm:loadData', params.config);
            }
        },
        onExit: function() {
            SmartOffice.log('info', '离开配置表单页面');
            SmartOffice.Core.EventBus.emit('configForm:cleanup');
        }
    });

    // 字段映射管理页面
    this.register('fieldMapping', {
        title: '字段映射管理',
        pageId: 'fieldMappingPage',
        showBackButton: true,
        showAddButton: false,
        onEnter: function() {
            SmartOffice.log('info', '进入字段映射管理页面');
            // 通知字段映射页面组件初始化
            SmartOffice.Core.EventBus.emit('fieldMapping:init');
        },
        onExit: function() {
            SmartOffice.log('info', '离开字段映射管理页面');
            // 通知字段映射页面组件清理状态
            SmartOffice.Core.EventBus.emit('fieldMapping:cleanup');
        }
    });

    SmartOffice.log('info', '默认路由已注册');
};

/**
 * @function Router.prototype.register
 * @description 注册路由
 * @param {string} path - 路由路径
 * @param {Object} config - 路由配置
 */
Router.prototype.register = function(path, config) {
    if (typeof path !== 'string' || !config) {
        SmartOffice.log('error', '路由注册参数无效:', { path, config });
        return;
    }
    
    this.routes[path] = {
        title: config.title || '页面',
        pageId: config.pageId || '',
        showBackButton: config.showBackButton !== false,
        showAddButton: config.showAddButton === true,
        onEnter: config.onEnter || null,
        onExit: config.onExit || null,
        params: config.params || {}
    };
    
    SmartOffice.log('debug', '路由已注册:', path);
};

/**
 * @function Router.prototype.navigate
 * @description 导航到指定路由
 * @param {string} path - 路由路径
 * @param {Object} params - 路由参数
 * @param {boolean} addToHistory - 是否添加到历史记录
 */
Router.prototype.navigate = function(path, params, addToHistory) {
    if (typeof path !== 'string') {
        SmartOffice.log('error', '路由路径必须是字符串:', path);
        return;
    }
    
    addToHistory = addToHistory !== false; // 默认为true
    
    this.navigateToRoute(path, addToHistory, params);
};

/**
 * @function Router.prototype.navigateToRoute
 * @description 导航到路由的内部实现
 * @param {string} path - 路由路径
 * @param {boolean} addToHistory - 是否添加到历史记录
 * @param {Object} params - 路由参数
 */
Router.prototype.navigateToRoute = function(path, addToHistory, params) {
    const route = this.routes[path];
    if (!route) {
        SmartOffice.log('error', '未找到路由:', path);
        return;
    }
    
    try {
        // 执行当前路由的退出回调
        if (this.currentRoute && this.routes[this.currentRoute] && this.routes[this.currentRoute].onExit) {
            this.routes[this.currentRoute].onExit();
        }
        
        // 隐藏所有页面
        this.hideAllPages();
        
        // 显示目标页面
        this.showPage(route.pageId);
        
        // 更新导航栏
        this.updateNavigation(route);
        
        // 添加到历史记录
        if (addToHistory) {
            this.history.push(this.currentRoute);
            
            // 更新浏览器历史
            const state = { route: path, params: params };
            history.pushState(state, route.title, '#' + path);
        }
        
        // 更新当前路由
        this.currentRoute = path;
        
        // 执行进入回调
        if (route.onEnter) {
            route.onEnter(params);
        }
        
        // 触发路由变化事件
        this.eventBus.emit(SmartOffice.Events.ROUTE_CHANGE, {
            from: this.history[this.history.length - 1] || '',
            to: path,
            params: params
        });
        
        SmartOffice.log('info', '路由导航完成:', path);
        
    } catch (error) {
        SmartOffice.log('error', '路由导航失败:', {
            path: path,
            error: error.message
        });
        
        this.eventBus.emit(SmartOffice.Events.ROUTE_ERROR, {
            path: path,
            error: error
        });
    }
};

/**
 * @function Router.prototype.hideAllPages
 * @description 隐藏所有页面
 */
Router.prototype.hideAllPages = function() {
    const pages = document.querySelectorAll('.page');
    for (let i = 0; i < pages.length; i++) {
        pages[i].classList.add('page-hidden');
        pages[i].classList.remove('page-visible');
    }
};

/**
 * @function Router.prototype.showPage
 * @description 显示指定页面
 * @param {string} pageId - 页面ID
 */
Router.prototype.showPage = function(pageId) {
    const page = document.getElementById(pageId);
    if (page) {
        page.classList.remove('page-hidden');
        page.classList.add('page-visible');
    } else {
        SmartOffice.log('error', '未找到页面元素:', pageId);
    }
};

/**
 * @function Router.prototype.updateNavigation
 * @description 更新导航栏
 * @param {Object} route - 路由配置
 */
Router.prototype.updateNavigation = function(route) {
    // 更新标题
    const titleElement = document.getElementById('navTitle');
    if (titleElement) {
        titleElement.textContent = route.title;
    }
    
    // 更新返回按钮
    const backButton = document.getElementById('navBack');
    if (backButton) {
        if (route.showBackButton && this.history.length > 0) {
            backButton.style.display = 'block';
        } else {
            backButton.style.display = 'none';
        }
    }
    
    // 更新添加按钮
    const addButton = document.getElementById('navAdd');
    if (addButton) {
        if (route.showAddButton) {
            addButton.style.display = 'block';
        } else {
            addButton.style.display = 'none';
        }
    }
};

/**
 * @function Router.prototype.back
 * @description 返回上一页
 */
Router.prototype.back = function() {
    if (this.history.length > 0) {
        const previousRoute = this.history.pop();
        this.navigateToRoute(previousRoute, false);
        
        // 更新浏览器历史
        history.back();
    } else {
        SmartOffice.log('info', '没有可返回的页面');
    }
};

/**
 * @function Router.prototype.getCurrentRoute
 * @description 获取当前路由
 * @returns {string} 当前路由路径
 */
Router.prototype.getCurrentRoute = function() {
    return this.currentRoute;
};

/**
 * @function Router.prototype.getHistory
 * @description 获取路由历史
 * @returns {Array} 路由历史
 */
Router.prototype.getHistory = function() {
    return this.history.slice(); // 返回副本
};

/**
 * @function Router.prototype.clearHistory
 * @description 清空路由历史
 */
Router.prototype.clearHistory = function() {
    this.history = [];
    SmartOffice.log('info', '路由历史已清空');
};

// 创建全局路由管理器实例
SmartOffice.Core.Router = new Router();

SmartOffice.log('info', 'SmartOffice路由管理模块初始化完成');
