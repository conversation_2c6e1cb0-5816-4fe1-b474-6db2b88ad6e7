/**
 * @file SmartOffice存储管理模块
 * @description 本地存储管理，支持localStorage和数据序列化
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function StorageManager
 * @description 存储管理器构造函数
 * @constructor
 */
function StorageManager() {
    /**
     * @property {string} prefix - 存储键前缀
     */
    this.prefix = SmartOffice.Config.STORAGE_PREFIX;
    
    /**
     * @property {boolean} available - localStorage是否可用
     */
    this.available = this.checkAvailability();
    
    /**
     * @property {Object} cache - 内存缓存
     */
    this.cache = {};
    
    SmartOffice.log('info', 'StorageManager初始化完成', {
        available: this.available,
        prefix: this.prefix
    });
}

/**
 * @function StorageManager.prototype.checkAvailability
 * @description 检查localStorage是否可用
 * @returns {boolean} 是否可用
 */
StorageManager.prototype.checkAvailability = function() {
    try {
        const testKey = this.prefix + 'test';
        localStorage.setItem(testKey, 'test');
        localStorage.removeItem(testKey);
        return true;
    } catch (error) {
        SmartOffice.log('warn', 'localStorage不可用，将使用内存缓存:', error);
        return false;
    }
};

/**
 * @function StorageManager.prototype.getKey
 * @description 获取带前缀的完整键名
 * @param {string} key - 原始键名
 * @returns {string} 完整键名
 */
StorageManager.prototype.getKey = function(key) {
    return this.prefix + key;
};

/**
 * @function StorageManager.prototype.set
 * @description 设置存储值
 * @param {string} key - 键名
 * @param {*} value - 值
 * @returns {boolean} 是否设置成功
 */
StorageManager.prototype.set = function(key, value) {
    if (typeof key !== 'string') {
        SmartOffice.log('error', '存储键名必须是字符串:', key);
        return false;
    }
    
    try {
        const fullKey = this.getKey(key);
        const serializedValue = JSON.stringify({
            data: value,
            timestamp: Date.now(),
            version: SmartOffice.Config.STORAGE_VERSION
        });
        
        if (this.available) {
            localStorage.setItem(fullKey, serializedValue);
        }
        
        // 同时更新内存缓存
        this.cache[key] = value;
        
        SmartOffice.log('debug', '存储值已设置:', key);
        return true;
        
    } catch (error) {
        SmartOffice.log('error', '设置存储值失败:', {
            key: key,
            error: error.message
        });
        return false;
    }
};

/**
 * @function StorageManager.prototype.get
 * @description 获取存储值
 * @param {string} key - 键名
 * @param {*} defaultValue - 默认值
 * @returns {*} 存储的值或默认值
 */
StorageManager.prototype.get = function(key, defaultValue) {
    if (typeof key !== 'string') {
        SmartOffice.log('error', '存储键名必须是字符串:', key);
        return defaultValue;
    }
    
    try {
        // 先检查内存缓存
        if (this.cache.hasOwnProperty(key)) {
            return this.cache[key];
        }
        
        // 从localStorage读取
        if (this.available) {
            const fullKey = this.getKey(key);
            const serializedValue = localStorage.getItem(fullKey);
            
            if (serializedValue !== null) {
                const parsed = JSON.parse(serializedValue);
                
                // 检查版本兼容性
                if (parsed.version && parsed.version !== SmartOffice.Config.STORAGE_VERSION) {
                    SmartOffice.log('warn', '存储版本不匹配，使用默认值:', {
                        key: key,
                        storedVersion: parsed.version,
                        currentVersion: SmartOffice.Config.STORAGE_VERSION
                    });
                    return defaultValue;
                }
                
                // 更新内存缓存
                this.cache[key] = parsed.data;
                
                SmartOffice.log('debug', '从存储获取值:', key);
                return parsed.data;
            }
        }
        
        SmartOffice.log('debug', '存储中未找到值，返回默认值:', key);
        return defaultValue;
        
    } catch (error) {
        SmartOffice.log('error', '获取存储值失败:', {
            key: key,
            error: error.message
        });
        return defaultValue;
    }
};

/**
 * @function StorageManager.prototype.remove
 * @description 移除存储值
 * @param {string} key - 键名
 * @returns {boolean} 是否移除成功
 */
StorageManager.prototype.remove = function(key) {
    if (typeof key !== 'string') {
        SmartOffice.log('error', '存储键名必须是字符串:', key);
        return false;
    }
    
    try {
        const fullKey = this.getKey(key);
        
        if (this.available) {
            localStorage.removeItem(fullKey);
        }
        
        // 从内存缓存中移除
        delete this.cache[key];
        
        SmartOffice.log('debug', '存储值已移除:', key);
        return true;
        
    } catch (error) {
        SmartOffice.log('error', '移除存储值失败:', {
            key: key,
            error: error.message
        });
        return false;
    }
};

/**
 * @function StorageManager.prototype.clear
 * @description 清空所有存储值
 * @returns {boolean} 是否清空成功
 */
StorageManager.prototype.clear = function() {
    try {
        if (this.available) {
            // 只清除带有指定前缀的项
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(this.prefix)) {
                    keysToRemove.push(key);
                }
            }
            
            for (let i = 0; i < keysToRemove.length; i++) {
                localStorage.removeItem(keysToRemove[i]);
            }
        }
        
        // 清空内存缓存
        this.cache = {};
        
        SmartOffice.log('info', '所有存储值已清空');
        return true;
        
    } catch (error) {
        SmartOffice.log('error', '清空存储失败:', error);
        return false;
    }
};

/**
 * @function StorageManager.prototype.has
 * @description 检查是否存在指定键
 * @param {string} key - 键名
 * @returns {boolean} 是否存在
 */
StorageManager.prototype.has = function(key) {
    if (typeof key !== 'string') {
        return false;
    }
    
    // 检查内存缓存
    if (this.cache.hasOwnProperty(key)) {
        return true;
    }
    
    // 检查localStorage
    if (this.available) {
        const fullKey = this.getKey(key);
        return localStorage.getItem(fullKey) !== null;
    }
    
    return false;
};

/**
 * @function StorageManager.prototype.keys
 * @description 获取所有存储键名
 * @returns {Array} 键名列表
 */
StorageManager.prototype.keys = function() {
    const keys = [];
    
    // 从内存缓存获取
    for (const key in this.cache) {
        if (this.cache.hasOwnProperty(key)) {
            keys.push(key);
        }
    }
    
    // 从localStorage获取
    if (this.available) {
        for (let i = 0; i < localStorage.length; i++) {
            const fullKey = localStorage.key(i);
            if (fullKey && fullKey.startsWith(this.prefix)) {
                const key = fullKey.substring(this.prefix.length);
                if (keys.indexOf(key) === -1) {
                    keys.push(key);
                }
            }
        }
    }
    
    return keys;
};

/**
 * @function StorageManager.prototype.size
 * @description 获取存储项数量
 * @returns {number} 存储项数量
 */
StorageManager.prototype.size = function() {
    return this.keys().length;
};

/**
 * @function StorageManager.prototype.getUsage
 * @description 获取localStorage使用情况
 * @returns {Object} 使用情况信息
 */
StorageManager.prototype.getUsage = function() {
    if (!this.available) {
        return {
            used: 0,
            total: 0,
            available: 0,
            percentage: 0
        };
    }
    
    try {
        let used = 0;
        
        // 计算当前应用使用的存储空间
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith(this.prefix)) {
                const value = localStorage.getItem(key);
                used += key.length + (value ? value.length : 0);
            }
        }
        
        // 尝试估算总可用空间
        const testData = new Array(1024).join('a'); // 1KB的数据
        let total = 0;
        
        try {
            let testKey = this.prefix + 'size_test_';
            while (true) {
                localStorage.setItem(testKey + total, testData);
                total += testData.length;
                localStorage.removeItem(testKey + total);
            }
        } catch (e) {
            // 达到存储限制
        }
        
        return {
            used: used,
            total: Math.max(total, used),
            available: Math.max(0, total - used),
            percentage: total > 0 ? (used / total * 100) : 0
        };
        
    } catch (error) {
        SmartOffice.log('error', '获取存储使用情况失败:', error);
        return {
            used: 0,
            total: 0,
            available: 0,
            percentage: 0
        };
    }
};

/**
 * @function StorageManager.prototype.export
 * @description 导出所有存储数据
 * @returns {Object} 导出的数据
 */
StorageManager.prototype.export = function() {
    const data = {};
    const keys = this.keys();
    
    for (let i = 0; i < keys.length; i++) {
        const key = keys[i];
        data[key] = this.get(key);
    }
    
    return {
        version: SmartOffice.Config.STORAGE_VERSION,
        timestamp: Date.now(),
        data: data
    };
};

/**
 * @function StorageManager.prototype.import
 * @description 导入存储数据
 * @param {Object} exportedData - 导出的数据
 * @returns {boolean} 是否导入成功
 */
StorageManager.prototype.import = function(exportedData) {
    try {
        if (!exportedData || !exportedData.data) {
            SmartOffice.log('error', '导入数据格式无效');
            return false;
        }
        
        // 检查版本兼容性
        if (exportedData.version !== SmartOffice.Config.STORAGE_VERSION) {
            SmartOffice.log('warn', '导入数据版本不匹配');
        }
        
        // 导入数据
        const data = exportedData.data;
        for (const key in data) {
            if (data.hasOwnProperty(key)) {
                this.set(key, data[key]);
            }
        }
        
        SmartOffice.log('info', '存储数据导入成功');
        return true;
        
    } catch (error) {
        SmartOffice.log('error', '导入存储数据失败:', error);
        return false;
    }
};

/**
 * @function StorageManager.prototype.getItemsByPrefix
 * @description 按前缀批量获取存储项
 * @param {string} prefix - 键名前缀
 * @param {Object} options - 可选配置
 * @returns {Array} 匹配的存储项列表
 */
StorageManager.prototype.getItemsByPrefix = function(prefix, options) {
    options = options || {};
    const limit = options.limit || 100;
    const offset = options.offset || 0;
    const sortBy = options.sortBy || 'key';
    const sortOrder = options.sortOrder || 'asc';

    try {
        const items = [];
        const allKeys = this.keys();

        // 筛选匹配前缀的键
        const matchingKeys = allKeys.filter(function(key) {
            return key.startsWith(prefix);
        });

        // 排序
        matchingKeys.sort(function(a, b) {
            let valueA, valueB;

            if (sortBy === 'key') {
                valueA = a;
                valueB = b;
            } else {
                // 按其他字段排序需要获取数据
                const dataA = SmartOffice.Core.Storage.get(a);
                const dataB = SmartOffice.Core.Storage.get(b);
                valueA = dataA && dataA[sortBy] ? dataA[sortBy] : '';
                valueB = dataB && dataB[sortBy] ? dataB[sortBy] : '';
            }

            if (sortOrder === 'desc') {
                return valueA < valueB ? 1 : (valueA > valueB ? -1 : 0);
            } else {
                return valueA > valueB ? 1 : (valueA < valueB ? -1 : 0);
            }
        });

        // 分页
        const paginatedKeys = matchingKeys.slice(offset, offset + limit);

        // 获取数据
        for (let i = 0; i < paginatedKeys.length; i++) {
            const key = paginatedKeys[i];
            const data = this.get(key);
            if (data !== null) {
                items.push({
                    key: key,
                    data: data,
                    metadata: this.getItemMetadata(key)
                });
            }
        }

        SmartOffice.log('debug', `按前缀获取存储项: ${prefix}, 找到 ${items.length} 项`);
        return items;

    } catch (error) {
        SmartOffice.log('error', '按前缀获取存储项失败:', error);
        return [];
    }
};

/**
 * @function StorageManager.prototype.getItemMetadata
 * @description 获取存储项的元数据
 * @param {string} key - 键名
 * @returns {Object|null} 元数据对象
 */
StorageManager.prototype.getItemMetadata = function(key) {
    try {
        if (!this.available) {
            return null;
        }

        const fullKey = this.getKey(key);
        const serializedValue = localStorage.getItem(fullKey);

        if (serializedValue !== null) {
            const parsed = JSON.parse(serializedValue);
            return {
                timestamp: parsed.timestamp,
                version: parsed.version,
                size: serializedValue.length
            };
        }

        return null;
    } catch (error) {
        SmartOffice.log('error', '获取存储项元数据失败:', error);
        return null;
    }
};

/**
 * @function StorageManager.prototype.saveFieldMappingTemplate
 * @description 保存字段映射模板
 * @param {Object} template - 映射模板配置
 * @returns {boolean} 是否保存成功
 */
StorageManager.prototype.saveFieldMappingTemplate = function(template) {
    try {
        // 基础验证
        if (!template || !template.id || !template.name) {
            SmartOffice.log('error', '字段映射模板配置无效');
            return false;
        }

        // 数据验证（如果数据结构定义模块可用）
        if (SmartOffice.Data && SmartOffice.Data.Schemas) {
            const validation = SmartOffice.Data.Schemas.validateData(template, 'FieldMappingTemplate');
            if (!validation.isValid) {
                SmartOffice.log('error', '字段映射模板验证失败:', validation.errors);
                return false;
            }
        }

        // 生成存储键
        const key = 'field_mapping_' + template.id;

        // 更新使用统计
        const existing = this.get(key);
        if (existing) {
            template.usageCount = (existing.usageCount || 0) + 1;
            template.lastUsed = new Date().toISOString();
        } else {
            template.usageCount = template.usageCount || 0;
        }

        // 保存数据
        const success = this.set(key, template);

        if (success) {
            SmartOffice.log('info', '字段映射模板保存成功: ' + template.name);
        }

        return success;
    } catch (error) {
        SmartOffice.log('error', '保存字段映射模板失败:', error);
        return false;
    }
};

/**
 * @function StorageManager.prototype.getAllFieldMappingTemplates
 * @description 获取所有字段映射模板
 * @param {Object} options - 查询选项
 * @returns {Array} 映射模板列表
 */
StorageManager.prototype.getAllFieldMappingTemplates = function(options) {
    try {
        const items = this.getItemsByPrefix('field_mapping_', options);
        const templates = items.map(function(item) {
            return item.data;
        });

        SmartOffice.log('info', '获取字段映射模板列表成功，共' + templates.length + '个模板');
        return templates;
    } catch (error) {
        SmartOffice.log('error', '获取字段映射模板列表失败:', error);
        return [];
    }
};

/**
 * @function StorageManager.prototype.saveTimeRangeConfig
 * @description 保存时间段配置
 * @param {Object} timeRangeConfig - 时间段配置
 * @returns {boolean} 是否保存成功
 */
StorageManager.prototype.saveTimeRangeConfig = function(timeRangeConfig) {
    try {
        // 基础验证
        if (!timeRangeConfig || !timeRangeConfig.id || !timeRangeConfig.name) {
            SmartOffice.log('error', '时间段配置无效');
            return false;
        }

        // 数据验证（如果数据结构定义模块可用）
        if (SmartOffice.Data && SmartOffice.Data.Schemas) {
            const validation = SmartOffice.Data.Schemas.validateData(timeRangeConfig, 'TimeRangeConfig');
            if (!validation.isValid) {
                SmartOffice.log('error', '时间段配置验证失败:', validation.errors);
                return false;
            }
        }

        // 生成存储键
        const key = 'time_range_' + timeRangeConfig.id;

        // 更新使用统计
        const existing = this.get(key);
        if (existing) {
            timeRangeConfig.usageCount = (existing.usageCount || 0) + 1;
            timeRangeConfig.lastUsed = new Date().toISOString();
        } else {
            timeRangeConfig.usageCount = timeRangeConfig.usageCount || 0;
        }

        // 保存数据
        const success = this.set(key, timeRangeConfig);

        if (success) {
            SmartOffice.log('info', '时间段配置保存成功: ' + timeRangeConfig.name);
        }

        return success;
    } catch (error) {
        SmartOffice.log('error', '保存时间段配置失败:', error);
        return false;
    }
};

/**
 * @function StorageManager.prototype.getAllTimeRangeConfigs
 * @description 获取所有时间段配置
 * @param {Object} options - 查询选项
 * @returns {Array} 时间段配置列表
 */
StorageManager.prototype.getAllTimeRangeConfigs = function(options) {
    try {
        const items = this.getItemsByPrefix('time_range_', options);
        const configs = items.map(function(item) {
            return item.data;
        });

        SmartOffice.log('info', '获取时间段配置列表成功，共' + configs.length + '个配置');
        return configs;
    } catch (error) {
        SmartOffice.log('error', '获取时间段配置列表失败:', error);
        return [];
    }
};

/**
 * @function StorageManager.prototype.migrateStorageData
 * @description 迁移存储数据到最新版本
 * @param {string} dataType - 数据类型 ('fieldMapping' | 'timeRange' | 'all')
 * @returns {Object} 迁移结果
 */
StorageManager.prototype.migrateStorageData = function(dataType) {
    const result = {
        success: true,
        migratedCount: 0,
        errors: [],
        details: []
    };

    try {
        let prefixes = [];

        if (dataType === 'all') {
            prefixes = ['field_mapping_', 'time_range_'];
        } else if (dataType === 'fieldMapping') {
            prefixes = ['field_mapping_'];
        } else if (dataType === 'timeRange') {
            prefixes = ['time_range_'];
        } else {
            result.success = false;
            result.errors.push('不支持的数据类型: ' + dataType);
            return result;
        }

        for (let i = 0; i < prefixes.length; i++) {
            const prefix = prefixes[i];
            const items = this.getItemsByPrefix(prefix, { limit: 1000 });

            for (let j = 0; j < items.length; j++) {
                const item = items[j];
                const currentVersion = item.metadata ? item.metadata.version : '0.9.0';

                if (currentVersion !== SmartOffice.Config.STORAGE_VERSION) {
                    // 需要迁移
                    const schemaName = this.getSchemaNameFromPrefix(prefix);
                    if (schemaName && SmartOffice.Data.Schemas) {
                        const migrationResult = SmartOffice.Data.Schemas.migrateData(
                            item.data,
                            schemaName,
                            currentVersion
                        );

                        if (migrationResult.success) {
                            // 保存迁移后的数据
                            this.set(item.key, migrationResult.migratedData);
                            result.migratedCount++;
                            result.details.push({
                                key: item.key,
                                fromVersion: currentVersion,
                                toVersion: SmartOffice.Config.STORAGE_VERSION,
                                changes: migrationResult.changes
                            });
                        } else {
                            result.errors = result.errors.concat(migrationResult.errors);
                        }
                    }
                }
            }
        }

        SmartOffice.log('info', `数据迁移完成，迁移了 ${result.migratedCount} 个项目`);
        return result;

    } catch (error) {
        result.success = false;
        result.errors.push('数据迁移失败: ' + error.message);
        SmartOffice.log('error', '数据迁移失败:', error);
        return result;
    }
};

/**
 * @function StorageManager.prototype.getSchemaNameFromPrefix
 * @description 根据存储键前缀获取对应的数据结构名称
 * @param {string} prefix - 存储键前缀
 * @returns {string|null} 数据结构名称
 */
StorageManager.prototype.getSchemaNameFromPrefix = function(prefix) {
    const mapping = {
        'field_mapping_': 'FieldMappingTemplate',
        'time_range_': 'TimeRangeConfig'
    };
    return mapping[prefix] || null;
};

/**
 * @function StorageManager.prototype.validateStorageIntegrity
 * @description 验证存储数据完整性
 * @param {string} dataType - 数据类型 ('fieldMapping' | 'timeRange' | 'all')
 * @returns {Object} 验证结果
 */
StorageManager.prototype.validateStorageIntegrity = function(dataType) {
    const result = {
        isValid: true,
        totalItems: 0,
        validItems: 0,
        invalidItems: 0,
        errors: [],
        warnings: []
    };

    try {
        let prefixes = [];

        if (dataType === 'all') {
            prefixes = ['field_mapping_', 'time_range_'];
        } else if (dataType === 'fieldMapping') {
            prefixes = ['field_mapping_'];
        } else if (dataType === 'timeRange') {
            prefixes = ['time_range_'];
        }

        for (let i = 0; i < prefixes.length; i++) {
            const prefix = prefixes[i];
            const items = this.getItemsByPrefix(prefix, { limit: 1000 });
            const schemaName = this.getSchemaNameFromPrefix(prefix);

            result.totalItems += items.length;

            for (let j = 0; j < items.length; j++) {
                const item = items[j];

                if (schemaName && SmartOffice.Data.Schemas) {
                    const validation = SmartOffice.Data.Schemas.validateData(item.data, schemaName);

                    if (validation.isValid) {
                        result.validItems++;
                    } else {
                        result.invalidItems++;
                        result.isValid = false;
                        result.errors.push({
                            key: item.key,
                            errors: validation.errors
                        });
                    }

                    if (validation.warnings.length > 0) {
                        result.warnings.push({
                            key: item.key,
                            warnings: validation.warnings
                        });
                    }
                } else {
                    result.warnings.push({
                        key: item.key,
                        warnings: ['无法验证数据结构']
                    });
                }
            }
        }

        SmartOffice.log('info', `存储完整性验证完成: ${result.validItems}/${result.totalItems} 项有效`);
        return result;

    } catch (error) {
        result.isValid = false;
        result.errors.push('存储完整性验证失败: ' + error.message);
        SmartOffice.log('error', '存储完整性验证失败:', error);
        return result;
    }
};

/**
 * @function StorageManager.prototype.optimizeStorage
 * @description 优化存储性能
 * @returns {Object} 优化结果
 */
StorageManager.prototype.optimizeStorage = function() {
    const result = {
        success: true,
        optimizedItems: 0,
        freedSpace: 0,
        errors: []
    };

    try {
        // 清理过期的缓存
        this.cache = {};

        // 压缩存储数据（移除不必要的字段）
        const allKeys = this.keys();

        for (let i = 0; i < allKeys.length; i++) {
            const key = allKeys[i];
            const data = this.get(key);

            if (data && typeof data === 'object') {
                const originalSize = JSON.stringify(data).length;

                // 移除空值和未定义的字段
                const optimizedData = this.removeEmptyFields(data);

                const optimizedSize = JSON.stringify(optimizedData).length;

                if (optimizedSize < originalSize) {
                    this.set(key, optimizedData);
                    result.optimizedItems++;
                    result.freedSpace += (originalSize - optimizedSize);
                }
            }
        }

        SmartOffice.log('info', `存储优化完成: 优化了 ${result.optimizedItems} 个项目，释放了 ${result.freedSpace} 字节`);
        return result;

    } catch (error) {
        result.success = false;
        result.errors.push('存储优化失败: ' + error.message);
        SmartOffice.log('error', '存储优化失败:', error);
        return result;
    }
};

/**
 * @function StorageManager.prototype.removeEmptyFields
 * @description 移除对象中的空字段
 * @param {Object} obj - 要处理的对象
 * @returns {Object} 处理后的对象
 */
StorageManager.prototype.removeEmptyFields = function(obj) {
    if (typeof obj !== 'object' || obj === null) {
        return obj;
    }

    const cleaned = {};

    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            const value = obj[key];

            if (value !== null && value !== undefined && value !== '') {
                if (typeof value === 'object' && !Array.isArray(value)) {
                    const cleanedValue = this.removeEmptyFields(value);
                    if (Object.keys(cleanedValue).length > 0) {
                        cleaned[key] = cleanedValue;
                    }
                } else if (Array.isArray(value) && value.length > 0) {
                    cleaned[key] = value;
                } else if (typeof value !== 'object') {
                    cleaned[key] = value;
                }
            }
        }
    }

    return cleaned;
};

// 创建全局存储管理器实例
SmartOffice.Core.Storage = new StorageManager();

SmartOffice.log('info', 'SmartOffice存储管理模块初始化完成');
