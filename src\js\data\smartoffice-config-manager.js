/**
 * @file SmartOffice配置管理器
 * @description 透视表配置的管理和操作
 * <AUTHOR> Team
 */

/**
 * @function ConfigManager
 * @description 配置管理器构造函数
 * @constructor
 */
function ConfigManager() {
    /**
     * @property {string} storageKey - 本地存储键名
     */
    this.storageKey = 'smartoffice_pivot_configs';

    /**
     * @property {Object} storage - 存储管理器引用
     */
    this.storage = SmartOffice.Core.Storage;

    /**
     * @property {Object} helpers - 工具函数引用
     */
    this.helpers = SmartOffice.Utils.Helpers;

    SmartOffice.log('info', 'ConfigManager配置管理器初始化完成');
}

/**
 * @function ConfigManager.prototype.getAllConfigs
 * @description 获取所有已保存的配置
 * @returns {Array} 配置数组
 */
ConfigManager.prototype.getAllConfigs = function() {
    try {
        const configs = this.storage.get(this.storageKey, []);
        SmartOffice.log('info', '获取配置列表成功，共' + configs.length + '个配置');
        return configs;
    } catch (error) {
        SmartOffice.log('error', '获取配置列表失败:', error);
        return [];
    }
};

/**
 * @function ConfigManager.prototype.getConfigById
 * @description 根据ID获取配置
 * @param {string} configId - 配置ID
 * @returns {Object|null} 配置对象或null
 */
ConfigManager.prototype.getConfigById = function(configId) {
    try {
        const configs = this.getAllConfigs();
        const config = configs.find(function(c) { return c.id === configId; });

        if (config) {
            SmartOffice.log('info', '获取配置成功: ' + config.name);
        } else {
            SmartOffice.log('warn', '配置不存在: ' + configId);
        }

        return config || null;
    } catch (error) {
        SmartOffice.log('error', '获取配置失败:', error);
        return null;
    }
};

/**
 * @function ConfigManager.prototype.saveConfig
 * @description 保存配置
 * @param {Object} config - 配置对象
 * @returns {boolean} 是否保存成功
 */
ConfigManager.prototype.saveConfig = function(config) {
    try {
        if (!config || !config.name) {
            throw new Error('配置对象无效');
        }

        const configs = this.getAllConfigs();

        // 生成ID（如果没有）
        if (!config.id) {
            config.id = this.helpers.generateId();
        }

        // 设置时间戳
        const now = new Date().toISOString();
        if (!config.createdAt) {
            config.createdAt = now;
        }
        config.updatedAt = now;

        // 检查是否为更新操作
        const existingIndex = configs.findIndex(function(c) { return c.id === config.id; });

        if (existingIndex >= 0) {
            // 更新现有配置
            configs[existingIndex] = config;
            SmartOffice.log('info', '配置更新成功: ' + config.name);
        } else {
            // 添加新配置
            configs.push(config);
            SmartOffice.log('info', '配置保存成功: ' + config.name);
        }

        // 保存到存储
        this.storage.set(this.storageKey, configs);

        return true;
    } catch (error) {
        SmartOffice.log('error', '配置保存失败:', error);
        return false;
    }
};

/**
 * @function ConfigManager.prototype.deleteConfig
 * @description 删除配置
 * @param {string} configId - 配置ID
 * @returns {boolean} 是否删除成功
 */
ConfigManager.prototype.deleteConfig = function(configId) {
    try {
        const configs = this.getAllConfigs();
        const initialLength = configs.length;

        const filteredConfigs = configs.filter(function(c) { return c.id !== configId; });

        if (filteredConfigs.length < initialLength) {
            this.storage.set(this.storageKey, filteredConfigs);
            SmartOffice.log('info', '配置删除成功: ' + configId);
            return true;
        } else {
            SmartOffice.log('warn', '配置不存在，无法删除: ' + configId);
            return false;
        }
    } catch (error) {
        SmartOffice.log('error', '配置删除失败:', error);
        return false;
    }
};

/**
 * @function ConfigManager.prototype.duplicateConfig
 * @description 复制配置
 * @param {string} configId - 源配置ID
 * @param {string} newName - 新配置名称（可选）
 * @returns {Object|null} 新配置对象或null
 */
ConfigManager.prototype.duplicateConfig = function(configId, newName) {
    try {
        const sourceConfig = this.getConfigById(configId);
        if (!sourceConfig) {
            throw new Error('源配置不存在');
        }

        // 创建配置副本
        const newConfig = JSON.parse(JSON.stringify(sourceConfig));
        newConfig.id = this.helpers.generateId();
        newConfig.name = newName || (sourceConfig.name + ' (副本)');
        newConfig.createdAt = new Date().toISOString();
        newConfig.updatedAt = newConfig.createdAt;

        // 保存新配置
        if (this.saveConfig(newConfig)) {
            SmartOffice.log('info', '配置复制成功: ' + newConfig.name);
            return newConfig;
        } else {
            throw new Error('保存配置副本失败');
        }
    } catch (error) {
        SmartOffice.log('error', '配置复制失败:', error);
        return null;
    }
};

// 创建全局实例
SmartOffice.Data.ConfigManager = new ConfigManager();

SmartOffice.log('info', 'SmartOffice配置管理器模块初始化完成');
