/**
 * @file SmartOffice数据结构定义模块
 * @description 定义字段映射、时间段配置等数据结构和验证规则
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function DataSchemas
 * @description 数据结构定义管理器构造函数
 * @constructor
 */
function DataSchemas() {
    /**
     * @property {string} currentVersion - 当前数据结构版本
     */
    this.currentVersion = '1.0.0';

    /**
     * @property {Object} schemas - 数据结构定义
     */
    this.schemas = this.initializeSchemas();

    /**
     * @property {Object} migrationRules - 数据迁移规则
     */
    this.migrationRules = this.initializeMigrationRules();

    SmartOffice.log('info', 'DataSchemas数据结构定义管理器初始化完成');
}

/**
 * @function DataSchemas.prototype.initializeSchemas
 * @description 初始化数据结构定义
 * @returns {Object} 数据结构定义对象
 */
DataSchemas.prototype.initializeSchemas = function() {
    return {
        /**
         * @schema FieldMappingTemplate - 字段映射模板数据结构
         */
        FieldMappingTemplate: {
            version: '1.0.0',
            required: ['id', 'name', 'mappings'],
            properties: {
                id: {
                    type: 'string',
                    pattern: /^mapping_[a-z0-9_]+$/,
                    description: '映射模板唯一标识符'
                },
                name: {
                    type: 'string',
                    minLength: 1,
                    maxLength: 100,
                    description: '映射模板名称'
                },
                description: {
                    type: 'string',
                    maxLength: 500,
                    optional: true,
                    description: '映射模板描述'
                },
                mappings: {
                    type: 'array',
                    minItems: 1,
                    maxItems: 50,
                    items: {
                        type: 'object',
                        required: ['sourceField', 'targetField', 'dataType'],
                        properties: {
                            sourceField: {
                                type: 'string',
                                minLength: 1,
                                maxLength: 100,
                                description: '源字段名称'
                            },
                            targetField: {
                                type: 'string',
                                minLength: 1,
                                maxLength: 50,
                                description: '目标标准字段名称'
                            },
                            dataType: {
                                type: 'string',
                                enum: ['string', 'number', 'date', 'time'],
                                description: '数据类型'
                            },
                            confidence: {
                                type: 'number',
                                minimum: 0,
                                maximum: 1,
                                optional: true,
                                description: '映射置信度'
                            },
                            required: {
                                type: 'boolean',
                                optional: true,
                                default: false,
                                description: '是否为必需字段'
                            },
                            transform: {
                                type: 'string',
                                optional: true,
                                description: '数据转换规则'
                            },
                            validation: {
                                type: 'object',
                                optional: true,
                                description: '数据验证规则'
                            }
                        }
                    },
                    description: '字段映射配置列表'
                },
                category: {
                    type: 'string',
                    enum: ['sales', 'finance', 'hr', 'inventory', 'custom'],
                    optional: true,
                    default: 'custom',
                    description: '模板类别'
                },
                tags: {
                    type: 'array',
                    items: { type: 'string' },
                    maxItems: 10,
                    optional: true,
                    description: '模板标签'
                },
                isDefault: {
                    type: 'boolean',
                    optional: true,
                    default: false,
                    description: '是否为默认模板'
                },
                usageCount: {
                    type: 'number',
                    minimum: 0,
                    optional: true,
                    default: 0,
                    description: '使用次数'
                },
                lastUsed: {
                    type: 'string',
                    format: 'iso-date',
                    optional: true,
                    description: '最后使用时间'
                },
                createdAt: {
                    type: 'string',
                    format: 'iso-date',
                    description: '创建时间'
                },
                updatedAt: {
                    type: 'string',
                    format: 'iso-date',
                    description: '更新时间'
                },
                createdBy: {
                    type: 'string',
                    optional: true,
                    description: '创建者'
                }
            }
        },

        /**
         * @schema TimeRangeConfig - 时间段配置数据结构
         */
        TimeRangeConfig: {
            version: '1.0.0',
            required: ['id', 'name', 'start', 'end'],
            properties: {
                id: {
                    type: 'string',
                    pattern: /^timerange_[a-z0-9_]+$/,
                    description: '时间段唯一标识符'
                },
                name: {
                    type: 'string',
                    minLength: 1,
                    maxLength: 50,
                    description: '时间段名称'
                },
                description: {
                    type: 'string',
                    maxLength: 200,
                    optional: true,
                    description: '时间段描述'
                },
                type: {
                    type: 'string',
                    enum: ['preset', 'custom'],
                    default: 'custom',
                    description: '时间段类型'
                },
                start: {
                    type: 'string',
                    pattern: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
                    description: '开始时间 (HH:MM格式)'
                },
                end: {
                    type: 'string',
                    pattern: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
                    description: '结束时间 (HH:MM格式)'
                },
                isGlobal: {
                    type: 'boolean',
                    default: true,
                    description: '是否为全局时间段'
                },
                isActive: {
                    type: 'boolean',
                    optional: true,
                    default: false,
                    description: '是否当前激活'
                },
                color: {
                    type: 'string',
                    pattern: /^#[0-9A-Fa-f]{6}$/,
                    optional: true,
                    description: '时间段颜色标识'
                },
                priority: {
                    type: 'number',
                    minimum: 0,
                    maximum: 100,
                    optional: true,
                    default: 50,
                    description: '优先级'
                },
                usageCount: {
                    type: 'number',
                    minimum: 0,
                    optional: true,
                    default: 0,
                    description: '使用次数'
                },
                lastUsed: {
                    type: 'string',
                    format: 'iso-date',
                    optional: true,
                    description: '最后使用时间'
                },
                createdAt: {
                    type: 'string',
                    format: 'iso-date',
                    description: '创建时间'
                },
                updatedAt: {
                    type: 'string',
                    format: 'iso-date',
                    description: '更新时间'
                }
            }
        },

        /**
         * @schema PivotConfig - 透视表配置数据结构（扩展）
         */
        PivotConfigExtended: {
            version: '1.0.0',
            extends: 'PivotConfig', // 扩展现有的透视表配置
            additionalProperties: {
                fieldMappings: {
                    type: 'object',
                    optional: true,
                    description: '字段映射信息',
                    properties: {
                        templateId: {
                            type: 'string',
                            optional: true,
                            description: '使用的映射模板ID'
                        },
                        mappings: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    originalField: { type: 'string' },
                                    mappedField: { type: 'string' },
                                    dataType: { type: 'string' }
                                }
                            },
                            optional: true,
                            description: '字段映射关系'
                        }
                    }
                },
                timeRanges: {
                    type: 'array',
                    items: { type: 'string' },
                    optional: true,
                    description: '应用的时间段ID列表'
                },
                dataSource: {
                    type: 'object',
                    optional: true,
                    description: '数据源信息',
                    properties: {
                        fileName: { type: 'string', optional: true },
                        fileSize: { type: 'number', optional: true },
                        recordCount: { type: 'number', optional: true },
                        uploadTime: { type: 'string', format: 'iso-date', optional: true }
                    }
                }
            }
        },

        /**
         * @schema StorageMetadata - 存储元数据结构
         */
        StorageMetadata: {
            version: '1.0.0',
            required: ['key', 'type', 'version'],
            properties: {
                key: {
                    type: 'string',
                    description: '存储键名'
                },
                type: {
                    type: 'string',
                    enum: ['fieldMapping', 'timeRange', 'pivotConfig', 'userPreference'],
                    description: '数据类型'
                },
                version: {
                    type: 'string',
                    pattern: /^\d+\.\d+\.\d+$/,
                    description: '数据版本'
                },
                size: {
                    type: 'number',
                    minimum: 0,
                    optional: true,
                    description: '数据大小（字节）'
                },
                checksum: {
                    type: 'string',
                    optional: true,
                    description: '数据校验和'
                },
                encrypted: {
                    type: 'boolean',
                    optional: true,
                    default: false,
                    description: '是否加密'
                },
                compressed: {
                    type: 'boolean',
                    optional: true,
                    default: false,
                    description: '是否压缩'
                },
                createdAt: {
                    type: 'string',
                    format: 'iso-date',
                    description: '创建时间'
                },
                updatedAt: {
                    type: 'string',
                    format: 'iso-date',
                    description: '更新时间'
                },
                accessCount: {
                    type: 'number',
                    minimum: 0,
                    optional: true,
                    default: 0,
                    description: '访问次数'
                },
                lastAccessed: {
                    type: 'string',
                    format: 'iso-date',
                    optional: true,
                    description: '最后访问时间'
                }
            }
        }
    };
};

/**
 * @function DataSchemas.prototype.initializeMigrationRules
 * @description 初始化数据迁移规则
 * @returns {Object} 迁移规则对象
 */
DataSchemas.prototype.initializeMigrationRules = function() {
    return {
        // 从版本0.9.x迁移到1.0.0的规则
        '0.9.x_to_1.0.0': {
            FieldMappingTemplate: {
                // 添加新字段的默认值
                addFields: {
                    category: 'custom',
                    usageCount: 0,
                    isDefault: false
                },
                // 重命名字段
                renameFields: {},
                // 删除废弃字段
                removeFields: [],
                // 数据转换函数
                transform: function(oldData) {
                    // 确保mappings数组中的每个项都有必需的字段
                    if (oldData.mappings && Array.isArray(oldData.mappings)) {
                        oldData.mappings = oldData.mappings.map(function(mapping) {
                            return {
                                sourceField: mapping.sourceField || '',
                                targetField: mapping.targetField || '',
                                dataType: mapping.dataType || 'string',
                                confidence: mapping.confidence || 0.5,
                                required: mapping.required || false
                            };
                        });
                    }
                    return oldData;
                }
            },
            TimeRangeConfig: {
                addFields: {
                    isGlobal: true,
                    priority: 50,
                    usageCount: 0
                },
                renameFields: {},
                removeFields: [],
                transform: function(oldData) {
                    // 确保时间格式正确
                    if (oldData.start && !oldData.start.match(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)) {
                        oldData.start = '00:00';
                    }
                    if (oldData.end && !oldData.end.match(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)) {
                        oldData.end = '23:59';
                    }
                    return oldData;
                }
            }
        }
    };
};

/**
 * @function DataSchemas.prototype.validateData
 * @description 验证数据是否符合指定的数据结构
 * @param {*} data - 要验证的数据
 * @param {string} schemaName - 数据结构名称
 * @returns {Object} 验证结果
 */
DataSchemas.prototype.validateData = function(data, schemaName) {
    const result = {
        isValid: true,
        errors: [],
        warnings: []
    };

    try {
        const schema = this.schemas[schemaName];
        if (!schema) {
            result.isValid = false;
            result.errors.push('未找到数据结构定义: ' + schemaName);
            return result;
        }

        // 验证必需字段
        if (schema.required) {
            for (let i = 0; i < schema.required.length; i++) {
                const requiredField = schema.required[i];
                if (!data.hasOwnProperty(requiredField)) {
                    result.isValid = false;
                    result.errors.push('缺少必需字段: ' + requiredField);
                }
            }
        }

        // 验证字段属性
        if (schema.properties) {
            for (const fieldName in data) {
                if (data.hasOwnProperty(fieldName)) {
                    const fieldValue = data[fieldName];
                    const fieldSchema = schema.properties[fieldName];

                    if (fieldSchema) {
                        const fieldValidation = this.validateField(fieldValue, fieldSchema, fieldName);
                        if (!fieldValidation.isValid) {
                            result.isValid = false;
                            result.errors = result.errors.concat(fieldValidation.errors);
                        }
                        result.warnings = result.warnings.concat(fieldValidation.warnings);
                    } else {
                        result.warnings.push('未定义的字段: ' + fieldName);
                    }
                }
            }
        }

        return result;
    } catch (error) {
        result.isValid = false;
        result.errors.push('验证过程中发生错误: ' + error.message);
        return result;
    }
};

/**
 * @function DataSchemas.prototype.validateField
 * @description 验证单个字段
 * @param {*} value - 字段值
 * @param {Object} fieldSchema - 字段结构定义
 * @param {string} fieldName - 字段名称
 * @returns {Object} 验证结果
 */
DataSchemas.prototype.validateField = function(value, fieldSchema, fieldName) {
    const result = {
        isValid: true,
        errors: [],
        warnings: []
    };

    // 检查可选字段
    if (fieldSchema.optional && (value === undefined || value === null)) {
        return result;
    }

    // 检查数据类型
    if (fieldSchema.type) {
        const expectedType = fieldSchema.type;
        const actualType = Array.isArray(value) ? 'array' : typeof value;

        if (actualType !== expectedType) {
            result.isValid = false;
            result.errors.push(`字段 ${fieldName} 类型错误，期望 ${expectedType}，实际 ${actualType}`);
            return result;
        }
    }

    // 字符串类型验证
    if (fieldSchema.type === 'string' && typeof value === 'string') {
        if (fieldSchema.minLength && value.length < fieldSchema.minLength) {
            result.isValid = false;
            result.errors.push(`字段 ${fieldName} 长度不足，最小长度 ${fieldSchema.minLength}`);
        }
        if (fieldSchema.maxLength && value.length > fieldSchema.maxLength) {
            result.isValid = false;
            result.errors.push(`字段 ${fieldName} 长度超限，最大长度 ${fieldSchema.maxLength}`);
        }
        if (fieldSchema.pattern && !fieldSchema.pattern.test(value)) {
            result.isValid = false;
            result.errors.push(`字段 ${fieldName} 格式不正确`);
        }
        if (fieldSchema.enum && fieldSchema.enum.indexOf(value) === -1) {
            result.isValid = false;
            result.errors.push(`字段 ${fieldName} 值不在允许范围内: ${fieldSchema.enum.join(', ')}`);
        }
    }

    // 数值类型验证
    if (fieldSchema.type === 'number' && typeof value === 'number') {
        if (fieldSchema.minimum !== undefined && value < fieldSchema.minimum) {
            result.isValid = false;
            result.errors.push(`字段 ${fieldName} 值过小，最小值 ${fieldSchema.minimum}`);
        }
        if (fieldSchema.maximum !== undefined && value > fieldSchema.maximum) {
            result.isValid = false;
            result.errors.push(`字段 ${fieldName} 值过大，最大值 ${fieldSchema.maximum}`);
        }
    }

    // 数组类型验证
    if (fieldSchema.type === 'array' && Array.isArray(value)) {
        if (fieldSchema.minItems && value.length < fieldSchema.minItems) {
            result.isValid = false;
            result.errors.push(`字段 ${fieldName} 项目数量不足，最小数量 ${fieldSchema.minItems}`);
        }
        if (fieldSchema.maxItems && value.length > fieldSchema.maxItems) {
            result.isValid = false;
            result.errors.push(`字段 ${fieldName} 项目数量超限，最大数量 ${fieldSchema.maxItems}`);
        }

        // 验证数组项
        if (fieldSchema.items) {
            for (let i = 0; i < value.length; i++) {
                const itemValidation = this.validateField(value[i], fieldSchema.items, `${fieldName}[${i}]`);
                if (!itemValidation.isValid) {
                    result.isValid = false;
                    result.errors = result.errors.concat(itemValidation.errors);
                }
                result.warnings = result.warnings.concat(itemValidation.warnings);
            }
        }
    }

    return result;
};

/**
 * @function DataSchemas.prototype.migrateData
 * @description 迁移数据到最新版本
 * @param {*} data - 要迁移的数据
 * @param {string} schemaName - 数据结构名称
 * @param {string} fromVersion - 源版本
 * @returns {Object} 迁移结果
 */
DataSchemas.prototype.migrateData = function(data, schemaName, fromVersion) {
    const result = {
        success: true,
        migratedData: data,
        changes: [],
        errors: []
    };

    try {
        // 查找适用的迁移规则
        const migrationKey = this.findMigrationRule(fromVersion, this.currentVersion);
        if (!migrationKey) {
            result.changes.push('无需迁移，版本已是最新');
            return result;
        }

        const migrationRule = this.migrationRules[migrationKey];
        if (!migrationRule || !migrationRule[schemaName]) {
            result.changes.push('未找到适用的迁移规则');
            return result;
        }

        const schemaMigration = migrationRule[schemaName];
        let migratedData = JSON.parse(JSON.stringify(data)); // 深拷贝

        // 添加新字段
        if (schemaMigration.addFields) {
            for (const fieldName in schemaMigration.addFields) {
                if (!migratedData.hasOwnProperty(fieldName)) {
                    migratedData[fieldName] = schemaMigration.addFields[fieldName];
                    result.changes.push('添加字段: ' + fieldName);
                }
            }
        }

        // 重命名字段
        if (schemaMigration.renameFields) {
            for (const oldName in schemaMigration.renameFields) {
                const newName = schemaMigration.renameFields[oldName];
                if (migratedData.hasOwnProperty(oldName)) {
                    migratedData[newName] = migratedData[oldName];
                    delete migratedData[oldName];
                    result.changes.push(`重命名字段: ${oldName} -> ${newName}`);
                }
            }
        }

        // 删除废弃字段
        if (schemaMigration.removeFields) {
            for (let i = 0; i < schemaMigration.removeFields.length; i++) {
                const fieldName = schemaMigration.removeFields[i];
                if (migratedData.hasOwnProperty(fieldName)) {
                    delete migratedData[fieldName];
                    result.changes.push('删除字段: ' + fieldName);
                }
            }
        }

        // 执行自定义转换
        if (schemaMigration.transform && typeof schemaMigration.transform === 'function') {
            migratedData = schemaMigration.transform(migratedData);
            result.changes.push('执行自定义数据转换');
        }

        result.migratedData = migratedData;
        return result;

    } catch (error) {
        result.success = false;
        result.errors.push('数据迁移失败: ' + error.message);
        return result;
    }
};

/**
 * @function DataSchemas.prototype.findMigrationRule
 * @description 查找适用的迁移规则
 * @param {string} fromVersion - 源版本
 * @param {string} toVersion - 目标版本
 * @returns {string|null} 迁移规则键名
 */
DataSchemas.prototype.findMigrationRule = function(fromVersion, toVersion) {
    // 简化的版本匹配逻辑
    if (fromVersion === toVersion) {
        return null; // 无需迁移
    }

    // 检查是否有从0.9.x到1.0.0的迁移规则
    if (fromVersion.startsWith('0.9') && toVersion.startsWith('1.0')) {
        return '0.9.x_to_1.0.0';
    }

    return null;
};

/**
 * @function DataSchemas.prototype.getSchema
 * @description 获取指定的数据结构定义
 * @param {string} schemaName - 数据结构名称
 * @returns {Object|null} 数据结构定义
 */
DataSchemas.prototype.getSchema = function(schemaName) {
    return this.schemas[schemaName] || null;
};

/**
 * @function DataSchemas.prototype.getAllSchemas
 * @description 获取所有数据结构定义
 * @returns {Object} 所有数据结构定义
 */
DataSchemas.prototype.getAllSchemas = function() {
    return this.schemas;
};

/**
 * @function DataSchemas.prototype.getSchemaNames
 * @description 获取所有数据结构名称
 * @returns {Array} 数据结构名称列表
 */
DataSchemas.prototype.getSchemaNames = function() {
    return Object.keys(this.schemas);
};

// 创建全局数据结构定义管理器实例
SmartOffice.Data.Schemas = new DataSchemas();

SmartOffice.log('info', 'SmartOffice数据结构定义模块初始化完成');
