/**
 * @file SmartOffice字段映射管理器 - 重构版本
 * @description 提供通用字段映射管理功能，包括文件上传、表头识别、映射关系建立、模板管理等
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2025-01-03 重构为通用字段映射管理模块
 */

/**
 * @function FieldMappingManager
 * @description 字段映射管理器构造函数 - 通用字段映射管理模块
 * @constructor
 */
function FieldMappingManager() {
    /**
     * @property {Object} storage - 存储管理器引用
     */
    this.storage = SmartOffice.Core.Storage;

    /**
     * @property {Object} helpers - 工具函数引用
     */
    this.helpers = SmartOffice.Utils.Helpers;

    /**
     * @property {Object} eventBus - 事件总线引用
     */
    this.eventBus = SmartOffice.Core.EventBus;

    /**
     * @property {string} mappingTemplatesKey - 映射模板存储键名
     */
    this.mappingTemplatesKey = 'field_mapping_templates';

    /**
     * @property {string} mappingHistoryKey - 映射历史记录存储键名
     */
    this.mappingHistoryKey = 'field_mapping_history';

    /**
     * @property {string} currentMappingKey - 当前映射关系存储键名
     */
    this.currentMappingKey = 'current_field_mapping';

    /**
     * @property {Array} standardFields - 系统预定义标准字段库
     */
    this.standardFields = [
        // 基础数值字段
        { name: 'amount', label: '金额', type: 'number', category: 'financial', keywords: ['金额', '总额', '价格', 'amount', 'price', 'cost', 'total', '费用', '成本'] },
        { name: 'quantity', label: '数量', type: 'number', category: 'quantity', keywords: ['数量', '个数', 'quantity', 'qty', 'count', 'num', '件数', '数目'] },
        { name: 'revenue', label: '收入', type: 'number', category: 'financial', keywords: ['收入', '营收', 'revenue', 'income', 'sales', '销售额', '营业额'] },
        { name: 'profit', label: '利润', type: 'number', category: 'financial', keywords: ['利润', '盈利', 'profit', 'margin', 'earnings', '毛利', '净利'] },
        { name: 'discount', label: '折扣', type: 'number', category: 'financial', keywords: ['折扣', '优惠', 'discount', '减免', '让利'] },

        // 日期时间字段
        { name: 'date', label: '日期', type: 'date', category: 'temporal', keywords: ['日期', '时间', 'date', 'time', 'created', 'updated', '时刻'] },
        { name: 'created_date', label: '创建日期', type: 'date', category: 'temporal', keywords: ['创建日期', '创建时间', 'created', 'create_date', 'created_at', '建立时间'] },
        { name: 'modified_date', label: '修改日期', type: 'date', category: 'temporal', keywords: ['修改日期', '更新时间', 'modified', 'updated', 'update_date', '变更时间'] },
        { name: 'order_date', label: '订单日期', type: 'date', category: 'temporal', keywords: ['订单日期', '下单时间', 'order_date', '购买日期', '交易时间'] },

        // 分类字段
        { name: 'category', label: '类别', type: 'string', category: 'classification', keywords: ['类别', '分类', 'category', 'type', 'class', 'group', '种类'] },
        { name: 'region', label: '区域', type: 'string', category: 'location', keywords: ['区域', '地区', 'region', 'area', 'location', 'zone', '省份', '城市'] },
        { name: 'product', label: '产品', type: 'string', category: 'product', keywords: ['产品', '商品', 'product', 'item', 'goods', 'merchandise', '货品'] },
        { name: 'customer', label: '客户', type: 'string', category: 'entity', keywords: ['客户', '用户', 'customer', 'client', 'user', 'buyer', '买家'] },
        { name: 'supplier', label: '供应商', type: 'string', category: 'entity', keywords: ['供应商', '厂商', 'supplier', 'vendor', 'provider', '提供商'] },
        { name: 'employee', label: '员工', type: 'string', category: 'entity', keywords: ['员工', '职员', 'employee', 'staff', 'worker', '人员'] },

        // 状态字段
        { name: 'status', label: '状态', type: 'string', category: 'status', keywords: ['状态', '情况', 'status', 'state', 'condition', '阶段'] },
        { name: 'priority', label: '优先级', type: 'string', category: 'priority', keywords: ['优先级', '重要性', 'priority', 'importance', 'level', '等级'] },

        // 标识字段
        { name: 'id', label: 'ID', type: 'string', category: 'identifier', keywords: ['id', 'identifier', '编号', '标识', 'code', 'number', '序号'] },
        { name: 'name', label: '名称', type: 'string', category: 'identifier', keywords: ['名称', '姓名', 'name', 'title', 'label', '标题'] },
        { name: 'description', label: '描述', type: 'string', category: 'content', keywords: ['描述', '说明', 'description', 'note', 'remark', '备注'] }
    ];

    /**
     * @property {Object} currentFileData - 当前上传文件的数据
     */
    this.currentFileData = null;

    /**
     * @property {Array} currentSourceFields - 当前文件的源字段列表
     */
    this.currentSourceFields = [];

    /**
     * @property {Array} currentMappings - 当前的映射关系列表
     */
    this.currentMappings = [];

    /**
     * @property {Object} csvParser - CSV解析器引用
     */
    this.csvParser = null;

    /**
     * @property {Object} excelParser - Excel解析器引用
     */
    this.excelParser = null;

    SmartOffice.log('info', 'FieldMappingManager字段映射管理器初始化完成');
}

/**
 * @function FieldMappingManager.prototype.init
 * @description 初始化字段映射管理器
 */
FieldMappingManager.prototype.init = function() {
    try {
        // 初始化解析器引用
        this.csvParser = SmartOffice.Data.CSVParser;
        this.excelParser = SmartOffice.Parsers ? SmartOffice.Parsers.ExcelParser : null;

        // 加载当前映射关系
        this.loadCurrentMapping();

        SmartOffice.log('info', '字段映射管理器初始化成功');
    } catch (error) {
        SmartOffice.log('error', '字段映射管理器初始化失败:', error);
    }
};

/**
 * @function FieldMappingManager.prototype.uploadAndParseFile
 * @description 上传并解析文件，自动识别表头字段
 * @param {File} file - 上传的文件
 * @returns {Promise<Object>} 解析结果包含字段信息和数据预览
 */
FieldMappingManager.prototype.uploadAndParseFile = function(file) {
    const self = this;

    return new Promise(function(resolve, reject) {
        try {
            if (!file) {
                throw new Error('文件不能为空');
            }

            // 检查文件类型
            const fileType = self.detectFileType(file);
            if (!fileType) {
                throw new Error('不支持的文件格式，请上传CSV或Excel文件');
            }

            SmartOffice.log('info', '开始解析文件:', file.name, '类型:', fileType);

            // 根据文件类型选择解析器
            if (fileType === 'csv') {
                self.parseCSVFile(file).then(resolve).catch(reject);
            } else if (fileType === 'excel') {
                self.parseExcelFile(file).then(resolve).catch(reject);
            } else {
                reject(new Error('未知文件类型'));
            }
        } catch (error) {
            SmartOffice.log('error', '文件解析失败:', error);
            reject(error);
        }
    });
};

/**
 * @function FieldMappingManager.prototype.detectFileType
 * @description 检测文件类型
 * @param {File} file - 文件对象
 * @returns {string|null} 文件类型 ('csv', 'excel') 或 null
 */
FieldMappingManager.prototype.detectFileType = function(file) {
    if (!file || !file.name) {
        return null;
    }

    const fileName = file.name.toLowerCase();
    const extension = fileName.split('.').pop();

    if (extension === 'csv') {
        return 'csv';
    } else if (extension === 'xlsx' || extension === 'xls') {
        return 'excel';
    }

    // 检查MIME类型
    if (file.type) {
        if (file.type.includes('csv')) {
            return 'csv';
        } else if (file.type.includes('spreadsheet') || file.type.includes('excel')) {
            return 'excel';
        }
    }

    return null;
};

/**
 * @function FieldMappingManager.prototype.createMapping
 * @description 创建字段映射关系
 * @param {string} sourceField - 源字段名
 * @param {string} targetField - 目标标准字段名
 * @param {string} dataType - 数据类型
 * @param {Object} options - 可选配置
 * @returns {Object} 映射配置对象
 */
FieldMappingManager.prototype.createMapping = function(sourceField, targetField, dataType, options) {
    options = options || {};

    const mapping = {
        id: this.helpers.generateId('mapping'),
        sourceField: sourceField,
        targetField: targetField,
        dataType: dataType || 'string',
        confidence: this.calculateMappingConfidence(sourceField, targetField),
        required: options.required || false,
        transform: options.transform || null,
        validation: options.validation || null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };

    SmartOffice.log('info', '创建字段映射:', sourceField, '->', targetField);
    return mapping;
};

/**
 * @function FieldMappingManager.prototype.parseCSVFile
 * @description 解析CSV文件
 * @param {File} file - CSV文件
 * @returns {Promise<Object>} 解析结果
 */
FieldMappingManager.prototype.parseCSVFile = function(file) {
    const self = this;

    return new Promise(function(resolve, reject) {
        try {
            if (!self.csvParser) {
                throw new Error('CSV解析器未初始化');
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const csvContent = e.target.result;
                    const parseResult = self.csvParser.parse(csvContent, {
                        hasHeader: true,
                        detectTypes: true,
                        maxRows: 100 // 只解析前100行用于预览
                    });

                    if (!parseResult.success) {
                        throw new Error(parseResult.error || 'CSV解析失败');
                    }

                    // 提取字段信息
                    const sourceFields = self.extractFieldsFromData(parseResult.data, parseResult.headers);

                    // 保存当前文件数据
                    self.currentFileData = {
                        fileName: file.name,
                        fileType: 'csv',
                        headers: parseResult.headers,
                        data: parseResult.data,
                        totalRows: parseResult.totalRows || parseResult.data.length
                    };

                    self.currentSourceFields = sourceFields;

                    // 生成智能映射建议
                    const suggestions = self.autoSuggestMapping(sourceFields);

                    const result = {
                        success: true,
                        fileName: file.name,
                        fileType: 'csv',
                        sourceFields: sourceFields,
                        suggestions: suggestions,
                        preview: {
                            headers: parseResult.headers,
                            data: parseResult.data.slice(0, 10), // 前10行预览
                            totalRows: parseResult.totalRows || parseResult.data.length
                        }
                    };

                    SmartOffice.log('info', 'CSV文件解析成功:', file.name, '字段数:', sourceFields.length);
                    resolve(result);
                } catch (error) {
                    SmartOffice.log('error', 'CSV文件解析失败:', error);
                    reject(error);
                }
            };

            reader.onerror = function() {
                reject(new Error('文件读取失败'));
            };

            reader.readAsText(file, 'UTF-8');
        } catch (error) {
            reject(error);
        }
    });
};

/**
 * @function FieldMappingManager.prototype.parseExcelFile
 * @description 解析Excel文件
 * @param {File} file - Excel文件
 * @returns {Promise<Object>} 解析结果
 */
FieldMappingManager.prototype.parseExcelFile = function(file) {
    const self = this;

    return new Promise(function(resolve, reject) {
        try {
            if (!self.excelParser) {
                // 如果Excel解析器不可用，提示用户转换为CSV
                reject(new Error('Excel解析器未加载，请将文件转换为CSV格式后重试'));
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const arrayBuffer = e.target.result;
                    const parseResult = self.excelParser.parse(arrayBuffer, {
                        hasHeader: true,
                        detectTypes: true,
                        maxRows: 100 // 只解析前100行用于预览
                    });

                    if (!parseResult.success) {
                        throw new Error(parseResult.error || 'Excel解析失败');
                    }

                    // 提取字段信息
                    const sourceFields = self.extractFieldsFromData(parseResult.data, parseResult.headers);

                    // 保存当前文件数据
                    self.currentFileData = {
                        fileName: file.name,
                        fileType: 'excel',
                        headers: parseResult.headers,
                        data: parseResult.data,
                        totalRows: parseResult.totalRows || parseResult.data.length
                    };

                    self.currentSourceFields = sourceFields;

                    // 生成智能映射建议
                    const suggestions = self.autoSuggestMapping(sourceFields);

                    const result = {
                        success: true,
                        fileName: file.name,
                        fileType: 'excel',
                        sourceFields: sourceFields,
                        suggestions: suggestions,
                        preview: {
                            headers: parseResult.headers,
                            data: parseResult.data.slice(0, 10), // 前10行预览
                            totalRows: parseResult.totalRows || parseResult.data.length
                        }
                    };

                    SmartOffice.log('info', 'Excel文件解析成功:', file.name, '字段数:', sourceFields.length);
                    resolve(result);
                } catch (error) {
                    SmartOffice.log('error', 'Excel文件解析失败:', error);
                    reject(error);
                }
            };

            reader.onerror = function() {
                reject(new Error('文件读取失败'));
            };

            reader.readAsArrayBuffer(file);
        } catch (error) {
            reject(error);
        }
    });
};

/**
 * @function FieldMappingManager.prototype.extractFieldsFromData
 * @description 从解析的数据中提取字段信息
 * @param {Array} data - 解析的数据行
 * @param {Array} headers - 表头列表
 * @returns {Array} 字段信息列表
 */
FieldMappingManager.prototype.extractFieldsFromData = function(data, headers) {
    const fields = [];

    if (!headers || headers.length === 0) {
        return fields;
    }

    for (let i = 0; i < headers.length; i++) {
        const header = headers[i];
        if (!header || header.trim() === '') {
            continue;
        }

        // 分析字段数据类型
        const fieldType = this.detectFieldType(data, i, header);

        // 获取字段统计信息
        const stats = this.getFieldStatistics(data, i);

        const fieldInfo = {
            name: header.trim(),
            originalName: header,
            type: fieldType,
            index: i,
            statistics: stats,
            sampleValues: this.getSampleValues(data, i, 5),
            isEmpty: stats.emptyCount === data.length,
            isUnique: stats.uniqueCount === data.length
        };

        fields.push(fieldInfo);
    }

    SmartOffice.log('info', '提取字段信息完成，共', fields.length, '个字段');
    return fields;
};

/**
 * @function FieldMappingManager.prototype.detectFieldType
 * @description 检测字段数据类型
 * @param {Array} data - 数据行
 * @param {number} columnIndex - 列索引
 * @param {string} fieldName - 字段名
 * @returns {string} 数据类型
 */
FieldMappingManager.prototype.detectFieldType = function(data, columnIndex, fieldName) {
    if (!data || data.length === 0) {
        return 'string';
    }

    let numberCount = 0;
    let dateCount = 0;
    let validValueCount = 0;

    // 检查前50行数据来判断类型
    const sampleSize = Math.min(data.length, 50);

    for (let i = 0; i < sampleSize; i++) {
        const row = data[i];
        if (!row || columnIndex >= row.length) {
            continue;
        }

        const value = row[columnIndex];
        if (value === null || value === undefined || value === '') {
            continue;
        }

        validValueCount++;
        const strValue = String(value).trim();

        // 检查是否为数字
        if (!isNaN(strValue) && !isNaN(parseFloat(strValue))) {
            numberCount++;
        }

        // 检查是否为日期
        if (this.isDateValue(strValue)) {
            dateCount++;
        }
    }

    if (validValueCount === 0) {
        return 'string';
    }

    // 如果80%以上是数字，认为是数字类型
    if (numberCount / validValueCount >= 0.8) {
        return 'number';
    }

    // 如果60%以上是日期，认为是日期类型
    if (dateCount / validValueCount >= 0.6) {
        return 'date';
    }

    // 默认为字符串类型
    return 'string';
};

/**
 * @function FieldMappingManager.prototype.isDateValue
 * @description 判断值是否为日期
 * @param {string} value - 值
 * @returns {boolean} 是否为日期
 */
FieldMappingManager.prototype.isDateValue = function(value) {
    if (!value || typeof value !== 'string') {
        return false;
    }

    // 常见日期格式正则表达式
    const datePatterns = [
        /^\d{4}-\d{1,2}-\d{1,2}$/,           // YYYY-MM-DD
        /^\d{4}\/\d{1,2}\/\d{1,2}$/,         // YYYY/MM/DD
        /^\d{1,2}\/\d{1,2}\/\d{4}$/,         // MM/DD/YYYY
        /^\d{1,2}-\d{1,2}-\d{4}$/,           // MM-DD-YYYY
        /^\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}:\d{1,2}/, // YYYY-MM-DD HH:MM
        /^\d{1,2}\/\d{1,2}\/\d{4}\s+\d{1,2}:\d{1,2}/ // MM/DD/YYYY HH:MM
    ];

    for (let i = 0; i < datePatterns.length; i++) {
        if (datePatterns[i].test(value)) {
            // 进一步验证是否为有效日期
            const date = new Date(value);
            return !isNaN(date.getTime());
        }
    }

    return false;
};

/**
 * @function FieldMappingManager.prototype.getFieldStatistics
 * @description 获取字段统计信息
 * @param {Array} data - 数据行
 * @param {number} columnIndex - 列索引
 * @returns {Object} 统计信息
 */
FieldMappingManager.prototype.getFieldStatistics = function(data, columnIndex) {
    const stats = {
        totalCount: data.length,
        emptyCount: 0,
        uniqueCount: 0,
        minLength: Infinity,
        maxLength: 0,
        avgLength: 0
    };

    const uniqueValues = new Set();
    let totalLength = 0;

    for (let i = 0; i < data.length; i++) {
        const row = data[i];
        if (!row || columnIndex >= row.length) {
            stats.emptyCount++;
            continue;
        }

        const value = row[columnIndex];
        if (value === null || value === undefined || value === '') {
            stats.emptyCount++;
            continue;
        }

        const strValue = String(value);
        uniqueValues.add(strValue);

        const length = strValue.length;
        totalLength += length;
        stats.minLength = Math.min(stats.minLength, length);
        stats.maxLength = Math.max(stats.maxLength, length);
    }

    stats.uniqueCount = uniqueValues.size;
    stats.avgLength = stats.totalCount > stats.emptyCount ?
        Math.round(totalLength / (stats.totalCount - stats.emptyCount)) : 0;

    if (stats.minLength === Infinity) {
        stats.minLength = 0;
    }

    return stats;
};

/**
 * @function FieldMappingManager.prototype.getSampleValues
 * @description 获取字段样本值
 * @param {Array} data - 数据行
 * @param {number} columnIndex - 列索引
 * @param {number} count - 样本数量
 * @returns {Array} 样本值列表
 */
FieldMappingManager.prototype.getSampleValues = function(data, columnIndex, count) {
    const samples = [];
    const seen = new Set();

    for (let i = 0; i < data.length && samples.length < count; i++) {
        const row = data[i];
        if (!row || columnIndex >= row.length) {
            continue;
        }

        const value = row[columnIndex];
        if (value === null || value === undefined || value === '') {
            continue;
        }

        const strValue = String(value);
        if (!seen.has(strValue)) {
            seen.add(strValue);
            samples.push(strValue);
        }
    }

    return samples;
};

/**
 * @function FieldMappingManager.prototype.calculateMappingConfidence
 * @description 计算映射置信度
 * @param {string} sourceField - 源字段名
 * @param {string} targetField - 目标字段名
 * @returns {number} 置信度 (0-1)
 */
FieldMappingManager.prototype.calculateMappingConfidence = function(sourceField, targetField) {
    if (!sourceField || !targetField) {
        return 0;
    }

    // 查找目标字段的标准定义
    const standardField = this.standardFields.find(field => field.name === targetField);
    if (!standardField) {
        return 0.1; // 未知目标字段，低置信度
    }

    const sourceFieldLower = sourceField.toLowerCase();
    let maxSimilarity = 0;

    // 计算与关键词的相似度
    for (let i = 0; i < standardField.keywords.length; i++) {
        const keyword = standardField.keywords[i].toLowerCase();
        const similarity = this.calculateStringSimilarity(sourceFieldLower, keyword);
        maxSimilarity = Math.max(maxSimilarity, similarity);
    }

    // 额外的相似度计算
    const directSimilarity = this.calculateStringSimilarity(sourceFieldLower, standardField.name);
    const labelSimilarity = this.calculateStringSimilarity(sourceFieldLower, standardField.label);

    // 综合置信度计算
    const confidence = Math.max(maxSimilarity, directSimilarity, labelSimilarity);
    
    return Math.round(confidence * 100) / 100;
};

/**
 * @function FieldMappingManager.prototype.calculateStringSimilarity
 * @description 计算字符串相似度（使用编辑距离算法）
 * @param {string} str1 - 字符串1
 * @param {string} str2 - 字符串2
 * @returns {number} 相似度 (0-1)
 */
FieldMappingManager.prototype.calculateStringSimilarity = function(str1, str2) {
    if (!str1 || !str2) return 0;
    if (str1 === str2) return 1;

    // 包含关系检查
    if (str1.includes(str2) || str2.includes(str1)) {
        return 0.8;
    }

    // 编辑距离算法
    const len1 = str1.length;
    const len2 = str2.length;
    const matrix = [];

    // 初始化矩阵
    for (let i = 0; i <= len1; i++) {
        matrix[i] = [i];
    }
    for (let j = 0; j <= len2; j++) {
        matrix[0][j] = j;
    }

    // 计算编辑距离
    for (let i = 1; i <= len1; i++) {
        for (let j = 1; j <= len2; j++) {
            const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
            matrix[i][j] = Math.min(
                matrix[i - 1][j] + 1,      // 删除
                matrix[i][j - 1] + 1,      // 插入
                matrix[i - 1][j - 1] + cost // 替换
            );
        }
    }

    const editDistance = matrix[len1][len2];
    const maxLength = Math.max(len1, len2);
    
    return maxLength === 0 ? 1 : (maxLength - editDistance) / maxLength;
};

/**
 * @function FieldMappingManager.prototype.autoSuggestMapping
 * @description 智能映射建议
 * @param {Array} sourceFields - 源文件字段列表
 * @param {Object} options - 可选配置
 * @returns {Array} 建议的映射配置列表
 */
FieldMappingManager.prototype.autoSuggestMapping = function(sourceFields, options) {
    options = options || {};
    const minConfidence = options.minConfidence || 0.6;
    const maxSuggestions = options.maxSuggestions || 10;
    
    const suggestions = [];
    
    for (let i = 0; i < sourceFields.length; i++) {
        const sourceField = sourceFields[i];
        const fieldName = typeof sourceField === 'string' ? sourceField : sourceField.name;
        
        // 为每个源字段找到最佳匹配
        const bestMatch = this.findBestMatch(fieldName);
        
        if (bestMatch && bestMatch.confidence >= minConfidence) {
            suggestions.push({
                sourceField: fieldName,
                targetField: bestMatch.targetField,
                confidence: bestMatch.confidence,
                dataType: bestMatch.dataType,
                category: bestMatch.category,
                reason: bestMatch.reason
            });
        }
    }

    // 按置信度排序并限制数量
    suggestions.sort((a, b) => b.confidence - a.confidence);
    const limitedSuggestions = suggestions.slice(0, maxSuggestions);

    SmartOffice.log('info', '生成智能映射建议:', limitedSuggestions.length + '个建议');
    return limitedSuggestions;
};

/**
 * @function FieldMappingManager.prototype.findBestMatch
 * @description 为单个字段找到最佳匹配
 * @param {string} sourceField - 源字段名
 * @returns {Object|null} 最佳匹配结果
 */
FieldMappingManager.prototype.findBestMatch = function(sourceField) {
    if (!sourceField) return null;

    let bestMatch = null;
    let maxConfidence = 0;

    for (let i = 0; i < this.standardFields.length; i++) {
        const standardField = this.standardFields[i];
        const confidence = this.calculateMappingConfidence(sourceField, standardField.name);
        
        if (confidence > maxConfidence) {
            maxConfidence = confidence;
            bestMatch = {
                targetField: standardField.name,
                confidence: confidence,
                dataType: standardField.type,
                category: standardField.category,
                reason: this.generateMatchReason(sourceField, standardField, confidence)
            };
        }
    }

    return bestMatch;
};

/**
 * @function FieldMappingManager.prototype.generateMatchReason
 * @description 生成匹配原因说明
 * @param {string} sourceField - 源字段名
 * @param {Object} standardField - 标准字段定义
 * @param {number} confidence - 置信度
 * @returns {string} 匹配原因
 */
FieldMappingManager.prototype.generateMatchReason = function(sourceField, standardField, confidence) {
    if (confidence >= 0.9) {
        return '字段名称完全匹配';
    } else if (confidence >= 0.8) {
        return '字段名称高度相似';
    } else if (confidence >= 0.7) {
        return '包含相关关键词';
    } else if (confidence >= 0.6) {
        return '字段含义相近';
    } else {
        return '可能相关';
    }
};

/**
 * @function FieldMappingManager.prototype.saveMappingTemplate
 * @description 保存映射模板
 * @param {Object} template - 映射模板配置
 * @returns {boolean} 是否保存成功
 */
FieldMappingManager.prototype.saveMappingTemplate = function(template) {
    try {
        if (!template || !template.name) {
            throw new Error('映射模板配置无效');
        }

        const templates = this.getAllMappingTemplates();

        // 生成ID（如果没有）
        if (!template.id) {
            template.id = this.helpers.generateId('mapping');
        }

        // 设置时间戳
        const now = new Date().toISOString();
        if (!template.createdAt) {
            template.createdAt = now;
        }
        template.updatedAt = now;

        // 检查是否为更新操作
        const existingIndex = templates.findIndex(function(t) { return t.id === template.id; });

        if (existingIndex >= 0) {
            // 更新现有模板
            templates[existingIndex] = template;
            SmartOffice.log('info', '映射模板更新成功: ' + template.name);
        } else {
            // 添加新模板
            templates.push(template);
            SmartOffice.log('info', '映射模板保存成功: ' + template.name);
        }

        // 保存到存储
        this.storage.set(this.mappingTemplatesKey, templates);

        // 触发事件
        this.eventBus.emit('fieldMapping:templateSaved', template);

        return true;
    } catch (error) {
        SmartOffice.log('error', '映射模板保存失败:', error);
        return false;
    }
};

/**
 * @function FieldMappingManager.prototype.getAllMappingTemplates
 * @description 获取所有映射模板
 * @returns {Array} 映射模板列表
 */
FieldMappingManager.prototype.getAllMappingTemplates = function() {
    try {
        const templates = this.storage.get(this.mappingTemplatesKey, []);
        SmartOffice.log('info', '获取映射模板列表成功，共' + templates.length + '个模板');
        return templates;
    } catch (error) {
        SmartOffice.log('error', '获取映射模板列表失败:', error);
        return [];
    }
};

/**
 * @function FieldMappingManager.prototype.getMappingTemplate
 * @description 获取指定映射模板
 * @param {string} templateId - 模板ID
 * @returns {Object|null} 映射模板配置
 */
FieldMappingManager.prototype.getMappingTemplate = function(templateId) {
    try {
        const templates = this.getAllMappingTemplates();
        const template = templates.find(function(t) { return t.id === templateId; });

        if (template) {
            SmartOffice.log('info', '获取映射模板成功: ' + template.name);
            return template;
        } else {
            SmartOffice.log('warn', '未找到映射模板: ' + templateId);
            return null;
        }
    } catch (error) {
        SmartOffice.log('error', '获取映射模板失败:', error);
        return null;
    }
};

/**
 * @function FieldMappingManager.prototype.deleteMappingTemplate
 * @description 删除映射模板
 * @param {string} templateId - 模板ID
 * @returns {boolean} 是否删除成功
 */
FieldMappingManager.prototype.deleteMappingTemplate = function(templateId) {
    try {
        const templates = this.getAllMappingTemplates();
        const templateIndex = templates.findIndex(function(t) { return t.id === templateId; });

        if (templateIndex >= 0) {
            const deletedTemplate = templates.splice(templateIndex, 1)[0];
            this.storage.set(this.mappingTemplatesKey, templates);

            // 触发事件
            this.eventBus.emit('fieldMapping:templateDeleted', deletedTemplate);

            SmartOffice.log('info', '映射模板删除成功: ' + deletedTemplate.name);
            return true;
        } else {
            SmartOffice.log('warn', '未找到要删除的映射模板: ' + templateId);
            return false;
        }
    } catch (error) {
        SmartOffice.log('error', '删除映射模板失败:', error);
        return false;
    }
};

/**
 * @function FieldMappingManager.prototype.applyMappingTemplate
 * @description 应用映射模板到字段数据
 * @param {Array} sourceFields - 源字段列表
 * @param {Object} template - 映射模板
 * @returns {Array} 映射后的字段列表
 */
FieldMappingManager.prototype.applyMappingTemplate = function(sourceFields, template) {
    try {
        if (!template || !template.mappings) {
            SmartOffice.log('warn', '映射模板无效');
            return sourceFields;
        }

        const mappedFields = [];
        const mappingMap = {};

        // 创建映射查找表
        for (let i = 0; i < template.mappings.length; i++) {
            const mapping = template.mappings[i];
            mappingMap[mapping.sourceField] = mapping;
        }

        // 应用映射
        for (let i = 0; i < sourceFields.length; i++) {
            const sourceField = sourceFields[i];
            const fieldName = typeof sourceField === 'string' ? sourceField : sourceField.name;
            const mapping = mappingMap[fieldName];

            if (mapping) {
                // 有映射关系的字段
                mappedFields.push({
                    name: mapping.targetField,
                    label: this.getStandardFieldLabel(mapping.targetField),
                    type: mapping.dataType,
                    originalName: fieldName,
                    mapped: true,
                    mapping: mapping
                });
            } else {
                // 没有映射关系的字段，保持原样
                mappedFields.push({
                    name: fieldName,
                    label: fieldName,
                    type: typeof sourceField === 'object' ? sourceField.type : 'string',
                    originalName: fieldName,
                    mapped: false
                });
            }
        }

        SmartOffice.log('info', '映射模板应用成功: ' + template.name);
        return mappedFields;
    } catch (error) {
        SmartOffice.log('error', '应用映射模板失败:', error);
        return sourceFields;
    }
};

/**
 * @function FieldMappingManager.prototype.getStandardFieldLabel
 * @description 获取标准字段的显示标签
 * @param {string} fieldName - 标准字段名
 * @returns {string} 显示标签
 */
FieldMappingManager.prototype.getStandardFieldLabel = function(fieldName) {
    const standardField = this.standardFields.find(field => field.name === fieldName);
    return standardField ? standardField.label : fieldName;
};

/**
 * @function FieldMappingManager.prototype.getStandardFieldsByCategory
 * @description 按类别获取标准字段
 * @param {string} category - 字段类别
 * @returns {Array} 指定类别的标准字段列表
 */
FieldMappingManager.prototype.getStandardFieldsByCategory = function(category) {
    if (!category) {
        return this.standardFields;
    }

    return this.standardFields.filter(field => field.category === category);
};

/**
 * @function FieldMappingManager.prototype.validateMapping
 * @description 验证字段映射配置
 * @param {Object} mapping - 映射配置
 * @returns {Object} 验证结果
 */
FieldMappingManager.prototype.validateMapping = function(mapping) {
    const result = { isValid: true, errors: [] };

    if (!mapping) {
        result.isValid = false;
        result.errors.push('映射配置不能为空');
        return result;
    }

    if (!mapping.sourceField) {
        result.isValid = false;
        result.errors.push('源字段名不能为空');
    }

    if (!mapping.targetField) {
        result.isValid = false;
        result.errors.push('目标字段名不能为空');
    }

    // 检查目标字段是否为有效的标准字段
    const standardField = this.standardFields.find(field => field.name === mapping.targetField);
    if (!standardField) {
        result.isValid = false;
        result.errors.push('目标字段不是有效的标准字段');
    }

    return result;
};

/**
 * @function FieldMappingManager.prototype.saveCurrentMapping
 * @description 保存当前映射关系
 * @param {Array} mappings - 映射关系列表
 * @returns {boolean} 是否保存成功
 */
FieldMappingManager.prototype.saveCurrentMapping = function(mappings) {
    try {
        this.currentMappings = mappings || [];
        this.storage.set(this.currentMappingKey, {
            mappings: this.currentMappings,
            sourceFields: this.currentSourceFields,
            fileData: this.currentFileData,
            updatedAt: new Date().toISOString()
        });

        // 触发事件
        this.eventBus.emit('fieldMapping:currentMappingSaved', {
            mappings: this.currentMappings,
            sourceFields: this.currentSourceFields
        });

        SmartOffice.log('info', '当前映射关系保存成功，共', this.currentMappings.length, '个映射');
        return true;
    } catch (error) {
        SmartOffice.log('error', '保存当前映射关系失败:', error);
        return false;
    }
};

/**
 * @function FieldMappingManager.prototype.loadCurrentMapping
 * @description 加载当前映射关系
 * @returns {Object|null} 当前映射数据
 */
FieldMappingManager.prototype.loadCurrentMapping = function() {
    try {
        const currentData = this.storage.get(this.currentMappingKey, null);
        if (currentData) {
            this.currentMappings = currentData.mappings || [];
            this.currentSourceFields = currentData.sourceFields || [];
            this.currentFileData = currentData.fileData || null;

            SmartOffice.log('info', '当前映射关系加载成功，共', this.currentMappings.length, '个映射');
            return currentData;
        }
        return null;
    } catch (error) {
        SmartOffice.log('error', '加载当前映射关系失败:', error);
        return null;
    }
};

/**
 * @function FieldMappingManager.prototype.clearCurrentMapping
 * @description 清除当前映射关系
 */
FieldMappingManager.prototype.clearCurrentMapping = function() {
    this.currentMappings = [];
    this.currentSourceFields = [];
    this.currentFileData = null;
    this.storage.remove(this.currentMappingKey);

    // 触发事件
    this.eventBus.emit('fieldMapping:currentMappingCleared');

    SmartOffice.log('info', '当前映射关系已清除');
};

/**
 * @function FieldMappingManager.prototype.addMapping
 * @description 添加映射关系
 * @param {string} sourceField - 源字段名
 * @param {string} targetField - 目标字段名
 * @param {string} dataType - 数据类型
 * @returns {Object} 新增的映射对象
 */
FieldMappingManager.prototype.addMapping = function(sourceField, targetField, dataType) {
    const mapping = this.createMapping(sourceField, targetField, dataType);

    // 检查是否已存在相同的源字段映射
    const existingIndex = this.currentMappings.findIndex(m => m.sourceField === sourceField);
    if (existingIndex >= 0) {
        // 更新现有映射
        this.currentMappings[existingIndex] = mapping;
        SmartOffice.log('info', '更新字段映射:', sourceField, '->', targetField);
    } else {
        // 添加新映射
        this.currentMappings.push(mapping);
        SmartOffice.log('info', '添加字段映射:', sourceField, '->', targetField);
    }

    // 触发事件
    this.eventBus.emit('fieldMapping:mappingAdded', mapping);

    return mapping;
};

/**
 * @function FieldMappingManager.prototype.removeMapping
 * @description 移除映射关系
 * @param {string} sourceField - 源字段名
 * @returns {boolean} 是否移除成功
 */
FieldMappingManager.prototype.removeMapping = function(sourceField) {
    const index = this.currentMappings.findIndex(m => m.sourceField === sourceField);
    if (index >= 0) {
        const removedMapping = this.currentMappings.splice(index, 1)[0];

        // 触发事件
        this.eventBus.emit('fieldMapping:mappingRemoved', removedMapping);

        SmartOffice.log('info', '移除字段映射:', sourceField);
        return true;
    }
    return false;
};

/**
 * @function FieldMappingManager.prototype.getCurrentMappings
 * @description 获取当前映射关系列表
 * @returns {Array} 当前映射关系列表
 */
FieldMappingManager.prototype.getCurrentMappings = function() {
    return this.currentMappings.slice(); // 返回副本
};

/**
 * @function FieldMappingManager.prototype.getMappedFields
 * @description 获取已映射的字段列表（用于透视表配置）
 * @returns {Array} 已映射的字段列表
 */
FieldMappingManager.prototype.getMappedFields = function() {
    return this.currentMappings.map(mapping => ({
        name: mapping.targetField,
        label: this.getStandardFieldLabel(mapping.targetField),
        type: mapping.dataType,
        originalName: mapping.sourceField,
        mapped: true,
        mapping: mapping
    }));
};

// 创建全局字段映射管理器实例
SmartOffice.Data.FieldMappingManager = new FieldMappingManager();

// 保持向后兼容性
SmartOffice.Data.FieldMapper = SmartOffice.Data.FieldMappingManager;

SmartOffice.log('info', 'SmartOffice字段映射管理器模块初始化完成 - 重构版本2.0.0');
