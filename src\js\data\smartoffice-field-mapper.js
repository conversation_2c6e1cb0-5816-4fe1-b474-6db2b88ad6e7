/**
 * @file SmartOffice字段映射管理器
 * @description 提供字段映射的创建、保存、加载和智能建议功能
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function FieldMapper
 * @description 字段映射管理器构造函数
 * @constructor
 */
function FieldMapper() {
    /**
     * @property {Object} storage - 存储管理器引用
     */
    this.storage = SmartOffice.Core.Storage;

    /**
     * @property {Object} helpers - 工具函数引用
     */
    this.helpers = SmartOffice.Utils.Helpers;

    /**
     * @property {Object} eventBus - 事件总线引用
     */
    this.eventBus = SmartOffice.Core.EventBus;

    /**
     * @property {string} storageKey - 映射模板存储键名
     */
    this.storageKey = 'field_mapping_templates';

    /**
     * @property {Array} standardFields - 系统预定义标准字段
     */
    this.standardFields = [
        // 基础数值字段
        { name: 'amount', label: '金额', type: 'number', category: 'financial', keywords: ['金额', '总额', '价格', 'amount', 'price', 'cost', 'total'] },
        { name: 'quantity', label: '数量', type: 'number', category: 'quantity', keywords: ['数量', '个数', 'quantity', 'qty', 'count', 'num'] },
        { name: 'revenue', label: '收入', type: 'number', category: 'financial', keywords: ['收入', '营收', 'revenue', 'income', 'sales'] },
        { name: 'profit', label: '利润', type: 'number', category: 'financial', keywords: ['利润', '盈利', 'profit', 'margin', 'earnings'] },
        
        // 日期时间字段
        { name: 'date', label: '日期', type: 'date', category: 'temporal', keywords: ['日期', '时间', 'date', 'time', 'created', 'updated'] },
        { name: 'created_date', label: '创建日期', type: 'date', category: 'temporal', keywords: ['创建日期', '创建时间', 'created', 'create_date', 'created_at'] },
        { name: 'modified_date', label: '修改日期', type: 'date', category: 'temporal', keywords: ['修改日期', '更新时间', 'modified', 'updated', 'update_date'] },
        
        // 分类字段
        { name: 'category', label: '类别', type: 'string', category: 'classification', keywords: ['类别', '分类', 'category', 'type', 'class', 'group'] },
        { name: 'region', label: '区域', type: 'string', category: 'location', keywords: ['区域', '地区', 'region', 'area', 'location', 'zone'] },
        { name: 'product', label: '产品', type: 'string', category: 'product', keywords: ['产品', '商品', 'product', 'item', 'goods', 'merchandise'] },
        { name: 'customer', label: '客户', type: 'string', category: 'entity', keywords: ['客户', '用户', 'customer', 'client', 'user', 'buyer'] },
        { name: 'supplier', label: '供应商', type: 'string', category: 'entity', keywords: ['供应商', '厂商', 'supplier', 'vendor', 'provider'] },
        
        // 状态字段
        { name: 'status', label: '状态', type: 'string', category: 'status', keywords: ['状态', '情况', 'status', 'state', 'condition'] },
        { name: 'priority', label: '优先级', type: 'string', category: 'priority', keywords: ['优先级', '重要性', 'priority', 'importance', 'level'] },
        
        // 标识字段
        { name: 'id', label: 'ID', type: 'string', category: 'identifier', keywords: ['id', 'identifier', '编号', '标识', 'code', 'number'] },
        { name: 'name', label: '名称', type: 'string', category: 'identifier', keywords: ['名称', '姓名', 'name', 'title', 'label'] }
    ];

    SmartOffice.log('info', 'FieldMapper字段映射管理器初始化完成');
}

/**
 * @function FieldMapper.prototype.createMapping
 * @description 创建字段映射
 * @param {string} sourceField - 源字段名
 * @param {string} targetField - 目标标准字段名
 * @param {string} dataType - 数据类型
 * @param {Object} options - 可选配置
 * @returns {Object} 映射配置对象
 */
FieldMapper.prototype.createMapping = function(sourceField, targetField, dataType, options) {
    options = options || {};
    
    const mapping = {
        sourceField: sourceField,
        targetField: targetField,
        dataType: dataType || 'string',
        confidence: this.calculateMappingConfidence(sourceField, targetField),
        required: options.required || false,
        transform: options.transform || null,
        validation: options.validation || null,
        createdAt: new Date().toISOString()
    };

    SmartOffice.log('info', '创建字段映射:', sourceField, '->', targetField);
    return mapping;
};

/**
 * @function FieldMapper.prototype.calculateMappingConfidence
 * @description 计算映射置信度
 * @param {string} sourceField - 源字段名
 * @param {string} targetField - 目标字段名
 * @returns {number} 置信度 (0-1)
 */
FieldMapper.prototype.calculateMappingConfidence = function(sourceField, targetField) {
    if (!sourceField || !targetField) {
        return 0;
    }

    // 查找目标字段的标准定义
    const standardField = this.standardFields.find(field => field.name === targetField);
    if (!standardField) {
        return 0.1; // 未知目标字段，低置信度
    }

    const sourceFieldLower = sourceField.toLowerCase();
    let maxSimilarity = 0;

    // 计算与关键词的相似度
    for (let i = 0; i < standardField.keywords.length; i++) {
        const keyword = standardField.keywords[i].toLowerCase();
        const similarity = this.calculateStringSimilarity(sourceFieldLower, keyword);
        maxSimilarity = Math.max(maxSimilarity, similarity);
    }

    // 额外的相似度计算
    const directSimilarity = this.calculateStringSimilarity(sourceFieldLower, standardField.name);
    const labelSimilarity = this.calculateStringSimilarity(sourceFieldLower, standardField.label);

    // 综合置信度计算
    const confidence = Math.max(maxSimilarity, directSimilarity, labelSimilarity);
    
    return Math.round(confidence * 100) / 100;
};

/**
 * @function FieldMapper.prototype.calculateStringSimilarity
 * @description 计算字符串相似度（使用编辑距离算法）
 * @param {string} str1 - 字符串1
 * @param {string} str2 - 字符串2
 * @returns {number} 相似度 (0-1)
 */
FieldMapper.prototype.calculateStringSimilarity = function(str1, str2) {
    if (!str1 || !str2) return 0;
    if (str1 === str2) return 1;

    // 包含关系检查
    if (str1.includes(str2) || str2.includes(str1)) {
        return 0.8;
    }

    // 编辑距离算法
    const len1 = str1.length;
    const len2 = str2.length;
    const matrix = [];

    // 初始化矩阵
    for (let i = 0; i <= len1; i++) {
        matrix[i] = [i];
    }
    for (let j = 0; j <= len2; j++) {
        matrix[0][j] = j;
    }

    // 计算编辑距离
    for (let i = 1; i <= len1; i++) {
        for (let j = 1; j <= len2; j++) {
            const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
            matrix[i][j] = Math.min(
                matrix[i - 1][j] + 1,      // 删除
                matrix[i][j - 1] + 1,      // 插入
                matrix[i - 1][j - 1] + cost // 替换
            );
        }
    }

    const editDistance = matrix[len1][len2];
    const maxLength = Math.max(len1, len2);
    
    return maxLength === 0 ? 1 : (maxLength - editDistance) / maxLength;
};

/**
 * @function FieldMapper.prototype.autoSuggestMapping
 * @description 智能映射建议
 * @param {Array} sourceFields - 源文件字段列表
 * @param {Object} options - 可选配置
 * @returns {Array} 建议的映射配置列表
 */
FieldMapper.prototype.autoSuggestMapping = function(sourceFields, options) {
    options = options || {};
    const minConfidence = options.minConfidence || 0.6;
    const maxSuggestions = options.maxSuggestions || 10;
    
    const suggestions = [];
    
    for (let i = 0; i < sourceFields.length; i++) {
        const sourceField = sourceFields[i];
        const fieldName = typeof sourceField === 'string' ? sourceField : sourceField.name;
        
        // 为每个源字段找到最佳匹配
        const bestMatch = this.findBestMatch(fieldName);
        
        if (bestMatch && bestMatch.confidence >= minConfidence) {
            suggestions.push({
                sourceField: fieldName,
                targetField: bestMatch.targetField,
                confidence: bestMatch.confidence,
                dataType: bestMatch.dataType,
                category: bestMatch.category,
                reason: bestMatch.reason
            });
        }
    }

    // 按置信度排序并限制数量
    suggestions.sort((a, b) => b.confidence - a.confidence);
    const limitedSuggestions = suggestions.slice(0, maxSuggestions);

    SmartOffice.log('info', '生成智能映射建议:', limitedSuggestions.length + '个建议');
    return limitedSuggestions;
};

/**
 * @function FieldMapper.prototype.findBestMatch
 * @description 为单个字段找到最佳匹配
 * @param {string} sourceField - 源字段名
 * @returns {Object|null} 最佳匹配结果
 */
FieldMapper.prototype.findBestMatch = function(sourceField) {
    if (!sourceField) return null;

    let bestMatch = null;
    let maxConfidence = 0;

    for (let i = 0; i < this.standardFields.length; i++) {
        const standardField = this.standardFields[i];
        const confidence = this.calculateMappingConfidence(sourceField, standardField.name);
        
        if (confidence > maxConfidence) {
            maxConfidence = confidence;
            bestMatch = {
                targetField: standardField.name,
                confidence: confidence,
                dataType: standardField.type,
                category: standardField.category,
                reason: this.generateMatchReason(sourceField, standardField, confidence)
            };
        }
    }

    return bestMatch;
};

/**
 * @function FieldMapper.prototype.generateMatchReason
 * @description 生成匹配原因说明
 * @param {string} sourceField - 源字段名
 * @param {Object} standardField - 标准字段定义
 * @param {number} confidence - 置信度
 * @returns {string} 匹配原因
 */
FieldMapper.prototype.generateMatchReason = function(sourceField, standardField, confidence) {
    if (confidence >= 0.9) {
        return '字段名称完全匹配';
    } else if (confidence >= 0.8) {
        return '字段名称高度相似';
    } else if (confidence >= 0.7) {
        return '包含相关关键词';
    } else if (confidence >= 0.6) {
        return '字段含义相近';
    } else {
        return '可能相关';
    }
};

/**
 * @function FieldMapper.prototype.saveMappingTemplate
 * @description 保存映射模板
 * @param {Object} template - 映射模板配置
 * @returns {boolean} 是否保存成功
 */
FieldMapper.prototype.saveMappingTemplate = function(template) {
    try {
        if (!template || !template.name) {
            throw new Error('映射模板配置无效');
        }

        const templates = this.getAllMappingTemplates();

        // 生成ID（如果没有）
        if (!template.id) {
            template.id = this.helpers.generateId('mapping');
        }

        // 设置时间戳
        const now = new Date().toISOString();
        if (!template.createdAt) {
            template.createdAt = now;
        }
        template.updatedAt = now;

        // 检查是否为更新操作
        const existingIndex = templates.findIndex(function(t) { return t.id === template.id; });

        if (existingIndex >= 0) {
            // 更新现有模板
            templates[existingIndex] = template;
            SmartOffice.log('info', '映射模板更新成功: ' + template.name);
        } else {
            // 添加新模板
            templates.push(template);
            SmartOffice.log('info', '映射模板保存成功: ' + template.name);
        }

        // 保存到存储
        this.storage.set(this.storageKey, templates);

        // 触发事件
        this.eventBus.emit('fieldMapping:templateSaved', template);

        return true;
    } catch (error) {
        SmartOffice.log('error', '映射模板保存失败:', error);
        return false;
    }
};

/**
 * @function FieldMapper.prototype.getAllMappingTemplates
 * @description 获取所有映射模板
 * @returns {Array} 映射模板列表
 */
FieldMapper.prototype.getAllMappingTemplates = function() {
    try {
        const templates = this.storage.get(this.storageKey, []);
        SmartOffice.log('info', '获取映射模板列表成功，共' + templates.length + '个模板');
        return templates;
    } catch (error) {
        SmartOffice.log('error', '获取映射模板列表失败:', error);
        return [];
    }
};

/**
 * @function FieldMapper.prototype.getMappingTemplate
 * @description 获取指定映射模板
 * @param {string} templateId - 模板ID
 * @returns {Object|null} 映射模板配置
 */
FieldMapper.prototype.getMappingTemplate = function(templateId) {
    try {
        const templates = this.getAllMappingTemplates();
        const template = templates.find(function(t) { return t.id === templateId; });

        if (template) {
            SmartOffice.log('info', '获取映射模板成功: ' + template.name);
            return template;
        } else {
            SmartOffice.log('warn', '未找到映射模板: ' + templateId);
            return null;
        }
    } catch (error) {
        SmartOffice.log('error', '获取映射模板失败:', error);
        return null;
    }
};

/**
 * @function FieldMapper.prototype.deleteMappingTemplate
 * @description 删除映射模板
 * @param {string} templateId - 模板ID
 * @returns {boolean} 是否删除成功
 */
FieldMapper.prototype.deleteMappingTemplate = function(templateId) {
    try {
        const templates = this.getAllMappingTemplates();
        const templateIndex = templates.findIndex(function(t) { return t.id === templateId; });

        if (templateIndex >= 0) {
            const deletedTemplate = templates.splice(templateIndex, 1)[0];
            this.storage.set(this.storageKey, templates);

            // 触发事件
            this.eventBus.emit('fieldMapping:templateDeleted', deletedTemplate);

            SmartOffice.log('info', '映射模板删除成功: ' + deletedTemplate.name);
            return true;
        } else {
            SmartOffice.log('warn', '未找到要删除的映射模板: ' + templateId);
            return false;
        }
    } catch (error) {
        SmartOffice.log('error', '删除映射模板失败:', error);
        return false;
    }
};

/**
 * @function FieldMapper.prototype.applyMappingTemplate
 * @description 应用映射模板到字段数据
 * @param {Array} sourceFields - 源字段列表
 * @param {Object} template - 映射模板
 * @returns {Array} 映射后的字段列表
 */
FieldMapper.prototype.applyMappingTemplate = function(sourceFields, template) {
    try {
        if (!template || !template.mappings) {
            SmartOffice.log('warn', '映射模板无效');
            return sourceFields;
        }

        const mappedFields = [];
        const mappingMap = {};

        // 创建映射查找表
        for (let i = 0; i < template.mappings.length; i++) {
            const mapping = template.mappings[i];
            mappingMap[mapping.sourceField] = mapping;
        }

        // 应用映射
        for (let i = 0; i < sourceFields.length; i++) {
            const sourceField = sourceFields[i];
            const fieldName = typeof sourceField === 'string' ? sourceField : sourceField.name;
            const mapping = mappingMap[fieldName];

            if (mapping) {
                // 有映射关系的字段
                mappedFields.push({
                    name: mapping.targetField,
                    label: this.getStandardFieldLabel(mapping.targetField),
                    type: mapping.dataType,
                    originalName: fieldName,
                    mapped: true,
                    mapping: mapping
                });
            } else {
                // 没有映射关系的字段，保持原样
                mappedFields.push({
                    name: fieldName,
                    label: fieldName,
                    type: typeof sourceField === 'object' ? sourceField.type : 'string',
                    originalName: fieldName,
                    mapped: false
                });
            }
        }

        SmartOffice.log('info', '映射模板应用成功: ' + template.name);
        return mappedFields;
    } catch (error) {
        SmartOffice.log('error', '应用映射模板失败:', error);
        return sourceFields;
    }
};

/**
 * @function FieldMapper.prototype.getStandardFieldLabel
 * @description 获取标准字段的显示标签
 * @param {string} fieldName - 标准字段名
 * @returns {string} 显示标签
 */
FieldMapper.prototype.getStandardFieldLabel = function(fieldName) {
    const standardField = this.standardFields.find(field => field.name === fieldName);
    return standardField ? standardField.label : fieldName;
};

/**
 * @function FieldMapper.prototype.getStandardFieldsByCategory
 * @description 按类别获取标准字段
 * @param {string} category - 字段类别
 * @returns {Array} 指定类别的标准字段列表
 */
FieldMapper.prototype.getStandardFieldsByCategory = function(category) {
    if (!category) {
        return this.standardFields;
    }

    return this.standardFields.filter(field => field.category === category);
};

/**
 * @function FieldMapper.prototype.validateMapping
 * @description 验证字段映射配置
 * @param {Object} mapping - 映射配置
 * @returns {Object} 验证结果
 */
FieldMapper.prototype.validateMapping = function(mapping) {
    const result = { isValid: true, errors: [] };

    if (!mapping) {
        result.isValid = false;
        result.errors.push('映射配置不能为空');
        return result;
    }

    if (!mapping.sourceField) {
        result.isValid = false;
        result.errors.push('源字段名不能为空');
    }

    if (!mapping.targetField) {
        result.isValid = false;
        result.errors.push('目标字段名不能为空');
    }

    // 检查目标字段是否为有效的标准字段
    const standardField = this.standardFields.find(field => field.name === mapping.targetField);
    if (!standardField) {
        result.isValid = false;
        result.errors.push('目标字段不是有效的标准字段');
    }

    return result;
};

// 创建全局字段映射管理器实例
SmartOffice.Data.FieldMapper = new FieldMapper();

SmartOffice.log('info', 'SmartOffice字段映射管理器模块初始化完成');
