/**
 * @file SmartOffice全局时间段管理器
 * @description 提供全局时间段配置的创建、管理和应用功能
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function TimeRangeManager
 * @description 时间段管理器构造函数
 * @constructor
 */
function TimeRangeManager() {
    /**
     * @property {Object} storage - 存储管理器引用
     */
    this.storage = SmartOffice.Core.Storage;

    /**
     * @property {Object} helpers - 工具函数引用
     */
    this.helpers = SmartOffice.Utils.Helpers;

    /**
     * @property {Object} eventBus - 事件总线引用
     */
    this.eventBus = SmartOffice.Core.EventBus;

    /**
     * @property {string} storageKey - 时间段配置存储键名
     */
    this.storageKey = 'global_time_ranges';

    /**
     * @property {Array} presetTimeRanges - 预设时间段模板
     */
    this.presetTimeRanges = [
        {
            id: 'morning',
            name: '上午',
            type: 'preset',
            start: '09:00',
            end: '12:00',
            description: '上午工作时间段'
        },
        {
            id: 'afternoon',
            name: '下午',
            type: 'preset',
            start: '13:00',
            end: '18:00',
            description: '下午工作时间段'
        },
        {
            id: 'evening',
            name: '晚上',
            type: 'preset',
            start: '19:00',
            end: '23:00',
            description: '晚上时间段'
        },
        {
            id: 'business_hours',
            name: '工作时间',
            type: 'preset',
            start: '09:00',
            end: '18:00',
            description: '标准工作时间'
        },
        {
            id: 'all_day',
            name: '全天',
            type: 'preset',
            start: '00:00',
            end: '23:59',
            description: '全天24小时'
        }
    ];

    /**
     * @property {Array} currentGlobalTimeRanges - 当前激活的全局时间段
     */
    this.currentGlobalTimeRanges = [];

    SmartOffice.log('info', 'TimeRangeManager时间段管理器初始化完成');
}

/**
 * @function TimeRangeManager.prototype.createGlobalTimeRange
 * @description 创建全局时间段
 * @param {Object} config - 时间段配置
 * @returns {string|null} 时间段ID
 */
TimeRangeManager.prototype.createGlobalTimeRange = function(config) {
    try {
        if (!config || !config.name) {
            throw new Error('时间段配置无效');
        }

        const timeRangeId = config.id || this.helpers.generateId('timerange');
        const timeRangeConfig = {
            id: timeRangeId,
            name: config.name,
            type: config.type || 'custom',
            start: config.start,
            end: config.end,
            description: config.description || '',
            isGlobal: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        // 验证时间段配置
        const validation = this.validateTimeRange(timeRangeConfig);
        if (!validation.isValid) {
            throw new Error('时间段配置验证失败: ' + validation.errors.join(', '));
        }

        // 保存到存储
        const timeRanges = this.getAllGlobalTimeRanges();
        const existingIndex = timeRanges.findIndex(tr => tr.id === timeRangeId);
        
        if (existingIndex >= 0) {
            timeRanges[existingIndex] = timeRangeConfig;
        } else {
            timeRanges.push(timeRangeConfig);
        }

        this.storage.set(this.storageKey, timeRanges);

        // 触发全局事件
        this.eventBus.emit('timeRange:created', timeRangeConfig);

        SmartOffice.log('info', '全局时间段创建成功: ' + config.name);
        return timeRangeId;
    } catch (error) {
        SmartOffice.log('error', '创建全局时间段失败:', error);
        return null;
    }
};

/**
 * @function TimeRangeManager.prototype.getAllGlobalTimeRanges
 * @description 获取所有全局时间段
 * @param {boolean} includePresets - 是否包含预设时间段
 * @returns {Array} 全局时间段列表
 */
TimeRangeManager.prototype.getAllGlobalTimeRanges = function(includePresets) {
    try {
        const customTimeRanges = this.storage.get(this.storageKey, []);
        
        if (includePresets !== false) {
            // 合并预设和自定义时间段
            return this.presetTimeRanges.concat(customTimeRanges);
        } else {
            // 只返回自定义时间段
            return customTimeRanges;
        }
    } catch (error) {
        SmartOffice.log('error', '获取全局时间段列表失败:', error);
        return includePresets !== false ? this.presetTimeRanges : [];
    }
};

/**
 * @function TimeRangeManager.prototype.getGlobalTimeRange
 * @description 获取指定的全局时间段
 * @param {string} timeRangeId - 时间段ID
 * @returns {Object|null} 时间段配置
 */
TimeRangeManager.prototype.getGlobalTimeRange = function(timeRangeId) {
    try {
        const allTimeRanges = this.getAllGlobalTimeRanges(true);
        const timeRange = allTimeRanges.find(tr => tr.id === timeRangeId);
        
        if (timeRange) {
            SmartOffice.log('info', '获取全局时间段成功: ' + timeRange.name);
            return timeRange;
        } else {
            SmartOffice.log('warn', '未找到全局时间段: ' + timeRangeId);
            return null;
        }
    } catch (error) {
        SmartOffice.log('error', '获取全局时间段失败:', error);
        return null;
    }
};

/**
 * @function TimeRangeManager.prototype.deleteGlobalTimeRange
 * @description 删除全局时间段
 * @param {string} timeRangeId - 时间段ID
 * @returns {boolean} 是否删除成功
 */
TimeRangeManager.prototype.deleteGlobalTimeRange = function(timeRangeId) {
    try {
        // 不能删除预设时间段
        const presetIds = this.presetTimeRanges.map(tr => tr.id);
        if (presetIds.includes(timeRangeId)) {
            SmartOffice.log('warn', '不能删除预设时间段: ' + timeRangeId);
            return false;
        }

        const customTimeRanges = this.getAllGlobalTimeRanges(false);
        const timeRangeIndex = customTimeRanges.findIndex(tr => tr.id === timeRangeId);
        
        if (timeRangeIndex >= 0) {
            const deletedTimeRange = customTimeRanges.splice(timeRangeIndex, 1)[0];
            this.storage.set(this.storageKey, customTimeRanges);
            
            // 触发事件
            this.eventBus.emit('timeRange:deleted', deletedTimeRange);
            
            SmartOffice.log('info', '全局时间段删除成功: ' + deletedTimeRange.name);
            return true;
        } else {
            SmartOffice.log('warn', '未找到要删除的全局时间段: ' + timeRangeId);
            return false;
        }
    } catch (error) {
        SmartOffice.log('error', '删除全局时间段失败:', error);
        return false;
    }
};

/**
 * @function TimeRangeManager.prototype.setActiveTimeRanges
 * @description 设置当前激活的时间段
 * @param {Array} timeRangeIds - 时间段ID列表
 * @returns {boolean} 是否设置成功
 */
TimeRangeManager.prototype.setActiveTimeRanges = function(timeRangeIds) {
    try {
        this.currentGlobalTimeRanges = [];
        
        for (let i = 0; i < timeRangeIds.length; i++) {
            const timeRange = this.getGlobalTimeRange(timeRangeIds[i]);
            if (timeRange) {
                this.currentGlobalTimeRanges.push(timeRange);
            }
        }

        // 触发事件
        this.eventBus.emit('timeRange:activeChanged', this.currentGlobalTimeRanges);

        SmartOffice.log('info', '设置激活时间段成功，共' + this.currentGlobalTimeRanges.length + '个');
        return true;
    } catch (error) {
        SmartOffice.log('error', '设置激活时间段失败:', error);
        return false;
    }
};

/**
 * @function TimeRangeManager.prototype.getActiveTimeRanges
 * @description 获取当前激活的时间段
 * @returns {Array} 激活的时间段列表
 */
TimeRangeManager.prototype.getActiveTimeRanges = function() {
    return this.currentGlobalTimeRanges;
};

/**
 * @function TimeRangeManager.prototype.applyTimeRangeToData
 * @description 将时间段应用到数据中
 * @param {Array} data - 原始数据
 * @param {string} timeField - 时间字段名
 * @param {Array} timeRanges - 要应用的时间段列表
 * @returns {Array} 处理后的数据
 */
TimeRangeManager.prototype.applyTimeRangeToData = function(data, timeField, timeRanges) {
    try {
        if (!data || !timeField || !timeRanges || timeRanges.length === 0) {
            return data;
        }

        const filteredData = [];

        for (let i = 0; i < data.length; i++) {
            const row = data[i];
            const timeValue = row[timeField];
            
            if (this.isTimeInRanges(timeValue, timeRanges)) {
                filteredData.push(row);
            }
        }

        SmartOffice.log('info', '时间段过滤完成，从' + data.length + '行过滤到' + filteredData.length + '行');
        return filteredData;
    } catch (error) {
        SmartOffice.log('error', '应用时间段过滤失败:', error);
        return data;
    }
};

/**
 * @function TimeRangeManager.prototype.isTimeInRanges
 * @description 检查时间是否在指定的时间段内
 * @param {string} timeValue - 时间值
 * @param {Array} timeRanges - 时间段列表
 * @returns {boolean} 是否在时间段内
 */
TimeRangeManager.prototype.isTimeInRanges = function(timeValue, timeRanges) {
    if (!timeValue || !timeRanges || timeRanges.length === 0) {
        return false;
    }

    const time = this.parseTimeValue(timeValue);
    if (!time) {
        return false;
    }

    for (let i = 0; i < timeRanges.length; i++) {
        const range = timeRanges[i];
        if (this.isTimeInRange(time, range.start, range.end)) {
            return true;
        }
    }

    return false;
};

/**
 * @function TimeRangeManager.prototype.parseTimeValue
 * @description 解析时间值
 * @param {string} timeValue - 时间值
 * @returns {string|null} 标准化的时间字符串 (HH:MM)
 */
TimeRangeManager.prototype.parseTimeValue = function(timeValue) {
    if (!timeValue) return null;

    try {
        // 尝试解析各种时间格式
        const timeStr = String(timeValue).trim();
        
        // HH:MM 格式
        const timeMatch = timeStr.match(/(\d{1,2}):(\d{2})/);
        if (timeMatch) {
            const hours = parseInt(timeMatch[1], 10);
            const minutes = parseInt(timeMatch[2], 10);
            if (hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
                return String(hours).padStart(2, '0') + ':' + String(minutes).padStart(2, '0');
            }
        }

        // 尝试从日期时间中提取时间
        const date = new Date(timeValue);
        if (!isNaN(date.getTime())) {
            const hours = date.getHours();
            const minutes = date.getMinutes();
            return String(hours).padStart(2, '0') + ':' + String(minutes).padStart(2, '0');
        }

        return null;
    } catch (error) {
        return null;
    }
};

/**
 * @function TimeRangeManager.prototype.isTimeInRange
 * @description 检查时间是否在指定范围内
 * @param {string} time - 时间 (HH:MM)
 * @param {string} startTime - 开始时间 (HH:MM)
 * @param {string} endTime - 结束时间 (HH:MM)
 * @returns {boolean} 是否在范围内
 */
TimeRangeManager.prototype.isTimeInRange = function(time, startTime, endTime) {
    if (!time || !startTime || !endTime) {
        return false;
    }

    const timeMinutes = this.timeToMinutes(time);
    const startMinutes = this.timeToMinutes(startTime);
    const endMinutes = this.timeToMinutes(endTime);

    if (timeMinutes === null || startMinutes === null || endMinutes === null) {
        return false;
    }

    // 处理跨天的情况
    if (startMinutes <= endMinutes) {
        // 同一天内
        return timeMinutes >= startMinutes && timeMinutes <= endMinutes;
    } else {
        // 跨天
        return timeMinutes >= startMinutes || timeMinutes <= endMinutes;
    }
};

/**
 * @function TimeRangeManager.prototype.timeToMinutes
 * @description 将时间转换为分钟数
 * @param {string} time - 时间 (HH:MM)
 * @returns {number|null} 分钟数
 */
TimeRangeManager.prototype.timeToMinutes = function(time) {
    if (!time) return null;

    const parts = time.split(':');
    if (parts.length !== 2) return null;

    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);

    if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
        return null;
    }

    return hours * 60 + minutes;
};

/**
 * @function TimeRangeManager.prototype.validateTimeRange
 * @description 验证时间段配置
 * @param {Object} timeRange - 时间段配置
 * @returns {Object} 验证结果
 */
TimeRangeManager.prototype.validateTimeRange = function(timeRange) {
    const result = { isValid: true, errors: [] };

    if (!timeRange) {
        result.isValid = false;
        result.errors.push('时间段配置不能为空');
        return result;
    }

    if (!timeRange.name) {
        result.isValid = false;
        result.errors.push('时间段名称不能为空');
    }

    if (!timeRange.start) {
        result.isValid = false;
        result.errors.push('开始时间不能为空');
    }

    if (!timeRange.end) {
        result.isValid = false;
        result.errors.push('结束时间不能为空');
    }

    // 验证时间格式
    if (timeRange.start && !this.parseTimeValue(timeRange.start)) {
        result.isValid = false;
        result.errors.push('开始时间格式无效');
    }

    if (timeRange.end && !this.parseTimeValue(timeRange.end)) {
        result.isValid = false;
        result.errors.push('结束时间格式无效');
    }

    return result;
};

// 创建全局时间段管理器实例
SmartOffice.Data.TimeRangeManager = new TimeRangeManager();

SmartOffice.log('info', 'SmartOffice全局时间段管理器模块初始化完成');
