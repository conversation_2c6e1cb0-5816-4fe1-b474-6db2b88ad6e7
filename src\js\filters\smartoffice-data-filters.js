/**
 * SmartOffice数据筛选器
 * 提供高级数据筛选、排序和搜索功能
 * @function DataFilters - 数据筛选和排序管理
 */

(function() {
    'use strict';

    /**
     * 数据筛选器构造函数
     * @function DataFilters - 创建数据筛选器实例
     */
    function DataFilters() {
        this.eventBus = SmartOffice.Core.EventBus;
        this.activeFilters = [];
        this.sortConfig = null;
        this.searchQuery = '';
        
        console.log('🔍 数据筛选器: 初始化完成');
    }

    /**
     * 添加筛选条件
     * @function addFilter - 添加新的筛选条件
     * @param {Object} filter - 筛选条件对象
     * @returns {string} 筛选器ID
     */
    DataFilters.prototype.addFilter = function(filter) {
        const filterId = SmartOffice.Utils.Helpers.generateId();
        
        const filterObj = {
            id: filterId,
            field: filter.field,
            operator: filter.operator || 'equals',
            value: filter.value,
            type: filter.type || 'text',
            enabled: filter.enabled !== false,
            createdAt: new Date().toISOString()
        };
        
        // 验证筛选条件
        if (!this.validateFilter(filterObj)) {
            throw new Error('无效的筛选条件');
        }
        
        this.activeFilters.push(filterObj);
        
        this.eventBus.emit('filter:added', { filter: filterObj });
        console.log('✅ 数据筛选器: 已添加筛选条件', filterObj.field, filterObj.operator, filterObj.value);
        
        return filterId;
    };

    /**
     * 移除筛选条件
     * @function removeFilter - 移除指定的筛选条件
     * @param {string} filterId - 筛选器ID
     * @returns {boolean} 是否成功移除
     */
    DataFilters.prototype.removeFilter = function(filterId) {
        const index = this.activeFilters.findIndex(function(filter) {
            return filter.id === filterId;
        });
        
        if (index === -1) {
            return false;
        }
        
        const removedFilter = this.activeFilters.splice(index, 1)[0];
        
        this.eventBus.emit('filter:removed', { filter: removedFilter });
        console.log('✅ 数据筛选器: 已移除筛选条件', removedFilter.field);
        
        return true;
    };

    /**
     * 清除所有筛选条件
     * @function clearFilters - 清除所有筛选条件
     */
    DataFilters.prototype.clearFilters = function() {
        const removedCount = this.activeFilters.length;
        this.activeFilters = [];
        
        this.eventBus.emit('filters:cleared', { count: removedCount });
        console.log('✅ 数据筛选器: 已清除所有筛选条件');
    };

    /**
     * 设置排序配置
     * @function setSortConfig - 设置数据排序配置
     * @param {Object} sortConfig - 排序配置对象
     */
    DataFilters.prototype.setSortConfig = function(sortConfig) {
        this.sortConfig = {
            field: sortConfig.field,
            order: sortConfig.order || 'asc', // 'asc' 或 'desc'
            type: sortConfig.type || 'auto'   // 'text', 'number', 'date', 'auto'
        };
        
        this.eventBus.emit('sort:changed', { sortConfig: this.sortConfig });
        console.log('✅ 数据筛选器: 已设置排序', this.sortConfig.field, this.sortConfig.order);
    };

    /**
     * 设置搜索查询
     * @function setSearchQuery - 设置全局搜索查询
     * @param {string} query - 搜索查询字符串
     */
    DataFilters.prototype.setSearchQuery = function(query) {
        this.searchQuery = query || '';
        
        this.eventBus.emit('search:changed', { query: this.searchQuery });
        console.log('✅ 数据筛选器: 已设置搜索查询', this.searchQuery);
    };

    /**
     * 应用所有筛选条件到数据
     * @function applyFilters - 对数据应用所有筛选条件
     * @param {Array} data - 原始数据数组
     * @returns {Array} 筛选后的数据数组
     */
    DataFilters.prototype.applyFilters = function(data) {
        if (!data || !Array.isArray(data)) {
            return [];
        }
        
        let filteredData = data.slice(); // 创建副本
        
        // 应用搜索查询
        if (this.searchQuery && this.searchQuery.trim() !== '') {
            filteredData = this.applySearch(filteredData, this.searchQuery);
        }
        
        // 应用字段筛选
        this.activeFilters.forEach(function(filter) {
            if (filter.enabled) {
                filteredData = this.applyFieldFilter(filteredData, filter);
            }
        }.bind(this));
        
        // 应用排序
        if (this.sortConfig) {
            filteredData = this.applySorting(filteredData, this.sortConfig);
        }
        
        console.log('🔍 数据筛选器: 筛选完成，从', data.length, '行筛选出', filteredData.length, '行');
        
        return filteredData;
    };

    /**
     * 应用搜索查询
     * @function applySearch - 应用全局搜索
     * @param {Array} data - 数据数组
     * @param {string} query - 搜索查询
     * @returns {Array} 搜索结果数组
     */
    DataFilters.prototype.applySearch = function(data, query) {
        const lowerQuery = query.toLowerCase();
        
        return data.filter(function(row) {
            return Object.values(row).some(function(value) {
                return String(value).toLowerCase().includes(lowerQuery);
            });
        });
    };

    /**
     * 应用字段筛选
     * @function applyFieldFilter - 应用单个字段筛选
     * @param {Array} data - 数据数组
     * @param {Object} filter - 筛选条件
     * @returns {Array} 筛选结果数组
     */
    DataFilters.prototype.applyFieldFilter = function(data, filter) {
        return data.filter(function(row) {
            const fieldValue = row[filter.field];
            return this.evaluateCondition(fieldValue, filter.operator, filter.value, filter.type);
        }.bind(this));
    };

    /**
     * 评估筛选条件
     * @function evaluateCondition - 评估单个筛选条件
     * @param {*} fieldValue - 字段值
     * @param {string} operator - 操作符
     * @param {*} filterValue - 筛选值
     * @param {string} type - 数据类型
     * @returns {boolean} 是否满足条件
     */
    DataFilters.prototype.evaluateCondition = function(fieldValue, operator, filterValue, type) {
        // 处理空值
        if (fieldValue === null || fieldValue === undefined) {
            fieldValue = '';
        }
        
        const strFieldValue = String(fieldValue);
        const strFilterValue = String(filterValue);
        
        switch (operator) {
            case 'equals':
                return strFieldValue === strFilterValue;
                
            case 'not_equals':
                return strFieldValue !== strFilterValue;
                
            case 'contains':
                return strFieldValue.toLowerCase().includes(strFilterValue.toLowerCase());
                
            case 'not_contains':
                return !strFieldValue.toLowerCase().includes(strFilterValue.toLowerCase());
                
            case 'starts_with':
                return strFieldValue.toLowerCase().startsWith(strFilterValue.toLowerCase());
                
            case 'ends_with':
                return strFieldValue.toLowerCase().endsWith(strFilterValue.toLowerCase());
                
            case 'greater_than':
                return this.compareValues(fieldValue, filterValue, type) > 0;
                
            case 'less_than':
                return this.compareValues(fieldValue, filterValue, type) < 0;
                
            case 'greater_equal':
                return this.compareValues(fieldValue, filterValue, type) >= 0;
                
            case 'less_equal':
                return this.compareValues(fieldValue, filterValue, type) <= 0;
                
            case 'is_empty':
                return strFieldValue.trim() === '';
                
            case 'is_not_empty':
                return strFieldValue.trim() !== '';
                
            default:
                return true;
        }
    };

    /**
     * 比较两个值
     * @function compareValues - 根据类型比较两个值
     * @param {*} value1 - 值1
     * @param {*} value2 - 值2
     * @param {string} type - 数据类型
     * @returns {number} 比较结果 (-1, 0, 1)
     */
    DataFilters.prototype.compareValues = function(value1, value2, type) {
        if (type === 'number') {
            const num1 = parseFloat(value1) || 0;
            const num2 = parseFloat(value2) || 0;
            return num1 - num2;
        }
        
        if (type === 'date') {
            const date1 = new Date(value1);
            const date2 = new Date(value2);
            return date1.getTime() - date2.getTime();
        }
        
        // 默认字符串比较
        return String(value1).localeCompare(String(value2));
    };

    /**
     * 应用排序
     * @function applySorting - 对数据应用排序
     * @param {Array} data - 数据数组
     * @param {Object} sortConfig - 排序配置
     * @returns {Array} 排序后的数据数组
     */
    DataFilters.prototype.applySorting = function(data, sortConfig) {
        const sortedData = data.slice(); // 创建副本
        
        sortedData.sort(function(a, b) {
            const value1 = a[sortConfig.field];
            const value2 = b[sortConfig.field];
            
            let comparison = this.compareValues(value1, value2, sortConfig.type);
            
            // 如果是降序，反转比较结果
            if (sortConfig.order === 'desc') {
                comparison = -comparison;
            }
            
            return comparison;
        }.bind(this));
        
        return sortedData;
    };

    /**
     * 验证筛选条件
     * @function validateFilter - 验证筛选条件的有效性
     * @param {Object} filter - 筛选条件对象
     * @returns {boolean} 是否有效
     */
    DataFilters.prototype.validateFilter = function(filter) {
        if (!filter.field || typeof filter.field !== 'string') {
            return false;
        }
        
        const validOperators = [
            'equals', 'not_equals', 'contains', 'not_contains',
            'starts_with', 'ends_with', 'greater_than', 'less_than',
            'greater_equal', 'less_equal', 'is_empty', 'is_not_empty'
        ];
        
        if (!validOperators.includes(filter.operator)) {
            return false;
        }
        
        return true;
    };

    /**
     * 获取当前筛选状态
     * @function getFilterState - 获取当前筛选状态
     * @returns {Object} 筛选状态对象
     */
    DataFilters.prototype.getFilterState = function() {
        return {
            filters: this.activeFilters.slice(),
            sortConfig: this.sortConfig ? Object.assign({}, this.sortConfig) : null,
            searchQuery: this.searchQuery,
            filterCount: this.activeFilters.filter(function(f) { return f.enabled; }).length
        };
    };

    /**
     * 获取支持的操作符列表
     * @function getSupportedOperators - 获取支持的筛选操作符
     * @returns {Array} 操作符列表
     */
    DataFilters.prototype.getSupportedOperators = function() {
        return [
            { value: 'equals', label: '等于', icon: '=' },
            { value: 'not_equals', label: '不等于', icon: '≠' },
            { value: 'contains', label: '包含', icon: '⊃' },
            { value: 'not_contains', label: '不包含', icon: '⊅' },
            { value: 'starts_with', label: '开始于', icon: '⌐' },
            { value: 'ends_with', label: '结束于', icon: '⌐' },
            { value: 'greater_than', label: '大于', icon: '>' },
            { value: 'less_than', label: '小于', icon: '<' },
            { value: 'greater_equal', label: '大于等于', icon: '≥' },
            { value: 'less_equal', label: '小于等于', icon: '≤' },
            { value: 'is_empty', label: '为空', icon: '∅' },
            { value: 'is_not_empty', label: '不为空', icon: '∅̸' }
        ];
    };

    // 注册到全局命名空间
    if (!SmartOffice.Filters) {
        SmartOffice.Filters = {};
    }
    SmartOffice.Filters.DataFilters = DataFilters;

    console.log('🔍 数据筛选器: 模块加载完成');

})();
