/**
 * SmartOffice JSON解析器
 * 解析JSON文件并标准化为透视表可用的数据格式
 * @function JSONParser - JSON文件解析和数据标准化
 */

(function() {
    'use strict';

    /**
     * JSON解析器构造函数
     * @function JSONParser - 创建JSON解析器实例
     */
    function JSONParser() {
        this.eventBus = SmartOffice.Core.EventBus;
        this.dataValidator = new SmartOffice.Data.DataValidator();
        
        // 支持的文件类型
        this.supportedTypes = [
            'application/json',
            'text/json'
        ];
        
        console.log('📄 JSON解析器: 初始化完成');
    }

    /**
     * 检查文件是否为JSON格式
     * @function isJSONFile - 验证文件格式
     * @param {File} file - 要检查的文件对象
     * @returns {boolean} 是否为JSON文件
     */
    JSONParser.prototype.isJSONFile = function(file) {
        if (!file) {
            return false;
        }
        
        // 检查MIME类型
        if (this.supportedTypes.includes(file.type)) {
            return true;
        }
        
        // 检查文件扩展名
        const fileName = file.name.toLowerCase();
        return fileName.endsWith('.json');
    };

    /**
     * 解析JSON文件
     * @function parseFile - 解析JSON文件并提取数据
     * @param {File} file - JSON文件对象
     * @returns {Promise} 解析结果Promise
     */
    JSONParser.prototype.parseFile = function(file) {
        return new Promise(function(resolve, reject) {
            if (!this.isJSONFile(file)) {
                reject(new Error('不支持的文件格式，请选择.json文件'));
                return;
            }

            console.log('📄 JSON解析器: 开始解析文件', file.name);
            this.eventBus.emit('json:parse:start', { file: file });

            const reader = new FileReader();
            
            reader.onload = function(e) {
                try {
                    const jsonText = e.target.result;
                    const jsonData = JSON.parse(jsonText);
                    
                    // 标准化JSON数据
                    const standardizedData = this.standardizeData(jsonData);
                    
                    // 验证和处理数据
                    const result = this.processData(standardizedData, file.name);
                    
                    console.log('✅ JSON解析器: 解析完成，共', result.data.length, '行数据');
                    this.eventBus.emit('json:parse:complete', result);
                    
                    resolve(result);
                } catch (error) {
                    console.error('❌ JSON解析器: 解析失败', error);
                    this.eventBus.emit('json:parse:error', { error: error });
                    reject(new Error('JSON文件格式错误: ' + error.message));
                }
            }.bind(this);
            
            reader.onerror = function() {
                const error = new Error('文件读取失败');
                console.error('❌ JSON解析器: 文件读取失败');
                this.eventBus.emit('json:parse:error', { error: error });
                reject(error);
            }.bind(this);
            
            reader.readAsText(file, 'UTF-8');
        }.bind(this));
    };

    /**
     * 标准化JSON数据为数组格式
     * @function standardizeData - 将各种JSON格式标准化为对象数组
     * @param {*} jsonData - 解析后的JSON数据
     * @returns {Array} 标准化的数据数组
     */
    JSONParser.prototype.standardizeData = function(jsonData) {
        // 如果已经是数组，直接返回
        if (Array.isArray(jsonData)) {
            return this.processArray(jsonData);
        }
        
        // 如果是对象，尝试找到数据数组
        if (typeof jsonData === 'object' && jsonData !== null) {
            return this.processObject(jsonData);
        }
        
        throw new Error('不支持的JSON数据格式，请提供对象数组或包含数据数组的对象');
    };

    /**
     * 处理JSON数组
     * @function processArray - 处理数组类型的JSON数据
     * @param {Array} arrayData - 数组数据
     * @returns {Array} 处理后的数据数组
     */
    JSONParser.prototype.processArray = function(arrayData) {
        if (arrayData.length === 0) {
            throw new Error('JSON数组为空');
        }
        
        // 检查数组元素类型
        const firstElement = arrayData[0];
        
        if (typeof firstElement === 'object' && firstElement !== null) {
            // 对象数组，直接返回
            return arrayData;
        } else {
            // 基本类型数组，转换为对象数组
            return arrayData.map(function(item, index) {
                return {
                    '索引': index + 1,
                    '值': item
                };
            });
        }
    };

    /**
     * 处理JSON对象
     * @function processObject - 处理对象类型的JSON数据
     * @param {Object} objectData - 对象数据
     * @returns {Array} 处理后的数据数组
     */
    JSONParser.prototype.processObject = function(objectData) {
        // 查找可能包含数据的数组字段
        const arrayFields = [];
        
        for (const key in objectData) {
            if (objectData.hasOwnProperty(key) && Array.isArray(objectData[key])) {
                arrayFields.push({
                    key: key,
                    length: objectData[key].length,
                    data: objectData[key]
                });
            }
        }
        
        if (arrayFields.length === 0) {
            // 没有数组字段，将对象转换为单行数据
            return [objectData];
        }
        
        // 选择最大的数组作为主数据
        arrayFields.sort(function(a, b) {
            return b.length - a.length;
        });
        
        const mainArray = arrayFields[0].data;
        
        if (mainArray.length === 0) {
            throw new Error('找到的数据数组为空');
        }
        
        // 如果数组元素是对象，直接返回
        if (typeof mainArray[0] === 'object' && mainArray[0] !== null) {
            return mainArray;
        }
        
        // 如果数组元素是基本类型，转换为对象
        return mainArray.map(function(item, index) {
            return {
                '索引': index + 1,
                '值': item
            };
        });
    };

    /**
     * 处理和标准化数据
     * @function processData - 处理解析后的数据
     * @param {Array} rawData - 原始数据数组
     * @param {string} fileName - 文件名
     * @returns {Object} 处理后的数据对象
     */
    JSONParser.prototype.processData = function(rawData, fileName) {
        if (!rawData || rawData.length === 0) {
            throw new Error('JSON文件中没有找到数据');
        }

        // 提取所有可能的字段名
        const allFields = new Set();
        rawData.forEach(function(row) {
            if (typeof row === 'object' && row !== null) {
                Object.keys(row).forEach(function(key) {
                    allFields.add(key);
                });
            }
        });

        const headers = Array.from(allFields);
        
        if (headers.length === 0) {
            throw new Error('JSON数据中没有找到有效字段');
        }

        // 标准化数据，确保每行都有所有字段
        const standardizedData = rawData.map(function(row) {
            const standardRow = {};
            headers.forEach(function(header) {
                const value = row[header];
                standardRow[header] = value !== undefined && value !== null ? String(value) : '';
            });
            return standardRow;
        });

        // 过滤完全空的行
        const filteredData = standardizedData.filter(function(row) {
            return Object.values(row).some(function(value) {
                return value && value.trim() !== '';
            });
        });

        // 数据验证和类型检测
        const validationResult = this.dataValidator.validateData(filteredData, headers);
        
        return {
            fileName: fileName,
            fileType: 'json',
            headers: headers,
            data: filteredData,
            rowCount: filteredData.length,
            columnCount: headers.length,
            fieldTypes: validationResult.fieldTypes,
            statistics: this.generateStatistics(filteredData, headers),
            parseTime: new Date().toISOString(),
            isValid: validationResult.isValid,
            errors: validationResult.errors || []
        };
    };

    /**
     * 生成数据统计信息
     * @function generateStatistics - 生成数据统计
     * @param {Array} data - 数据数组
     * @param {Array} headers - 表头数组
     * @returns {Object} 统计信息对象
     */
    JSONParser.prototype.generateStatistics = function(data, headers) {
        const stats = {};
        
        headers.forEach(function(header) {
            const values = data.map(function(row) {
                return row[header];
            }).filter(function(value) {
                return value && value.trim() !== '';
            });
            
            stats[header] = {
                totalCount: data.length,
                validCount: values.length,
                emptyCount: data.length - values.length,
                uniqueCount: new Set(values).size,
                sampleValues: values.slice(0, 5)
            };
        });
        
        return stats;
    };

    /**
     * 获取支持的文件类型
     * @function getSupportedTypes - 获取支持的文件类型列表
     * @returns {Array} 支持的MIME类型数组
     */
    JSONParser.prototype.getSupportedTypes = function() {
        return this.supportedTypes.slice();
    };

    // 注册到全局命名空间
    if (!SmartOffice.Parsers) {
        SmartOffice.Parsers = {};
    }
    SmartOffice.Parsers.JSONParser = JSONParser;

    console.log('📄 JSON解析器: 模块加载完成');

})();
