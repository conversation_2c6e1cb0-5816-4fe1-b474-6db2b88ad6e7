/**
 * SmartOffice模板管理器
 * 管理预设透视表模板和用户自定义模板
 */

(function() {
    'use strict';

    /**
     * 模板管理器构造函数
     */
    function TemplateManager() {
        this.templates = [];
        this.categories = [];
        this.storage = SmartOffice.Core.Storage;
        this.eventBus = SmartOffice.Core.EventBus;
        
        // 模板存储键
        this.STORAGE_KEY = 'smartoffice_templates';
        
        // 初始化
        this.init();
    }

    /**
     * 初始化模板管理器
     */
    TemplateManager.prototype.init = function() {
        console.log('📋 模板管理器: 初始化开始');
        
        // 加载内置模板
        this.loadBuiltinTemplates();
        
        // 加载用户模板
        this.loadUserTemplates();
        
        console.log('✅ 模板管理器: 初始化完成，共加载', this.templates.length, '个模板');
    };

    /**
     * 加载内置模板
     */
    TemplateManager.prototype.loadBuiltinTemplates = function() {
        const builtinTemplates = [
            {
                id: 'sales-by-region',
                name: '按地区销售统计',
                description: '统计各地区的销售数据，适用于销售报表分析',
                category: 'sales',
                categoryName: '销售分析',
                icon: '📊',
                isBuiltin: true,
                config: {
                    name: '按地区销售统计',
                    description: '统计各地区的销售数据',
                    rowFields: ['地区', '省份'],
                    columnFields: ['产品类别'],
                    valueFields: ['销售额', '销售量'],
                    aggregationMethod: 'sum',
                    filterFields: [],
                    sortBy: 'rowFields',
                    sortOrder: 'asc'
                },
                requiredFields: ['地区', '销售额'],
                suggestedFields: ['省份', '产品类别', '销售量', '销售员']
            },
            
            {
                id: 'product-performance',
                name: '产品销售业绩',
                description: '分析各产品的销售表现和盈利情况',
                category: 'sales',
                categoryName: '销售分析',
                icon: '📈',
                isBuiltin: true,
                config: {
                    name: '产品销售业绩',
                    description: '分析产品销售表现',
                    rowFields: ['产品名称', '产品类别'],
                    columnFields: ['月份'],
                    valueFields: ['销售额', '利润', '销售量'],
                    aggregationMethod: 'sum',
                    filterFields: ['产品类别'],
                    sortBy: 'valueFields',
                    sortOrder: 'desc'
                },
                requiredFields: ['产品名称', '销售额'],
                suggestedFields: ['产品类别', '月份', '利润', '销售量']
            },
            
            {
                id: 'monthly-trend',
                name: '月度趋势分析',
                description: '分析数据的月度变化趋势，适用于时间序列分析',
                category: 'time',
                categoryName: '时间分析',
                icon: '📅',
                isBuiltin: true,
                config: {
                    name: '月度趋势分析',
                    description: '分析月度变化趋势',
                    rowFields: ['年份', '月份'],
                    columnFields: ['类别'],
                    valueFields: ['数量', '金额'],
                    aggregationMethod: 'sum',
                    filterFields: ['年份'],
                    sortBy: 'rowFields',
                    sortOrder: 'asc'
                },
                requiredFields: ['月份', '数量'],
                suggestedFields: ['年份', '类别', '金额', '日期']
            },
            
            {
                id: 'customer-analysis',
                name: '客户分析报表',
                description: '分析客户购买行为和价值分布',
                category: 'customer',
                categoryName: '客户分析',
                icon: '👥',
                isBuiltin: true,
                config: {
                    name: '客户分析报表',
                    description: '分析客户购买行为',
                    rowFields: ['客户等级', '客户地区'],
                    columnFields: ['购买渠道'],
                    valueFields: ['购买金额', '购买次数'],
                    aggregationMethod: 'sum',
                    filterFields: ['客户等级'],
                    sortBy: 'valueFields',
                    sortOrder: 'desc'
                },
                requiredFields: ['客户等级', '购买金额'],
                suggestedFields: ['客户地区', '购买渠道', '购买次数', '客户名称']
            },
            
            {
                id: 'inventory-summary',
                name: '库存汇总表',
                description: '汇总分析库存状况和周转情况',
                category: 'inventory',
                categoryName: '库存管理',
                icon: '📦',
                isBuiltin: true,
                config: {
                    name: '库存汇总表',
                    description: '库存状况汇总分析',
                    rowFields: ['仓库', '产品类别'],
                    columnFields: ['库存状态'],
                    valueFields: ['库存数量', '库存金额'],
                    aggregationMethod: 'sum',
                    filterFields: ['仓库'],
                    sortBy: 'valueFields',
                    sortOrder: 'desc'
                },
                requiredFields: ['仓库', '库存数量'],
                suggestedFields: ['产品类别', '库存状态', '库存金额', '产品名称']
            },
            
            {
                id: 'financial-summary',
                name: '财务汇总分析',
                description: '财务数据的分类汇总和分析',
                category: 'finance',
                categoryName: '财务分析',
                icon: '💰',
                isBuiltin: true,
                config: {
                    name: '财务汇总分析',
                    description: '财务数据汇总分析',
                    rowFields: ['科目', '部门'],
                    columnFields: ['月份'],
                    valueFields: ['收入', '支出', '利润'],
                    aggregationMethod: 'sum',
                    filterFields: ['科目'],
                    sortBy: 'rowFields',
                    sortOrder: 'asc'
                },
                requiredFields: ['科目', '收入'],
                suggestedFields: ['部门', '月份', '支出', '利润']
            }
        ];
        
        // 添加到模板列表
        this.templates = this.templates.concat(builtinTemplates);
        
        // 提取分类信息
        this.extractCategories();
        
        console.log('📋 已加载', builtinTemplates.length, '个内置模板');
    };

    /**
     * 加载用户自定义模板
     */
    TemplateManager.prototype.loadUserTemplates = function() {
        try {
            const userTemplates = this.storage.get(this.STORAGE_KEY) || [];
            
            // 验证和添加用户模板
            userTemplates.forEach(function(template) {
                if (this.validateTemplate(template)) {
                    this.templates.push(template);
                }
            }.bind(this));
            
            console.log('📋 已加载', userTemplates.length, '个用户模板');
        } catch (error) {
            console.error('❌ 加载用户模板失败:', error);
        }
    };

    /**
     * 提取分类信息
     */
    TemplateManager.prototype.extractCategories = function() {
        const categoryMap = new Map();
        
        this.templates.forEach(function(template) {
            if (template.category && template.categoryName) {
                categoryMap.set(template.category, {
                    id: template.category,
                    name: template.categoryName,
                    count: (categoryMap.get(template.category) || { count: 0 }).count + 1
                });
            }
        });
        
        this.categories = Array.from(categoryMap.values());
    };

    /**
     * 获取所有模板
     */
    TemplateManager.prototype.getAllTemplates = function() {
        return this.templates.slice(); // 返回副本
    };

    /**
     * 按分类获取模板
     */
    TemplateManager.prototype.getTemplatesByCategory = function(category) {
        return this.templates.filter(function(template) {
            return template.category === category;
        });
    };

    /**
     * 获取模板分类
     */
    TemplateManager.prototype.getCategories = function() {
        return this.categories.slice(); // 返回副本
    };

    /**
     * 根据ID获取模板
     */
    TemplateManager.prototype.getTemplateById = function(id) {
        return this.templates.find(function(template) {
            return template.id === id;
        });
    };

    /**
     * 搜索模板
     */
    TemplateManager.prototype.searchTemplates = function(keyword) {
        if (!keyword || keyword.trim() === '') {
            return this.getAllTemplates();
        }
        
        const lowerKeyword = keyword.toLowerCase();
        
        return this.templates.filter(function(template) {
            return template.name.toLowerCase().includes(lowerKeyword) ||
                   template.description.toLowerCase().includes(lowerKeyword) ||
                   template.categoryName.toLowerCase().includes(lowerKeyword);
        });
    };

    /**
     * 根据字段匹配推荐模板
     */
    TemplateManager.prototype.recommendTemplates = function(availableFields) {
        if (!availableFields || availableFields.length === 0) {
            return [];
        }
        
        const fieldSet = new Set(availableFields.map(function(field) {
            return field.toLowerCase();
        }));
        
        const recommendations = this.templates.map(function(template) {
            let score = 0;
            
            // 检查必需字段匹配度
            const requiredFields = template.requiredFields || [];
            const requiredMatches = requiredFields.filter(function(field) {
                return fieldSet.has(field.toLowerCase());
            });
            
            if (requiredMatches.length === requiredFields.length) {
                score += 100; // 必需字段全部匹配
            } else if (requiredMatches.length > 0) {
                score += (requiredMatches.length / requiredFields.length) * 50;
            }
            
            // 检查建议字段匹配度
            const suggestedFields = template.suggestedFields || [];
            const suggestedMatches = suggestedFields.filter(function(field) {
                return fieldSet.has(field.toLowerCase());
            });
            
            if (suggestedMatches.length > 0) {
                score += (suggestedMatches.length / suggestedFields.length) * 30;
            }
            
            return {
                template: template,
                score: score,
                requiredMatches: requiredMatches,
                suggestedMatches: suggestedMatches
            };
        }).filter(function(item) {
            return item.score > 0; // 只返回有匹配的模板
        }).sort(function(a, b) {
            return b.score - a.score; // 按分数降序排列
        });
        
        return recommendations.slice(0, 5); // 返回前5个推荐
    };

    /**
     * 保存用户模板
     */
    TemplateManager.prototype.saveUserTemplate = function(config, metadata) {
        const template = {
            id: 'user_' + SmartOffice.Utils.Helpers.generateId(),
            name: metadata.name || config.name || '自定义模板',
            description: metadata.description || config.description || '',
            category: metadata.category || 'custom',
            categoryName: metadata.categoryName || '自定义',
            icon: metadata.icon || '⚙️',
            isBuiltin: false,
            isUser: true,
            createdAt: new Date().toISOString(),
            config: config,
            requiredFields: this.extractRequiredFields(config),
            suggestedFields: this.extractSuggestedFields(config)
        };
        
        // 验证模板
        if (!this.validateTemplate(template)) {
            throw new Error('模板数据无效');
        }
        
        // 添加到模板列表
        this.templates.push(template);
        
        // 保存到存储
        this.saveUserTemplates();
        
        // 更新分类
        this.extractCategories();
        
        // 触发事件
        this.eventBus.emit('template:saved', { template: template });
        
        console.log('✅ 用户模板已保存:', template.name);
        return template;
    };

    /**
     * 删除用户模板
     */
    TemplateManager.prototype.deleteUserTemplate = function(id) {
        const index = this.templates.findIndex(function(template) {
            return template.id === id && template.isUser;
        });
        
        if (index === -1) {
            throw new Error('模板不存在或不可删除');
        }
        
        const template = this.templates[index];
        this.templates.splice(index, 1);
        
        // 保存到存储
        this.saveUserTemplates();
        
        // 更新分类
        this.extractCategories();
        
        // 触发事件
        this.eventBus.emit('template:deleted', { template: template });
        
        console.log('✅ 用户模板已删除:', template.name);
        return template;
    };

    /**
     * 应用模板到配置
     */
    TemplateManager.prototype.applyTemplate = function(templateId, availableFields) {
        const template = this.getTemplateById(templateId);
        if (!template) {
            throw new Error('模板不存在');
        }
        
        // 克隆模板配置
        const config = SmartOffice.Utils.Helpers.deepClone(template.config);
        
        // 如果提供了可用字段，则验证和调整配置
        if (availableFields && availableFields.length > 0) {
            config.availableFields = availableFields;
            this.adjustConfigFields(config, availableFields);
        }
        
        // 触发事件
        this.eventBus.emit('template:applied', { 
            template: template, 
            config: config 
        });
        
        console.log('✅ 模板已应用:', template.name);
        return config;
    };

    /**
     * 调整配置字段以匹配可用字段
     */
    TemplateManager.prototype.adjustConfigFields = function(config, availableFields) {
        const fieldMap = new Map();
        availableFields.forEach(function(field) {
            fieldMap.set(field.toLowerCase(), field);
        });
        
        // 调整行字段
        config.rowFields = this.mapFields(config.rowFields, fieldMap);
        
        // 调整列字段
        config.columnFields = this.mapFields(config.columnFields, fieldMap);
        
        // 调整值字段
        config.valueFields = this.mapFields(config.valueFields, fieldMap);
        
        // 调整筛选字段
        config.filterFields = this.mapFields(config.filterFields, fieldMap);
    };

    /**
     * 映射字段名称
     */
    TemplateManager.prototype.mapFields = function(templateFields, fieldMap) {
        if (!templateFields || !Array.isArray(templateFields)) {
            return [];
        }
        
        return templateFields.map(function(field) {
            const lowerField = field.toLowerCase();
            return fieldMap.get(lowerField) || null;
        }).filter(function(field) {
            return field !== null;
        });
    };

    /**
     * 提取必需字段
     */
    TemplateManager.prototype.extractRequiredFields = function(config) {
        const fields = [];
        
        if (config.rowFields && config.rowFields.length > 0) {
            fields.push(config.rowFields[0]); // 第一个行字段是必需的
        }
        
        if (config.valueFields && config.valueFields.length > 0) {
            fields.push(config.valueFields[0]); // 第一个值字段是必需的
        }
        
        return fields;
    };

    /**
     * 提取建议字段
     */
    TemplateManager.prototype.extractSuggestedFields = function(config) {
        const fields = [];
        
        // 添加所有配置中的字段
        ['rowFields', 'columnFields', 'valueFields', 'filterFields'].forEach(function(fieldType) {
            if (config[fieldType] && Array.isArray(config[fieldType])) {
                fields.push.apply(fields, config[fieldType]);
            }
        });
        
        // 去重
        return Array.from(new Set(fields));
    };

    /**
     * 验证模板数据
     */
    TemplateManager.prototype.validateTemplate = function(template) {
        // 基本字段验证
        if (!template.id || !template.name || !template.config) {
            return false;
        }
        
        // 配置验证
        const config = template.config;
        if (!config.name) {
            return false;
        }
        
        return true;
    };

    /**
     * 保存用户模板到存储
     */
    TemplateManager.prototype.saveUserTemplates = function() {
        const userTemplates = this.templates.filter(function(template) {
            return template.isUser;
        });
        
        try {
            this.storage.set(this.STORAGE_KEY, userTemplates);
        } catch (error) {
            console.error('❌ 保存用户模板失败:', error);
            throw error;
        }
    };

    /**
     * 导出模板
     */
    TemplateManager.prototype.exportTemplate = function(id) {
        const template = this.getTemplateById(id);
        if (!template) {
            throw new Error('模板不存在');
        }
        
        // 创建导出数据
        const exportData = {
            version: '1.0',
            template: SmartOffice.Utils.Helpers.deepClone(template),
            exportedAt: new Date().toISOString()
        };
        
        // 移除内部ID（导入时会重新生成）
        delete exportData.template.id;
        exportData.template.isUser = true;
        
        return exportData;
    };

    /**
     * 导入模板
     */
    TemplateManager.prototype.importTemplate = function(exportData) {
        if (!exportData || !exportData.template) {
            throw new Error('导入数据无效');
        }
        
        const template = exportData.template;
        
        // 生成新ID
        template.id = 'user_' + SmartOffice.Utils.Helpers.generateId();
        template.isUser = true;
        template.importedAt = new Date().toISOString();
        
        // 验证模板
        if (!this.validateTemplate(template)) {
            throw new Error('导入的模板数据无效');
        }
        
        // 检查名称冲突
        const existingTemplate = this.templates.find(function(t) {
            return t.name === template.name;
        });
        
        if (existingTemplate) {
            template.name += ' (导入)';
        }
        
        // 添加到模板列表
        this.templates.push(template);
        
        // 保存到存储
        this.saveUserTemplates();
        
        // 更新分类
        this.extractCategories();
        
        // 触发事件
        this.eventBus.emit('template:imported', { template: template });
        
        console.log('✅ 模板已导入:', template.name);
        return template;
    };

    // 注册到全局命名空间
    SmartOffice.Templates = SmartOffice.Templates || {};
    SmartOffice.Templates.TemplateManager = TemplateManager;

    console.log('📋 SmartOffice模板管理器已加载');

})();
