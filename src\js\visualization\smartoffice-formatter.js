/**
 * SmartOffice条件格式化器
 * 提供数据驱动的条件格式化和样式应用功能
 * @function Formatter - 条件格式化和样式管理
 */

(function() {
    'use strict';

    /**
     * 条件格式化器构造函数
     * @function Formatter - 创建条件格式化器实例
     */
    function Formatter() {
        this.eventBus = SmartOffice.Core.EventBus;
        this.rules = [];
        this.styleCache = new Map();
        
        // 预定义的颜色方案
        this.colorSchemes = {
            traffic: {
                name: '交通灯',
                colors: ['#ff4444', '#ffaa00', '#44ff44']
            },
            heatmap: {
                name: '热力图',
                colors: ['#ffffff', '#ffeeee', '#ffcccc', '#ff9999', '#ff6666', '#ff3333', '#ff0000']
            },
            blue: {
                name: '蓝色渐变',
                colors: ['#f0f8ff', '#e6f3ff', '#ccddff', '#99bbff', '#6699ff', '#3377ff', '#0055ff']
            },
            green: {
                name: '绿色渐变',
                colors: ['#f0fff0', '#e6ffe6', '#ccffcc', '#99ff99', '#66ff66', '#33ff33', '#00ff00']
            }
        };
        
        console.log('🎨 条件格式化器: 初始化完成');
    }

    /**
     * 添加格式化规则
     * @function addRule - 添加新的条件格式化规则
     * @param {Object} rule - 格式化规则对象
     * @returns {string} 规则ID
     */
    Formatter.prototype.addRule = function(rule) {
        const ruleId = SmartOffice.Utils.Helpers.generateId();
        
        const ruleObj = {
            id: ruleId,
            name: rule.name || '未命名规则',
            field: rule.field,
            condition: rule.condition,
            style: rule.style,
            priority: rule.priority || 0,
            enabled: rule.enabled !== false,
            createdAt: new Date().toISOString()
        };
        
        // 验证规则
        if (!this.validateRule(ruleObj)) {
            throw new Error('无效的格式化规则');
        }
        
        this.rules.push(ruleObj);
        this.sortRulesByPriority();
        
        this.eventBus.emit('formatter:rule:added', { rule: ruleObj });
        console.log('✅ 条件格式化器: 已添加规则', ruleObj.name);
        
        return ruleId;
    };

    /**
     * 移除格式化规则
     * @function removeRule - 移除指定的格式化规则
     * @param {string} ruleId - 规则ID
     * @returns {boolean} 是否成功移除
     */
    Formatter.prototype.removeRule = function(ruleId) {
        const index = this.rules.findIndex(function(rule) {
            return rule.id === ruleId;
        });
        
        if (index === -1) {
            return false;
        }
        
        const removedRule = this.rules.splice(index, 1)[0];
        
        this.eventBus.emit('formatter:rule:removed', { rule: removedRule });
        console.log('✅ 条件格式化器: 已移除规则', removedRule.name);
        
        return true;
    };

    /**
     * 应用条件格式化到数据
     * @function applyFormatting - 对数据应用条件格式化
     * @param {Array} data - 数据数组
     * @param {Array} fields - 字段列表
     * @returns {Object} 格式化结果对象
     */
    Formatter.prototype.applyFormatting = function(data, fields) {
        if (!data || !Array.isArray(data)) {
            return { data: [], styles: {} };
        }
        
        const formattedData = data.slice(); // 创建副本
        const styles = {};
        
        // 为每行数据应用格式化
        formattedData.forEach(function(row, rowIndex) {
            fields.forEach(function(field) {
                const cellKey = rowIndex + '_' + field;
                const cellValue = row[field];
                
                // 应用所有匹配的规则
                const appliedStyles = this.applyCellFormatting(cellValue, field, row);
                
                if (appliedStyles.length > 0) {
                    styles[cellKey] = this.mergeStyles(appliedStyles);
                }
            }.bind(this));
        }.bind(this));
        
        console.log('🎨 条件格式化器: 格式化完成，应用了', Object.keys(styles).length, '个样式');
        
        return {
            data: formattedData,
            styles: styles
        };
    };

    /**
     * 应用单元格格式化
     * @function applyCellFormatting - 对单个单元格应用格式化
     * @param {*} cellValue - 单元格值
     * @param {string} field - 字段名
     * @param {Object} row - 行数据
     * @returns {Array} 应用的样式数组
     */
    Formatter.prototype.applyCellFormatting = function(cellValue, field, row) {
        const appliedStyles = [];
        
        this.rules.forEach(function(rule) {
            if (!rule.enabled) {
                return;
            }
            
            // 检查字段匹配
            if (rule.field !== field && rule.field !== '*') {
                return;
            }
            
            // 评估条件
            if (this.evaluateCondition(cellValue, rule.condition, row)) {
                appliedStyles.push({
                    priority: rule.priority,
                    style: rule.style
                });
            }
        }.bind(this));
        
        // 按优先级排序
        appliedStyles.sort(function(a, b) {
            return b.priority - a.priority;
        });
        
        return appliedStyles;
    };

    /**
     * 评估格式化条件
     * @function evaluateCondition - 评估格式化条件
     * @param {*} value - 单元格值
     * @param {Object} condition - 条件对象
     * @param {Object} row - 行数据
     * @returns {boolean} 是否满足条件
     */
    Formatter.prototype.evaluateCondition = function(value, condition, row) {
        switch (condition.type) {
            case 'value_range':
                return this.evaluateValueRange(value, condition);
                
            case 'percentile':
                return this.evaluatePercentile(value, condition, row);
                
            case 'text_contains':
                return this.evaluateTextContains(value, condition);
                
            case 'custom_formula':
                return this.evaluateCustomFormula(value, condition, row);
                
            default:
                return false;
        }
    };

    /**
     * 评估数值范围条件
     * @function evaluateValueRange - 评估数值范围
     * @param {*} value - 值
     * @param {Object} condition - 条件
     * @returns {boolean} 是否满足条件
     */
    Formatter.prototype.evaluateValueRange = function(value, condition) {
        const numValue = parseFloat(value);
        if (isNaN(numValue)) {
            return false;
        }
        
        const min = condition.min !== undefined ? parseFloat(condition.min) : -Infinity;
        const max = condition.max !== undefined ? parseFloat(condition.max) : Infinity;
        
        return numValue >= min && numValue <= max;
    };

    /**
     * 评估百分位条件
     * @function evaluatePercentile - 评估百分位条件
     * @param {*} value - 值
     * @param {Object} condition - 条件
     * @param {Object} row - 行数据（用于获取字段的所有值）
     * @returns {boolean} 是否满足条件
     */
    Formatter.prototype.evaluatePercentile = function(value, condition, row) {
        // 这里需要访问整个数据集来计算百分位
        // 简化实现，实际应用中需要传入完整数据集
        const numValue = parseFloat(value);
        if (isNaN(numValue)) {
            return false;
        }
        
        // 简化的百分位判断
        return condition.percentile === 'top10' ? numValue > 1000 :
               condition.percentile === 'bottom10' ? numValue < 100 :
               false;
    };

    /**
     * 评估文本包含条件
     * @function evaluateTextContains - 评估文本包含条件
     * @param {*} value - 值
     * @param {Object} condition - 条件
     * @returns {boolean} 是否满足条件
     */
    Formatter.prototype.evaluateTextContains = function(value, condition) {
        const strValue = String(value).toLowerCase();
        const searchText = String(condition.text).toLowerCase();
        
        return strValue.includes(searchText);
    };

    /**
     * 评估自定义公式条件
     * @function evaluateCustomFormula - 评估自定义公式
     * @param {*} value - 值
     * @param {Object} condition - 条件
     * @param {Object} row - 行数据
     * @returns {boolean} 是否满足条件
     */
    Formatter.prototype.evaluateCustomFormula = function(value, condition, row) {
        // 简化的公式评估，实际实现需要更复杂的表达式解析
        try {
            const formula = condition.formula;
            // 替换变量
            const evaluatedFormula = formula
                .replace(/\$value/g, parseFloat(value) || 0)
                .replace(/\$(\w+)/g, function(match, fieldName) {
                    return parseFloat(row[fieldName]) || 0;
                });
            
            // 简单的数学表达式评估（仅支持基本运算）
            return eval(evaluatedFormula);
        } catch (error) {
            console.warn('公式评估失败:', error);
            return false;
        }
    };

    /**
     * 合并多个样式
     * @function mergeStyles - 合并样式对象
     * @param {Array} styleArray - 样式数组
     * @returns {Object} 合并后的样式对象
     */
    Formatter.prototype.mergeStyles = function(styleArray) {
        const mergedStyle = {};
        
        styleArray.forEach(function(styleItem) {
            Object.assign(mergedStyle, styleItem.style);
        });
        
        return mergedStyle;
    };

    /**
     * 创建数值范围规则
     * @function createValueRangeRule - 创建数值范围格式化规则
     * @param {string} field - 字段名
     * @param {Array} ranges - 范围数组
     * @param {string} colorScheme - 颜色方案
     * @returns {Array} 规则数组
     */
    Formatter.prototype.createValueRangeRule = function(field, ranges, colorScheme) {
        const scheme = this.colorSchemes[colorScheme] || this.colorSchemes.heatmap;
        const rules = [];
        
        ranges.forEach(function(range, index) {
            const color = scheme.colors[index % scheme.colors.length];
            
            const ruleId = this.addRule({
                name: field + ' 范围 ' + (index + 1),
                field: field,
                condition: {
                    type: 'value_range',
                    min: range.min,
                    max: range.max
                },
                style: {
                    backgroundColor: color,
                    color: this.getContrastColor(color)
                },
                priority: 100 - index
            });
            
            rules.push(ruleId);
        }.bind(this));
        
        return rules;
    };

    /**
     * 获取对比色
     * @function getContrastColor - 获取背景色的对比文字颜色
     * @param {string} backgroundColor - 背景颜色
     * @returns {string} 对比文字颜色
     */
    Formatter.prototype.getContrastColor = function(backgroundColor) {
        // 简化的对比色计算
        const hex = backgroundColor.replace('#', '');
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);
        
        // 计算亮度
        const brightness = (r * 299 + g * 587 + b * 114) / 1000;
        
        return brightness > 128 ? '#000000' : '#ffffff';
    };

    /**
     * 按优先级排序规则
     * @function sortRulesByPriority - 按优先级排序规则
     */
    Formatter.prototype.sortRulesByPriority = function() {
        this.rules.sort(function(a, b) {
            return b.priority - a.priority;
        });
    };

    /**
     * 验证格式化规则
     * @function validateRule - 验证格式化规则
     * @param {Object} rule - 规则对象
     * @returns {boolean} 是否有效
     */
    Formatter.prototype.validateRule = function(rule) {
        if (!rule.field || !rule.condition || !rule.style) {
            return false;
        }
        
        const validConditionTypes = ['value_range', 'percentile', 'text_contains', 'custom_formula'];
        if (!validConditionTypes.includes(rule.condition.type)) {
            return false;
        }
        
        return true;
    };

    /**
     * 获取所有规则
     * @function getRules - 获取所有格式化规则
     * @returns {Array} 规则数组
     */
    Formatter.prototype.getRules = function() {
        return this.rules.slice();
    };

    /**
     * 获取颜色方案
     * @function getColorSchemes - 获取可用的颜色方案
     * @returns {Object} 颜色方案对象
     */
    Formatter.prototype.getColorSchemes = function() {
        return Object.assign({}, this.colorSchemes);
    };

    // 注册到全局命名空间
    if (!SmartOffice.Visualization) {
        SmartOffice.Visualization = {};
    }
    SmartOffice.Visualization.Formatter = Formatter;

    console.log('🎨 条件格式化器: 模块加载完成');

})();
