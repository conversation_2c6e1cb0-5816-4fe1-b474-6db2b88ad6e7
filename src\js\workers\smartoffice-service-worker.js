/**
 * SmartOffice Service Worker - 离线功能支持
 * 提供应用缓存、离线使用、数据同步等功能
 */

// Service Worker版本和缓存名称
const CACHE_VERSION = 'smartoffice-v1.0.0';
const STATIC_CACHE = `${CACHE_VERSION}-static`;
const DATA_CACHE = `${CACHE_VERSION}-data`;
const RUNTIME_CACHE = `${CACHE_VERSION}-runtime`;

// 需要缓存的静态资源
const STATIC_FILES = [
    '/',
    '/index.html',
    '/src/css/main.css',
    '/src/css/ios-theme.css',
    '/src/css/mobile.css',
    '/src/css/components/config-list.css',
    '/src/css/components/config-form.css',
    '/src/css/components/file-upload.css',
    '/src/css/components/dropdown.css',
    '/src/css/components/data-table.css',
    '/src/css/components/toast.css',
    
    // JavaScript核心文件
    '/src/js/core/smartoffice-core.js',
    '/src/js/core/smartoffice-events.js',
    '/src/js/core/smartoffice-storage.js',
    '/src/js/core/smartoffice-router.js',
    '/src/js/core/smartoffice-app.js',
    
    // 工具函数
    '/src/js/utils/smartoffice-helpers.js',
    '/src/js/utils/smartoffice-dom.js',
    '/src/js/utils/smartoffice-format.js',
    
    // 数据处理模块
    '/src/js/data/smartoffice-data-validator.js',
    '/src/js/data/smartoffice-csv-parser.js',
    '/src/js/data/smartoffice-config-manager.js',
    '/src/js/data/smartoffice-pivot-engine.js',
    
    // UI组件
    '/src/js/components/smartoffice-loading.js',
    '/src/js/components/smartoffice-toast.js',
    '/src/js/components/smartoffice-dropdown.js',
    '/src/js/components/smartoffice-field-selector.js',
    '/src/js/components/smartoffice-file-upload.js',
    '/src/js/components/smartoffice-data-preview.js',
    '/src/js/components/smartoffice-data-table.js',
    '/src/js/components/smartoffice-config-form.js',
    '/src/js/components/smartoffice-config-list.js'
];

/**
 * Service Worker安装事件
 * 缓存应用的静态资源
 */
self.addEventListener('install', function(event) {
    console.log('🔧 Service Worker: 安装中...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(function(cache) {
                console.log('📦 Service Worker: 缓存静态资源');
                return cache.addAll(STATIC_FILES);
            })
            .then(function() {
                console.log('✅ Service Worker: 安装完成');
                return self.skipWaiting();
            })
            .catch(function(error) {
                console.error('❌ Service Worker: 安装失败', error);
            })
    );
});

/**
 * Service Worker激活事件
 * 清理旧版本缓存
 */
self.addEventListener('activate', function(event) {
    console.log('🚀 Service Worker: 激活中...');
    
    event.waitUntil(
        caches.keys()
            .then(function(cacheNames) {
                return Promise.all(
                    cacheNames.map(function(cacheName) {
                        if (cacheName !== STATIC_CACHE && 
                            cacheName !== DATA_CACHE && 
                            cacheName !== RUNTIME_CACHE) {
                            console.log('🗑️ Service Worker: 删除旧缓存', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(function() {
                console.log('✅ Service Worker: 激活完成');
                return self.clients.claim();
            })
    );
});

/**
 * 网络请求拦截
 * 实现缓存优先的离线策略
 */
self.addEventListener('fetch', function(event) {
    // 只处理GET请求
    if (event.request.method !== 'GET') {
        return;
    }
    
    const url = new URL(event.request.url);
    
    // 处理应用静态资源
    if (url.origin === location.origin) {
        event.respondWith(handleStaticRequest(event.request));
    }
    
    // 处理数据API请求（如果有的话）
    if (url.pathname.startsWith('/api/')) {
        event.respondWith(handleDataRequest(event.request));
    }
});

/**
 * 处理静态资源请求
 * 缓存优先策略
 */
function handleStaticRequest(request) {
    return caches.match(request)
        .then(function(cachedResponse) {
            if (cachedResponse) {
                console.log('📦 从缓存加载:', request.url);
                return cachedResponse;
            }
            
            // 缓存中没有，尝试网络请求
            return fetch(request)
                .then(function(networkResponse) {
                    // 只缓存成功的响应
                    if (networkResponse.status === 200) {
                        const responseClone = networkResponse.clone();
                        caches.open(STATIC_CACHE)
                            .then(function(cache) {
                                cache.put(request, responseClone);
                            });
                    }
                    return networkResponse;
                })
                .catch(function(error) {
                    console.log('🔌 网络请求失败，应用离线模式:', request.url);
                    
                    // 返回离线页面或默认响应
                    if (request.destination === 'document') {
                        return caches.match('/index.html');
                    }
                    
                    throw error;
                });
        });
}

/**
 * 处理数据API请求
 * 网络优先，失败时使用缓存
 */
function handleDataRequest(request) {
    return caches.open(DATA_CACHE)
        .then(function(cache) {
            return fetch(request)
                .then(function(networkResponse) {
                    // 缓存最新数据
                    if (networkResponse.status === 200) {
                        cache.put(request, networkResponse.clone());
                    }
                    return networkResponse;
                })
                .catch(function(error) {
                    console.log('🔌 API请求失败，使用缓存数据:', request.url);
                    return cache.match(request)
                        .then(function(cachedResponse) {
                            if (cachedResponse) {
                                return cachedResponse;
                            }
                            throw error;
                        });
                });
        });
}

/**
 * 后台同步事件
 * 处理离线期间的数据同步
 */
self.addEventListener('sync', function(event) {
    console.log('🔄 Service Worker: 后台同步', event.tag);
    
    if (event.tag === 'smartoffice-data-sync') {
        event.waitUntil(syncOfflineData());
    }
});

/**
 * 同步离线数据
 * 将离线期间的配置更改同步到服务器
 */
function syncOfflineData() {
    return self.registration.showNotification('SmartOffice', {
        body: '正在同步您的离线数据...',
        icon: '/src/assets/icons/favicon-32x32.png',
        badge: '/src/assets/icons/favicon-16x16.png',
        tag: 'sync-notification'
    })
    .then(function() {
        // 这里可以实现具体的数据同步逻辑
        console.log('✅ 离线数据同步完成');
        
        // 更新同步状态通知
        return self.registration.showNotification('SmartOffice', {
            body: '离线数据同步完成',
            icon: '/src/assets/icons/favicon-32x32.png',
            badge: '/src/assets/icons/favicon-16x16.png',
            tag: 'sync-complete'
        });
    })
    .catch(function(error) {
        console.error('❌ 离线数据同步失败:', error);
    });
}

/**
 * 推送消息事件
 * 处理应用更新通知
 */
self.addEventListener('push', function(event) {
    if (!event.data) {
        return;
    }
    
    const data = event.data.json();
    const options = {
        body: data.body,
        icon: '/src/assets/icons/favicon-32x32.png',
        badge: '/src/assets/icons/favicon-16x16.png',
        data: data.data,
        requireInteraction: true
    };
    
    event.waitUntil(
        self.registration.showNotification(data.title, options)
    );
});

/**
 * 通知点击事件
 * 处理用户点击通知的操作
 */
self.addEventListener('notificationclick', function(event) {
    event.notification.close();
    
    // 打开应用
    event.waitUntil(
        self.clients.matchAll({
            type: 'window',
            includeUncontrolled: true
        })
        .then(function(clients) {
            // 如果应用已经打开，聚焦到它
            for (let client of clients) {
                if (client.url.includes(location.origin)) {
                    return client.focus();
                }
            }
            
            // 否则打开新窗口
            return self.clients.openWindow('/');
        })
    );
});

/**
 * 消息事件
 * 处理来自主线程的消息
 */
self.addEventListener('message', function(event) {
    const data = event.data;
    
    switch (data.type) {
        case 'SKIP_WAITING':
            console.log('⏩ Service Worker: 跳过等待，立即激活');
            self.skipWaiting();
            break;
            
        case 'GET_VERSION':
            event.ports[0].postMessage({
                type: 'VERSION',
                version: CACHE_VERSION
            });
            break;
            
        case 'CLEAR_CACHE':
            console.log('🗑️ Service Worker: 清理所有缓存');
            event.waitUntil(
                caches.keys()
                    .then(function(cacheNames) {
                        return Promise.all(
                            cacheNames.map(function(cacheName) {
                                return caches.delete(cacheName);
                            })
                        );
                    })
                    .then(function() {
                        event.ports[0].postMessage({
                            type: 'CACHE_CLEARED'
                        });
                    })
            );
            break;
            
        default:
            console.log('📨 Service Worker: 未知消息类型', data.type);
    }
});

console.log('🔧 Service Worker: 脚本加载完成');
